import { useDeleteOrder } from '@/src/hooks/order-hooks'
import { DateTime } from 'luxon'
import Image from 'next/image'
import Link from 'next/link'
import { FiTrash2 } from 'react-icons/fi'
import { OrderShape } from '../../../../../../src/types/order-types'
import LimitTitleLength from '../../../../../../src/components/utils/TextLimit'
import Modal from '../../../../../../src/components/utils/modal/Modal'
import { addDays } from '../../../../../../src/components/utils/utils'
import styles from './OrdersListView.module.scss'

interface Props {
  orders: OrderShape[]
}

const OrdersListView = ({ orders }: Props) => {
  const { isModalOpen, handleDeleteClick, confirmDelete, cancelDelete } =
    useDeleteOrder()

  return (
    <section className={styles.orders}>
      {orders?.map((order) => (
        <div key={order.id} className={styles.orders_list}>
          <p className={styles.order_id}>#{order.id}</p>
          <ul className={styles.orders_list__order_items}>
            {order.ordered_items.map((item) => (
              <li key={item.id} className={styles.cart__cart_item}>
                <div className={styles.cart_items_img}>
                  <Image
                    src={
                      item.product_variant?.product_image?.[0]?.image
                        ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                        : ''
                    }
                    alt={item.product.title || 'Product image'}
                    width={60}
                    height={80}
                    className={styles.cart_items_img__image}
                  />
                </div>
                <div className={styles.cart_items_info}>
                  <Link href={`/product/${item.product.slug}`}>
                    <LimitTitleLength
                      title={item.product.title}
                      maxLength={40}
                    />
                  </Link>
                  <div>
                    <p>
                      ${item.product_variant.price} ({item.quantity}{' '}
                      {item.quantity > 1 ? 'items' : 'item'})
                    </p>
                  </div>
                  {Object.entries(item.extra_data).map(
                    ([key, value], index) => (
                      <div key={index}>
                        <p>{key}:</p>
                        <p>{value}</p>
                      </div>
                    )
                  )}
                </div>
              </li>
            ))}
          </ul>
          <div className={styles.orders_list__status}>
            <section>
              <h3>Delivery Status:</h3>
              <p>{order?.order_status}</p>
            </section>
            <section>
              <h3>Payment Status:</h3>
              <p>{`${order?.payment_status} ($${order.total.toFixed(1)})`}</p>
            </section>
            <section>
              <h3>Order Placed at:</h3>
              <p>
                {DateTime.fromISO(order.placed_at).toFormat(
                  'cccc, MMM d, yyyy'
                )}
              </p>
            </section>
            <section>
              <h3>Selected Payment Method:</h3>
              <p>{order?.payment_method.name}</p>
            </section>
            <section>
              <h3>Expect Your Delivery on:</h3>
              <p>{addDays(order.placed_at, 10)}</p>
            </section>
          </div>
          <div className={styles.view_order}>
            <Link href={`/checkout/order/${order.id}`}>View Order</Link>
            {order?.payment_status === 'Pending' &&
              order?.payment_method.slug === 'stripe' && (
                <Link href={`/checkout/order/${order.id}`}>Pay Order</Link>
              )}
            <button
              title='cancel order'
              onClick={() => handleDeleteClick(order.id)}
            >
              <i>
                <FiTrash2 />
              </i>
            </button>
          </div>
        </div>
      ))}

      {/* Modal for confirming delete action */}
      <Modal
        show={isModalOpen}
        title='Are you sure you want to delete this order?'
        message="
        Please note that you can only delete an order if it's currently in a PENDING delivery state.
        If you've already paid for your order and wish to cancel it, please contact our
        customer support team for assistance with a refund.
        Kindly be aware that only a portion of your order amount may be refundable.”
        "
        onClose={cancelDelete}
        onConfirm={confirmDelete}
      />
    </section>
  )
}

export default OrdersListView
