@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.error_container {
  min-height: 60vh;
  @include flexbox(center, center);
  padding: 2rem;
}

.error_content {
  text-align: center;
  max-width: 600px;

  h1 {
    font-size: $font-size-6;
    color: $primary-red;
    margin-bottom: 1rem;
  }

  p {
    font-size: $font-size-3;
    color: $primary-dark-text-color;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  ul {
    text-align: left;
    margin: 1rem 0;
    padding-left: 1.5rem;

    li {
      margin-bottom: 0.5rem;
      color: $primary-lighter-text-color;
    }
  }
}

.error_actions {
  @include flexbox(center, center, column);
  gap: 1rem;
  margin-top: 2rem;

  @media (min-width: 480px) {
    flex-direction: row;
    justify-content: center;
  }
}

.retry_button,
.browse_button,
.home_button {
  @include btn(#fff, $primary-blue);
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  border-radius: $border-radius-2;
  transition: all 0.2s ease;
  min-width: 150px;

  &:hover {
    background-color: color.adjust($primary-blue, $lightness: -5%);
    transform: translateY(-1px);
  }
}

.browse_button {
  background-color: $primary-green;

  &:hover {
    background-color: color.adjust($primary-green, $lightness: -5%);
  }
}

.home_button {
  background-color: $primary-lighter-text-color;

  &:hover {
    background-color: color.adjust($primary-lighter-text-color, $lightness: -10%);
  }
}