// Input component styles
// Implements design system with variants and states

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;

  &.fullWidth {
    width: 100%;
  }
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
  line-height: $line-height-tight;

  .required {
    color: $error-500;
    margin-left: $spacing-1;
  }
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  @include input-base;

  &.hasLeftIcon {
    padding-left: $spacing-10;
  }

  &.hasRightIcon {
    padding-right: $spacing-10;
  }

  &.error {
    border-color: $error-500;

    &:focus {
      border-color: $error-500;
      box-shadow: 0 0 0 3px rgba($error-500, 0.1);
    }
  }

  &.filled {
    background-color: $gray-50;
    border-color: $gray-200;

    &:focus {
      background-color: white;
      border-color: $primary-500;
    }
  }

  &.fullWidth {
    width: 100%;
  }

  // Placeholder styles
  &::placeholder {
    color: $gray-400;
    opacity: 1; // Firefox
  }

  // Disabled state
  &:disabled {
    background-color: $gray-50;
    color: $gray-500;
    cursor: not-allowed;

    &::placeholder {
      color: $gray-400;
    }
  }

  // Read-only state
  &:read-only {
    background-color: $gray-50;
    cursor: default;
  }

  // Invalid state (HTML5 validation)
  &:invalid {
    border-color: $error-500;
  }

  // Valid state (HTML5 validation)
  &:valid {
    border-color: $success-500;
  }
}

// Icon positioning
.leftIcon,
.rightIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-400;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: $spacing-5;
  height: $spacing-5;

  svg {
    width: $spacing-4;
    height: $spacing-4;
  }
}

.leftIcon {
  left: $spacing-3;
}

.rightIcon {
  right: $spacing-3;
}

// Helper and error text
.errorText {
  font-size: $font-size-xs;
  color: $error-500;
  line-height: $line-height-tight;

  // Add icon for error
  &::before {
    content: '⚠';
    margin-right: $spacing-1;
  }
}

.helperText {
  font-size: $font-size-xs;
  color: $gray-500;
  line-height: $line-height-tight;
}

// Focus styles for accessibility
.input:focus-visible {
  outline: 2px solid $primary-500;
  outline-offset: 2px;
}

// Input type specific styles
input[type="search"] {

  &::-webkit-search-decoration,
  &::-webkit-search-cancel-button,
  &::-webkit-search-results-button,
  &::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }
}

input[type="number"] {

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  -moz-appearance: textfield;
}

// Password input styles
input[type="password"] {
  font-family: text-security-disc, -webkit-small-control;
}

// File input styles
input[type="file"] {
  padding: $spacing-2;

  &::file-selector-button {
    background-color: $gray-100;
    border: 1px solid $gray-300;
    border-radius: $border-radius;
    padding: $spacing-1 $spacing-3;
    margin-right: $spacing-3;
    font-size: $font-size-sm;
    cursor: pointer;

    &:hover {
      background-color: $gray-200;
    }
  }
}