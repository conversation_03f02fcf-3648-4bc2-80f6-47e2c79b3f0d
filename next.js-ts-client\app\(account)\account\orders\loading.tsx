import styles from './OrdersLoading.module.scss'

export default function OrdersLoading() {
  return (
    <div className={styles.ordersLoading}>
      <h1 className={styles.title}></h1>

      <div className={styles.filters}>
        <div className={`${styles.filterItem} ${styles.pulse}`}></div>
        <div className={`${styles.filterItem} ${styles.pulse}`}></div>
        <div className={`${styles.filterItem} ${styles.pulse}`}></div>
      </div>

      {[1, 2, 3].map((i) => (
        <div key={i} className={styles.orderCard}>
          <div className={styles.orderHeader}>
            <div className={`${styles.orderId} ${styles.pulse}`}></div>
            <div className={`${styles.orderDate} ${styles.pulse}`}></div>
            <div className={`${styles.orderStatus} ${styles.pulse}`}></div>
          </div>

          <div className={styles.orderItems}>
            {[1, 2].map((j) => (
              <div key={j} className={styles.item}>
                <div className={`${styles.itemImage} ${styles.pulse}`}></div>
                <div className={styles.itemDetails}>
                  <div className={`${styles.itemTitle} ${styles.pulse}`}></div>
                  <div className={`${styles.itemPrice} ${styles.pulse}`}></div>
                </div>
              </div>
            ))}
          </div>

          <div className={styles.orderFooter}>
            <div className={`${styles.total} ${styles.pulse}`}></div>
            <div className={`${styles.button} ${styles.pulse}`}></div>
          </div>
        </div>
      ))}
    </div>
  )
}
