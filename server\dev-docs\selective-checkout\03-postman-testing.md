# Postman Testing Guide for Selective Checkout

This guide provides comprehensive testing scenarios for the selective checkout feature using Postman.

## Environment Setup

### 1. Create Environment Variables

Create a new Postman environment with the following variables:

```
base_url: http://127.0.0.1:8000
cart_id: [Your test cart UUID]
auth_token: [Your authentication token]
item_id_1: [First cart item ID]
item_id_2: [Second cart item ID]
item_id_3: [Third cart item ID]
address_id: [Test address ID]
payment_method_id: [Test payment method ID]
```

### 2. Authentication Setup

All requests require authentication. Add this to your request headers:

```
Authorization: Bearer {{auth_token}}
Content-Type: application/json
```

## Test Collection Structure

### Collection: Selective Checkout API Tests

#### Folder 1: Cart Item Selection

1. **Toggle Item Selection**
2. **Bulk Select Items**
3. **Bulk Deselect Items**
4. **Get Selected Summary**

#### Folder 2: Shipping Calculation

1. **Calculate Shipping for Selected Items**
2. **Calculate Shipping for All Items**

#### Folder 3: Order Creation

1. **Create Order with Selected Items**
2. **Create Order with No Selection (Error Case)**

#### Folder 4: Edge Cases

1. **Invalid Item IDs**
2. **Empty Cart Selection**
3. **Mixed Selection States**

## Detailed Test Cases

### 1. Toggle Item Selection

**Request:**

```http
PATCH {{base_url}}/cart/{{cart_id}}/items/{{item_id_1}}/select/
```

**Body:**

```json
{
  "is_selected": true
}
```

**Tests:**

```javascript
pm.test('Status code is 200', function () {
  pm.response.to.have.status(200)
})

pm.test('Item is selected', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.is_selected).to.be.true
})

pm.test('Response has required fields', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData).to.have.property('id')
  pm.expect(jsonData).to.have.property('is_selected')
  pm.expect(jsonData).to.have.property('message')
})
```

### 2. Bulk Select Items

**Request:**

```
POST {{base_url}}/cart/{{cart_id}}/items/bulk-select/
```

**Body:**

```json
{
  "item_ids": [{{item_id_1}}, {{item_id_2}}, {{item_id_3}}]
}
```

**Tests:**

```javascript
pm.test('Status code is 200', function () {
  pm.response.to.have.status(200)
})

pm.test('Bulk selection successful', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.success).to.be.true
  pm.expect(jsonData.updated_count).to.equal(3)
})
```

### 3. Get Selected Summary

**Request:**

```
GET {{base_url}}/cart/{{cart_id}}/selected-summary/
```

**Tests:**

```javascript
pm.test('Status code is 200', function () {
  pm.response.to.have.status(200)
})

pm.test('Summary has correct structure', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData).to.have.property('selected_items_count')
  pm.expect(jsonData).to.have.property('total_items_count')
  pm.expect(jsonData).to.have.property('selected_total_price')
  pm.expect(jsonData).to.have.property('selected_total_weight')
  pm.expect(jsonData).to.have.property('all_items_selected')
  pm.expect(jsonData).to.have.property('selected_item_ids')
})

pm.test('Selected count is positive', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.selected_items_count).to.be.above(0)
})
```

### 4. Calculate Shipping for Selected Items

**Request:**

```
POST {{base_url}}/cart/{{cart_id}}/calculate-shipping/
```

**Body:**

```json
{
  "destination_address_id": {{address_id}},
  "get_all_options": false,
  "selected_only": true
}
```

**Tests:**

```javascript
pm.test('Status code is 200', function () {
  pm.response.to.have.status(200)
})

pm.test('Shipping calculation successful', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.shipping_calculation.success).to.be.true
  pm.expect(jsonData.cart.selected_shipping_cost).to.exist
})

pm.test('Selected totals are present', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.cart.selected_total_price).to.exist
  pm.expect(jsonData.cart.selected_grand_total).to.exist
  pm.expect(jsonData.cart.selected_cart_weight).to.exist
})
```

### 5. Create Order with Selected Items

**Request:**

```
POST {{base_url}}/orders/
```

**Body:**

```json
{
  "cart_id": "{{cart_id}}",
  "selected_address": {{address_id}},
  "payment_method": {{payment_method_id}},
  "order_status": "Pending",
  "selected_cart_item_ids": [{{item_id_1}}, {{item_id_2}}]
}
```

**Tests:**

```javascript
pm.test('Order created successfully', function () {
  pm.response.to.have.status(201)
})

pm.test('Order has cart information', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData).to.have.property('remaining_cart_items')
  pm.expect(jsonData).to.have.property('cart_still_exists')
  pm.expect(jsonData.id).to.exist

  // Store order ID for cleanup
  pm.environment.set('created_order_id', jsonData.id)
})

pm.test('Order contains only selected items', function () {
  var jsonData = pm.response.json()
  var selectedIds = [
    parseInt(pm.environment.get('item_id_1')),
    parseInt(pm.environment.get('item_id_2')),
  ]

  pm.expect(jsonData.ordered_items).to.have.lengthOf(selectedIds.length)
})
```

## Error Case Testing

### 1. Invalid Item IDs

**Request:**

```
POST {{base_url}}/cart/{{cart_id}}/items/bulk-select/
```

**Body:**

```json
{
  "item_ids": [99999, 88888]
}
```

**Tests:**

```javascript
pm.test('Status code is 400', function () {
  pm.response.to.have.status(400)
})

pm.test('Error message is descriptive', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.error).to.exist
})
```

### 2. No Items Selected for Order

**Request:**

```
POST {{base_url}}/orders/
```

**Body:**

```json
{
  "cart_id": "{{cart_id}}",
  "selected_address": {{address_id}},
  "payment_method": {{payment_method_id}},
  "order_status": "Pending"
}
```

**Tests:**

```javascript
pm.test('Status code is 400', function () {
  pm.response.to.have.status(400)
})

pm.test('Validation error for no selection', function () {
  var jsonData = pm.response.json()
  pm.expect(jsonData.selected_cart_item_ids).to.exist
})
```

## Test Execution Order

Run tests in this sequence for best results:

1. **Setup**: Ensure cart has multiple items
2. **Selection Tests**: Toggle and bulk operations
3. **Summary Tests**: Verify selection state
4. **Shipping Tests**: Calculate costs for selected items
5. **Order Tests**: Create order with selected items
6. **Cleanup**: Reset cart state if needed

## Performance Testing

### Load Testing Scenarios

1. **Bulk Selection**: Select/deselect 100+ items
2. **Concurrent Selection**: Multiple users selecting items simultaneously
3. **Shipping Calculation**: Calculate shipping for large selections

### Monitoring Points

- Response times for bulk operations
- Database query performance
- Memory usage during large selections
- API rate limiting behavior

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify token is valid and not expired
2. **Cart Not Found**: Ensure cart_id exists and belongs to authenticated user
3. **Item Not Found**: Verify item IDs exist in the specified cart
4. **Selection State Mismatch**: Check frontend-backend synchronization

### Debug Tips

1. Use Postman Console to view request/response details
2. Check server logs for detailed error messages
3. Verify database state using Django admin or direct queries
4. Test with minimal data set first, then scale up
