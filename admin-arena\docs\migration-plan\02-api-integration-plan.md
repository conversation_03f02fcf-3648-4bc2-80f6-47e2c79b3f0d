# API Integration Technical Plan

## Overview

This document outlines the detailed implementation plan for API integration using TanStack Query, following the patterns established in the react-ts-client application.

## Package Versions

```json
{
  "@tanstack/react-query": "^5.35.1",
  "@tanstack/react-query-devtools": "^5.35.1",
  "@tanstack/query-sync-storage-persister": "^5.79.0",
  "@tanstack/react-query-persist-client": "^5.79.0",
  "axios": "^1.10.0"
}
```

## API Client Architecture

### 1. Base API Client (Enhanced from react-ts-client)

```typescript
// src/services/api-client.ts
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { AuthStorage } from '../utils/auth-storage';

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.get<TResponse>(this.endpoint, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  getAll = async (config?: AxiosRequestConfig): Promise<PaginatedResponse<TResponse>> => {
    try {
      const response = await apiClient.get<PaginatedResponse<TResponse>>(this.endpoint, config);
      console.log(`API Call: ${response.config.url}`);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.post<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.patch<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  put = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.put<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  delete = async (itemId?: number | string): Promise<void> => {
    try {
      await apiClient.delete(`${this.endpoint}${itemId ? `/${itemId}/` : ''}`);
    } catch (error) {
      this.handleError(error);
      throw new Error((error as AxiosError).message);
    }
  };

  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      // Don't log cancelled requests as errors
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        throw error;
      }

      // Don't log expected authentication errors to reduce console noise
      const isAuthError = error.response?.status === 401 || error.response?.status === 403;

      if (!isAuthError) {
        console.error('API Error:', error.message);
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
      }

      throw {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      };
    }
    
    console.error('Unexpected error:', error);
    throw error;
  };
}

export default APIClient;
```

### 2. Query Keys Factory

```typescript
// src/services/query-keys.ts
export const queryKeys = {
  // Authentication
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    permissions: () => [...queryKeys.auth.all, 'permissions'] as const,
  },

  // Orders
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (filters: OrderFilters) => [...queryKeys.orders.lists(), filters] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.orders.details(), id] as const,
    assignments: () => [...queryKeys.orders.all, 'assignments'] as const,
    myAssignments: () => [...queryKeys.orders.assignments(), 'my'] as const,
    bulkOperations: () => [...queryKeys.orders.all, 'bulk-operations'] as const,
    documents: () => [...queryKeys.orders.all, 'documents'] as const,
    notes: (orderId: number) => [...queryKeys.orders.detail(orderId), 'notes'] as const,
    statusHistory: (orderId: number) => [...queryKeys.orders.detail(orderId), 'status-history'] as const,
    dashboardStats: () => [...queryKeys.orders.all, 'dashboard-stats'] as const,
  },

  // Products
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (filters: ProductFilters) => [...queryKeys.products.lists(), filters] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.products.details(), id] as const,
    categories: () => [...queryKeys.products.all, 'categories'] as const,
    categoryTree: () => [...queryKeys.products.categories(), 'tree'] as const,
    brands: () => [...queryKeys.products.all, 'brands'] as const,
    attributes: () => [...queryKeys.products.all, 'attributes'] as const,
    attributeValues: () => [...queryKeys.products.all, 'attribute-values'] as const,
    variants: (productId: number) => [...queryKeys.products.detail(productId), 'variants'] as const,
    images: (productId: number) => [...queryKeys.products.detail(productId), 'images'] as const,
    analytics: () => [...queryKeys.products.all, 'analytics'] as const,
    bulkOperations: () => [...queryKeys.products.all, 'bulk-operations'] as const,
  },

  // Customers
  customers: {
    all: ['customers'] as const,
    lists: () => [...queryKeys.customers.all, 'list'] as const,
    list: (filters: CustomerFilters) => [...queryKeys.customers.lists(), filters] as const,
    details: () => [...queryKeys.customers.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.customers.details(), id] as const,
    addresses: (customerId: number) => [...queryKeys.customers.detail(customerId), 'addresses'] as const,
    activity: (customerId: number) => [...queryKeys.customers.detail(customerId), 'activity'] as const,
    supportHistory: (customerId: number) => [...queryKeys.customers.detail(customerId), 'support-history'] as const,
    analytics: () => [...queryKeys.customers.all, 'analytics'] as const,
    segments: () => [...queryKeys.customers.all, 'segments'] as const,
  },

  // Staff
  staff: {
    all: ['staff'] as const,
    profiles: () => [...queryKeys.staff.all, 'profiles'] as const,
    profile: (id: number) => [...queryKeys.staff.profiles(), id] as const,
    roles: () => [...queryKeys.staff.all, 'roles'] as const,
    role: (id: number) => [...queryKeys.staff.roles(), id] as const,
    permissions: () => [...queryKeys.staff.all, 'permissions'] as const,
    departmentSummary: () => [...queryKeys.staff.all, 'department-summary'] as const,
  },

  // Analytics
  analytics: {
    all: ['analytics'] as const,
    dashboard: () => [...queryKeys.analytics.all, 'dashboard'] as const,
    sales: () => [...queryKeys.analytics.all, 'sales'] as const,
    performance: () => [...queryKeys.analytics.all, 'performance'] as const,
  },
} as const;
```

### 3. Type Definitions

```typescript
// src/types/api-types.ts
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ApiError {
  message: string;
  status?: number;
  data?: Record<string, string[]>;
}

export interface OrderFilters {
  status?: string;
  assigned_to?: number;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
  ordering?: string;
}

export interface ProductFilters {
  category?: number;
  brand?: number;
  is_active?: boolean;
  search?: string;
  price_min?: number;
  price_max?: number;
  page?: number;
  page_size?: number;
  ordering?: string;
}

export interface CustomerFilters {
  is_active?: boolean;
  search?: string;
  date_joined_from?: string;
  date_joined_to?: string;
  page?: number;
  page_size?: number;
  ordering?: string;
}

// Order Types
export interface Order {
  id: number;
  order_number: string;
  customer: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
  total_amount: number;
  created_at: string;
  updated_at: string;
  assigned_to?: StaffProfile;
  items: OrderItem[];
}

export interface OrderItem {
  id: number;
  product_variant: {
    id: number;
    product: {
      id: number;
      name: string;
      slug: string;
    };
    sku: string;
    price: number;
  };
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface OrderNote {
  id: number;
  order: number;
  staff_member: StaffProfile;
  note: string;
  is_internal: boolean;
  created_at: string;
}

// Product Types
export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  category: Category;
  brand: Brand;
  product_type: ProductType;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  variants: ProductVariant[];
  images: ProductImage[];
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  parent?: Category;
  level: number;
  is_active: boolean;
}

export interface Brand {
  id: number;
  name: string;
  slug: string;
  logo?: string;
  is_active: boolean;
}

export interface ProductVariant {
  id: number;
  product: number;
  sku: string;
  price: number;
  stock_quantity: number;
  is_active: boolean;
  attribute_values: AttributeValue[];
}

export interface AttributeValue {
  id: number;
  attribute: {
    id: number;
    name: string;
    type: string;
  };
  value: string;
  is_active: boolean;
}

// Customer Types
export interface Customer {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  is_active: boolean;
  date_joined: string;
  last_login?: string;
  addresses: Address[];
  orders_count: number;
  total_spent: number;
}

export interface Address {
  id: number;
  customer: number;
  full_name: string;
  address_line_1: string;
  address_line_2?: string;
  city_or_village: string;
  postal_code: string;
  country: string;
  is_primary: boolean;
}
```

### 4. Service Layer Implementation

```typescript
// src/services/order-service.ts
import APIClient from './api-client';
import { Order, OrderNote, OrderFilters, PaginatedResponse } from '../types/api-types';

class OrderService {
  private client = new APIClient<Order>('/staff/orders/orders');
  private notesClient = new APIClient<OrderNote>('/staff/orders/notes');

  async getOrders(filters: OrderFilters = {}): Promise<PaginatedResponse<Order>> {
    return this.client.getAll({ params: filters });
  }

  async getOrder(id: number): Promise<Order> {
    const client = new APIClient<Order>(`/staff/orders/orders/${id}`);
    return client.get();
  }

  async updateOrderStatus(id: number, data: { status: string; notes?: string }): Promise<Order> {
    const client = new APIClient<Order>(`/staff/orders/orders/${id}/update_status`);
    return client.patch(data);
  }

  async assignOrder(id: number, staffId: number): Promise<Order> {
    const client = new APIClient<Order>(`/staff/orders/orders/${id}/assign`);
    return client.post({ staff_member: staffId });
  }

  async addOrderNote(orderId: number, note: string, isInternal: boolean = false): Promise<OrderNote> {
    return this.notesClient.post({
      order: orderId,
      note,
      is_internal: isInternal
    });
  }

  async getOrderNotes(orderId: number): Promise<OrderNote[]> {
    const client = new APIClient<OrderNote[]>(`/staff/orders/orders/${orderId}/notes`);
    return client.get();
  }

  async bulkUpdateStatus(orderIds: number[], status: string): Promise<void> {
    const client = new APIClient<void>('/staff/orders/orders/bulk_update_status');
    return client.post({ order_ids: orderIds, status });
  }

  async getDashboardStats(): Promise<any> {
    const client = new APIClient<any>('/staff/orders/orders/dashboard_stats');
    return client.get();
  }

  async getMyAssignments(): Promise<PaginatedResponse<Order>> {
    const client = new APIClient<Order>('/staff/orders/orders/my_assignments');
    return client.getAll();
  }
}

export const orderService = new OrderService();
```

## TanStack Query Hooks

### 1. Order Hooks

```typescript
// src/hooks/use-orders.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { orderService } from '../services/order-service';
import { queryKeys } from '../services/query-keys';
import { OrderFilters } from '../types/api-types';

export const useOrders = (filters: OrderFilters = {}) => {
  return useQuery({
    queryKey: queryKeys.orders.list(filters),
    queryFn: () => orderService.getOrders(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useOrder = (id: number) => {
  return useQuery({
    queryKey: queryKeys.orders.detail(id),
    queryFn: () => orderService.getOrder(id),
    enabled: !!id,
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { status: string; notes?: string } }) =>
      orderService.updateOrderStatus(id, data),
    onSuccess: (updatedOrder) => {
      // Update the order detail cache
      queryClient.setQueryData(queryKeys.orders.detail(updatedOrder.id), updatedOrder);
      
      // Invalidate order lists to refetch with new status
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.dashboardStats() });
    },
  });
};

export const useAssignOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderId, staffId }: { orderId: number; staffId: number }) =>
      orderService.assignOrder(orderId, staffId),
    onSuccess: (updatedOrder) => {
      queryClient.setQueryData(queryKeys.orders.detail(updatedOrder.id), updatedOrder);
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.myAssignments() });
    },
  });
};

export const useOrderNotes = (orderId: number) => {
  return useQuery({
    queryKey: queryKeys.orders.notes(orderId),
    queryFn: () => orderService.getOrderNotes(orderId),
    enabled: !!orderId,
  });
};

export const useAddOrderNote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderId, note, isInternal }: { orderId: number; note: string; isInternal?: boolean }) =>
      orderService.addOrderNote(orderId, note, isInternal),
    onSuccess: (newNote) => {
      // Add the new note to the cache
      queryClient.setQueryData(
        queryKeys.orders.notes(newNote.order),
        (oldNotes: OrderNote[] = []) => [...oldNotes, newNote]
      );
    },
  });
};

export const useBulkUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderIds, status }: { orderIds: number[]; status: string }) =>
      orderService.bulkUpdateStatus(orderIds, status),
    onSuccess: () => {
      // Invalidate all order-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });
    },
  });
};

export const useOrderDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.orders.dashboardStats(),
    queryFn: () => orderService.getDashboardStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useMyOrderAssignments = () => {
  return useQuery({
    queryKey: queryKeys.orders.myAssignments(),
    queryFn: () => orderService.getMyAssignments(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
```

## Query Client Configuration

```typescript
// src/lib/query-client.ts
import { QueryClient } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors except 408, 429
        if (error?.status >= 400 && error?.status < 500 && ![408, 429].includes(error.status)) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
});

// Persist queries to localStorage
const localStoragePersister = createSyncStoragePersister({
  storage: window.localStorage,
  key: 'admin-arena-cache',
});

persistQueryClient({
  queryClient,
  persister: localStoragePersister,
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
});
```

## Next Steps

1. **Service Implementation**: Create service classes for all API endpoints
2. **Hook Development**: Build TanStack Query hooks for each service
3. **Error Handling**: Implement comprehensive error handling and user feedback
4. **Caching Strategy**: Optimize cache invalidation and data synchronization
5. **Testing**: Write unit tests for services and hooks
