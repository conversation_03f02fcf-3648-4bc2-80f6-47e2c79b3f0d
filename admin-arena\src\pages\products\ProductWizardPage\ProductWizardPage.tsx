// Multi-step product creation wizard
// Implements the complete product addition workflow as described in the documentation

import React, { useState } from 'react'
import { FiArrowLeft, FiArrowRight, FiCheck, FiPackage } from 'react-icons/fi'
import { useNavigate } from '@tanstack/react-router'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Badge } from '../../../components/ui/Badge'
import { CategoryStep } from '../../../components/products/wizard/CategoryStep'
import { ProductTypeStep } from '../../../components/products/wizard/ProductTypeStep/ProductTypeStep'
import { BrandStep } from '../../../components/products/wizard/BrandStep'
import { AttributesStep } from '../../../components/products/wizard/AttributesStep'
import { ProductStep } from '../../../components/products/wizard/ProductStep'
import { VariantsStep } from '../../../components/products/wizard/VariantsStep'
import { ReviewStep } from '../../../components/products/wizard/ReviewStep'
// import { VariantsStep } from '../../../components/products/wizard/'
// import { ReviewStep } from '../../../components/products/wizard/ReviewStep'
import styles from './ProductWizardPage.module.scss'

export interface WizardData {
  // Step 1: Category
  category?: {
    id: number
    name: string
    parent?: number
  }

  // Step 2: Product Type
  productType?: {
    id: number
    name: string
    slug: string
  }

  // Step 3: Brand
  brand?: {
    id: number
    name: string
    logo?: string
  }

  // Step 4: Attributes
  attributes?: Array<{
    id: number
    name: string
    values: Array<{
      id: number
      value: string
    }>
  }>

  // Step 5: Product
  product?: {
    name: string
    slug: string
    description: string
    is_active: boolean
  }

  // Step 6: Variants
  variants?: Array<{
    sku: string
    price: number
    compare_at_price?: number
    quantity_available: number
    is_active: boolean
    attribute_values: Array<{
      attribute_id: number
      value_id: number
    }>
    images?: File[]
  }>
}

const STEPS = [
  { id: 'category', title: 'Category', description: 'Select product category' },
  { id: 'product-type', title: 'Product Type', description: 'Choose product type' },
  { id: 'brand', title: 'Brand', description: 'Select or create brand' },
  { id: 'attributes', title: 'Attributes', description: 'Define product attributes' },
  { id: 'product', title: 'Product', description: 'Basic product information' },
  { id: 'variants', title: 'Variants', description: 'Create product variants' },
  { id: 'review', title: 'Review', description: 'Review and create product' },
] as const

export const ProductWizardPage: React.FC = () => {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [wizardData, setWizardData] = useState<WizardData>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const updateWizardData = (stepData: Partial<WizardData>) => {
    setWizardData(prev => ({ ...prev, ...stepData }))
  }

  const canProceedToNext = () => {
    switch (currentStep) {
      case 0: // Category
        return !!wizardData.category
      case 1: // Product Type
        return !!wizardData.productType
      case 2: // Brand
        return !!wizardData.brand
      case 3: // Attributes
        return wizardData.attributes && wizardData.attributes.length > 0
      case 4: // Product
        return !!wizardData.product?.name && !!wizardData.product?.description
      case 5: // Variants
        return wizardData.variants && wizardData.variants.length > 0
      default:
        return true
    }
  }

  const handleNext = () => {
    if (currentStep < STEPS.length - 1 && canProceedToNext()) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleStepClick = (stepIndex: number) => {
    // Allow going back to completed steps
    if (stepIndex < currentStep) {
      setCurrentStep(stepIndex)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // TODO: Implement product creation logic
      console.log('Creating product with data:', wizardData)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Navigate to products list on success
      navigate({ to: '/products' })
    } catch (error) {
      console.error('Failed to create product:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <CategoryStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 1:
        return (
          <ProductTypeStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 2:
        return (
          <BrandStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 3:
        return (
          <AttributesStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 4:
        return (
          <ProductStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 5:
        return (
          <VariantsStep
            data={wizardData}
            onUpdate={updateWizardData}
          />
        )
      case 6:
        return (
          <ReviewStep
            data={wizardData}
            onEdit={setCurrentStep}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            variant="ghost"
            onClick={() => navigate({ to: '/products' })}
            className={styles.backButton}
          >
            <FiArrowLeft />
            Back to Products
          </Button>

          <div className={styles.titleSection}>
            <h1 className={styles.title}>Add New Product</h1>
            <p className={styles.subtitle}>
              Follow the steps to create a complete product with variants
            </p>
          </div>
        </div>

        <div className={styles.headerIcon}>
          <FiPackage />
        </div>
      </div>

      <div className={styles.wizardContainer}>
        {/* Step Navigation */}
        <Card className={styles.stepsCard}>
          <div className={styles.steps}>
            {STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`${styles.step} ${index === currentStep ? styles.active : ''
                  } ${index < currentStep ? styles.completed : ''}`}
                onClick={() => handleStepClick(index)}
              >
                <div className={styles.stepNumber}>
                  {index < currentStep ? (
                    <FiCheck />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <div className={styles.stepContent}>
                  <h3 className={styles.stepTitle}>{step.title}</h3>
                  <p className={styles.stepDescription}>{step.description}</p>
                </div>
                {index < currentStep && (
                  <Badge variant="success" size="sm">
                    Complete
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </Card>

        {/* Step Content */}
        <Card className={styles.contentCard}>
          <div className={styles.stepHeader}>
            <h2 className={styles.stepTitle}>
              Step {currentStep + 1}: {STEPS[currentStep].title}
            </h2>
            <p className={styles.stepDescription}>
              {STEPS[currentStep].description}
            </p>
          </div>

          <div className={styles.stepContent}>
            {renderStepContent()}
          </div>

          {currentStep < STEPS.length - 1 && (
            <div className={styles.stepActions}>
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                <FiArrowLeft />
                Previous
              </Button>

              <Button
                variant="primary"
                onClick={handleNext}
                disabled={!canProceedToNext()}
              >
                Next
                <FiArrowRight />
              </Button>
            </div>
          )}

          {currentStep === STEPS.length - 1 && (
            <div className={styles.stepActions}>
              <Button
                variant="outline"
                onClick={handlePrevious}
              >
                <FiArrowLeft />
                Previous
              </Button>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
