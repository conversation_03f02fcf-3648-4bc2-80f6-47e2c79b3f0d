@use '@/src/scss/variables' as *;
@use '@/src/scss/mixins' as *;
@use 'sass:color';

.orders {
  @include flexbox(flex-start, stretch, column);
  gap: 1rem;
}

.orders_list {
  box-shadow: $box-shadow-1;
  padding: 0.5rem 1rem;
  display: grid;
  grid-template-columns: 100%;

  .order_id {
    @include flexbox(center, center);
    padding: 0.5rem 0;
    color: $primary-blue;
    font-weight: bold;
    font-size: $font-size-3;
  }

  &__order_items {
    // background-color: #c3fae8;
    // background-color: red;
    @include flexbox(center, center);
    padding: 0rem;
    gap: 1rem;
  }

  &__status {
    @include flexbox(center, stretch, column);
    // padding: 0.4rem 0 0 0;
    margin: 0.2rem 0 0 0;
    // background-color: #c3fae8;

    section {
      // background-color: #c3fae8;
      font-size: $font-size-2;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      padding: 0.2rem;

      h3 {
        font-weight: bold;
        color: $primary-dark-text-color;
      }

      p {
        text-align: left;
      }
    }
  }

  .view_order {
    @include flexbox(center, center);
    width: 100%;
    column-gap: 1rem;
    // padding: 1rem;
    text-align: center;
    text-decoration: underline $primary-blue;

    a {
      color: $primary-blue;
      transition: all 0.3s ease;

      &:hover {
        color: color.adjust($primary-blue, $lightness: -10%, $space: hsl);
      }
    }

    button {
      @include btn($error-red, #fff);
      padding: 0.6rem;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background-color: color.adjust($error-red, $lightness: 40%, $space: hsl);
      }

      i {
        @include flexbox(center, center);
      }
    }
  }
}

.cart__item_list {
  // background-color: red;
  width: 100%;
}

.cart__cart_item {
  // @include flexbox(center, center);
  background-color: $alice-blue;
  padding: 0.5rem 0;
  margin: 0;
  width: 100%;
  display: grid;
  grid-template-columns: 20% 80%;
  box-shadow: $box-shadow-1;
}

.cart_items_img {
  @include flexbox(center, center);

  img {
    object-fit: contain;
  }
}

.cart_items_info {
  @include flexbox(flex-start, flex-start, column);
  row-gap: 0.2rem;

  div {
    @include flexbox(flex-start, center);
    column-gap: 0.2rem;

    p {
      font-weight: bold;
      font-size: $font-size-1;
      color: $primary-lighter-text-color;
    }
  }

  > a:first-child {
    font-weight: bold;
  }
}

@media (width > $tablet) {
  .orders_list {
    grid-template-columns: 1fr 9fr 9fr 1fr;
    padding: 1rem;

    .view_order {
      padding: 0;
      width: 4rem;
      flex-direction: column;
      row-gap: 1rem;
    }

    &__status {
      padding: 0 0 0 1rem;
    }
  }
}
