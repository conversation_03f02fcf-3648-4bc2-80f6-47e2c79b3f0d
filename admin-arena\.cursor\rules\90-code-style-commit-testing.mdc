---
description: Code style, commits, and testing guidelines
alwaysApply: true
---

### Code style, commits, and testing

Code style
- Strict TS is enabled; export explicit types for public APIs.
- Clear names, early returns, small focused functions.
- Move non-trivial logic into helpers/services/hooks.

ESLint
- Use `eslint.config.js`; consider enabling type-aware configs later.

Commits/branches
- Branches: `feat/*`, `fix/*`, `chore/*`, `docs/*`, `refactor/*`.
- Imperative, descriptive messages.

Testing
- Prefer integration-style tests for hooks/services.
- Consider Vitest + React Testing Library if/when tests are added.
- Target ~80% coverage on core modules.
