# UI Components & Design System Technical Plan

## Overview

This document outlines the implementation plan for the UI components and design system, following the patterns established in the react-ts-client application with SCSS modules.

## Package Versions

```json
{
  "react": "^19.1.0",
  "react-dom": "^19.1.0",
  "react-hook-form": "^7.60.0",
  "@hookform/resolvers": "^3.4.2",
  "react-icons": "^5.5.0",
  "react-spinners": "^0.17.0",
  "date-fns": "^4.1.0",
  "sass": "^1.89.2",
  "zod": "^4.0.0"
}
```

## SCSS Architecture

### 1. SCSS Structure (Based on react-ts-client)

```
src/scss/
├── index.scss              # Main entry point
├── _variables.scss         # Design tokens and variables
├── _mixins.scss           # Reusable mixins
├── _resets.scss           # CSS resets and normalize
└── animations.scss        # Animation definitions

src/components/
├── layouts/
│   └── AdminLayout.module.scss
├── navigation/
│   ├── Sidebar.module.scss
│   └── Breadcrumb.module.scss
├── ui/
│   ├── Button.module.scss
│   ├── Input.module.scss
│   ├── Table.module.scss
│   └── Modal.module.scss
└── forms/
    └── FormField.module.scss
```

### 2. Design System Variables

```scss
// src/scss/_variables.scss
// Color Palette
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

$success-500: #10b981;
$warning-500: #f59e0b;
$error-500: #ef4444;

// Typography
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Spacing
$spacing-1: 0.25rem;   // 4px
$spacing-2: 0.5rem;    // 8px
$spacing-3: 0.75rem;   // 12px
$spacing-4: 1rem;      // 16px
$spacing-5: 1.25rem;   // 20px
$spacing-6: 1.5rem;    // 24px
$spacing-8: 2rem;      // 32px
$spacing-10: 2.5rem;   // 40px
$spacing-12: 3rem;     // 48px
$spacing-16: 4rem;     // 64px

// Border Radius
$border-radius-sm: 0.25rem;  // 4px
$border-radius: 0.375rem;    // 6px
$border-radius-md: 0.5rem;   // 8px
$border-radius-lg: 0.75rem;  // 12px
$border-radius-xl: 1rem;     // 16px

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
```

### 3. Mixins

```scss
// src/scss/_mixins.scss
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin button-size($padding-y, $padding-x, $font-size) {
  padding: $padding-y $padding-x;
  font-size: $font-size;
}

@mixin input-base {
  display: block;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-size: $font-size-sm;
  background-color: white;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  &:focus {
    outline: none;
    border-color: $primary-500;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }
  
  &:disabled {
    background-color: $gray-50;
    cursor: not-allowed;
  }
}

@mixin card {
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

@mixin responsive($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  }
}
```

## Core UI Components

### 1. Button Component

```typescript
// src/components/ui/Button.tsx
import React from 'react';
import { Spinner } from 'react-spinners';
import styles from './Button.module.scss';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  disabled,
  className,
  ...props
}) => {
  const buttonClasses = [
    styles.button,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      className={buttonClasses}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <Spinner size={16} className={styles.spinner} />}
      {!loading && leftIcon && <span className={styles.leftIcon}>{leftIcon}</span>}
      <span className={styles.content}>{children}</span>
      {!loading && rightIcon && <span className={styles.rightIcon}>{rightIcon}</span>}
    </button>
  );
};
```

```scss
// src/components/ui/Button.module.scss
@import '../../scss/variables';
@import '../../scss/mixins';

.button {
  @include button-base;
  
  &.sm {
    @include button-size($spacing-2, $spacing-3, $font-size-sm);
  }
  
  &.md {
    @include button-size($spacing-3, $spacing-4, $font-size-base);
  }
  
  &.lg {
    @include button-size($spacing-4, $spacing-6, $font-size-lg);
  }
  
  &.primary {
    background-color: $primary-600;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: $primary-700;
    }
    
    &:active {
      background-color: $primary-800;
    }
  }
  
  &.secondary {
    background-color: $gray-600;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: $gray-700;
    }
  }
  
  &.outline {
    background-color: transparent;
    color: $primary-600;
    border: 1px solid $primary-600;
    
    &:hover:not(:disabled) {
      background-color: $primary-50;
    }
  }
  
  &.ghost {
    background-color: transparent;
    color: $gray-600;
    
    &:hover:not(:disabled) {
      background-color: $gray-100;
    }
  }
  
  &.danger {
    background-color: $error-500;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #dc2626;
    }
  }
  
  &.fullWidth {
    width: 100%;
  }
}

.content {
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.leftIcon,
.rightIcon {
  display: flex;
  align-items: center;
}

.spinner {
  margin-right: $spacing-2;
}
```

### 2. Input Component

```typescript
// src/components/ui/Input.tsx
import React, { forwardRef } from 'react';
import styles from './Input.module.scss';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  fullWidth = true,
  className,
  ...props
}, ref) => {
  const inputClasses = [
    styles.input,
    leftIcon && styles.hasLeftIcon,
    rightIcon && styles.hasRightIcon,
    error && styles.error,
    fullWidth && styles.fullWidth,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={styles.inputGroup}>
      {label && (
        <label className={styles.label} htmlFor={props.id}>
          {label}
        </label>
      )}
      
      <div className={styles.inputWrapper}>
        {leftIcon && <div className={styles.leftIcon}>{leftIcon}</div>}
        
        <input
          ref={ref}
          className={inputClasses}
          {...props}
        />
        
        {rightIcon && <div className={styles.rightIcon}>{rightIcon}</div>}
      </div>
      
      {error && <span className={styles.errorText}>{error}</span>}
      {helperText && !error && <span className={styles.helperText}>{helperText}</span>}
    </div>
  );
});

Input.displayName = 'Input';
```

```scss
// src/components/ui/Input.module.scss
@import '../../scss/variables';
@import '../../scss/mixins';

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  @include input-base;
  
  &.hasLeftIcon {
    padding-left: $spacing-10;
  }
  
  &.hasRightIcon {
    padding-right: $spacing-10;
  }
  
  &.error {
    border-color: $error-500;
    
    &:focus {
      border-color: $error-500;
      box-shadow: 0 0 0 3px rgba($error-500, 0.1);
    }
  }
  
  &.fullWidth {
    width: 100%;
  }
}

.leftIcon,
.rightIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-400;
  pointer-events: none;
}

.leftIcon {
  left: $spacing-3;
}

.rightIcon {
  right: $spacing-3;
}

.errorText {
  font-size: $font-size-xs;
  color: $error-500;
}

.helperText {
  font-size: $font-size-xs;
  color: $gray-500;
}
```

### 3. Data Table Component

```typescript
// src/components/ui/DataTable.tsx
import React, { useState } from 'react';
import { FiChevronUp, FiChevronDown, FiMoreHorizontal } from 'react-icons/fi';
import { Button } from './Button';
import styles from './DataTable.module.scss';

interface Column<T> {
  key: keyof T | string;
  header: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (key: string) => void;
  onRowClick?: (row: T) => void;
  selectedRows?: Set<string | number>;
  onRowSelect?: (rowId: string | number) => void;
  onSelectAll?: () => void;
  bulkActions?: React.ReactNode;
  emptyMessage?: string;
  rowKey?: keyof T;
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  sortBy,
  sortDirection,
  onSort,
  onRowClick,
  selectedRows,
  onRowSelect,
  onSelectAll,
  bulkActions,
  emptyMessage = 'No data available',
  rowKey = 'id' as keyof T,
}: DataTableProps<T>) {
  const [hoveredRow, setHoveredRow] = useState<string | number | null>(null);

  const handleSort = (key: string) => {
    if (onSort) {
      onSort(key);
    }
  };

  const handleRowSelect = (rowId: string | number) => {
    if (onRowSelect) {
      onRowSelect(rowId);
    }
  };

  const isAllSelected = selectedRows && data.length > 0 && 
    data.every(row => selectedRows.has(row[rowKey]));

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.skeleton}>
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className={styles.skeletonRow} />
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={styles.emptyState}>
        <p>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={styles.tableContainer}>
      {bulkActions && selectedRows && selectedRows.size > 0 && (
        <div className={styles.bulkActions}>
          <span className={styles.selectedCount}>
            {selectedRows.size} item{selectedRows.size !== 1 ? 's' : ''} selected
          </span>
          {bulkActions}
        </div>
      )}

      <div className={styles.tableWrapper}>
        <table className={styles.table}>
          <thead className={styles.thead}>
            <tr>
              {onRowSelect && (
                <th className={styles.checkboxColumn}>
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={onSelectAll}
                    className={styles.checkbox}
                  />
                </th>
              )}
              
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`${styles.th} ${column.sortable ? styles.sortable : ''}`}
                  style={{ width: column.width }}
                  onClick={column.sortable ? () => handleSort(String(column.key)) : undefined}
                >
                  <div className={styles.thContent}>
                    <span>{column.header}</span>
                    {column.sortable && sortBy === column.key && (
                      <span className={styles.sortIcon}>
                        {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              
              <th className={styles.actionsColumn}>
                <FiMoreHorizontal />
              </th>
            </tr>
          </thead>
          
          <tbody className={styles.tbody}>
            {data.map((row) => {
              const rowId = row[rowKey];
              const isSelected = selectedRows?.has(rowId);
              const isHovered = hoveredRow === rowId;
              
              return (
                <tr
                  key={String(rowId)}
                  className={`${styles.tr} ${isSelected ? styles.selected : ''} ${isHovered ? styles.hovered : ''}`}
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                  onMouseEnter={() => setHoveredRow(rowId)}
                  onMouseLeave={() => setHoveredRow(null)}
                >
                  {onRowSelect && (
                    <td className={styles.checkboxColumn}>
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleRowSelect(rowId)}
                        className={styles.checkbox}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </td>
                  )}
                  
                  {columns.map((column) => (
                    <td key={String(column.key)} className={styles.td}>
                      {column.render 
                        ? column.render(row[column.key as keyof T], row)
                        : String(row[column.key as keyof T] || '')
                      }
                    </td>
                  ))}
                  
                  <td className={styles.actionsColumn}>
                    <Button variant="ghost" size="sm">
                      <FiMoreHorizontal />
                    </Button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
```

### 4. Form Components with React Hook Form

```typescript
// src/components/forms/FormField.tsx
import React from 'react';
import { useController, Control, FieldPath, FieldValues } from 'react-hook-form';
import { Input } from '../ui/Input';

interface FormFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export function FormField<T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  type = 'text',
  helperText,
  leftIcon,
  rightIcon,
}: FormFieldProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Input
      {...field}
      id={name}
      label={label}
      placeholder={placeholder}
      type={type}
      error={error?.message}
      helperText={helperText}
      leftIcon={leftIcon}
      rightIcon={rightIcon}
    />
  );
}
```

## Layout Components

### 1. Admin Layout

```typescript
// src/components/layouts/AdminLayout.tsx
import React, { useState } from 'react';
import { Sidebar } from '../navigation/Sidebar';
import { Breadcrumb } from '../navigation/Breadcrumb';
import { UserMenu } from '../navigation/UserMenu';
import styles from './AdminLayout.module.scss';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className={styles.layout}>
      <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
      
      <div className={`${styles.main} ${sidebarCollapsed ? styles.sidebarCollapsed : ''}`}>
        <header className={styles.header}>
          <Breadcrumb />
          <UserMenu />
        </header>
        
        <main className={styles.content}>
          {children}
        </main>
      </div>
    </div>
  );
};
```

```scss
// src/components/layouts/AdminLayout.module.scss
@import '../../scss/variables';
@import '../../scss/mixins';

.layout {
  display: flex;
  min-height: 100vh;
  background-color: $gray-50;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px; // Sidebar width
  transition: margin-left 0.3s ease;
  
  &.sidebarCollapsed {
    margin-left: 80px; // Collapsed sidebar width
  }
}

.header {
  @include flex-between;
  padding: $spacing-4 $spacing-6;
  background: white;
  border-bottom: 1px solid $gray-200;
  box-shadow: $shadow-sm;
  z-index: $z-sticky;
}

.content {
  flex: 1;
  padding: $spacing-6;
  overflow-y: auto;
}

@include responsive(lg) {
  .main {
    margin-left: 0;
    
    &.sidebarCollapsed {
      margin-left: 0;
    }
  }
}
```

## Next Steps

1. **Component Library**: Build remaining UI components (Modal, Dropdown, Tabs, etc.)
2. **Form Validation**: Integrate Zod schemas with React Hook Form
3. **Theme System**: Implement dark mode support
4. **Accessibility**: Add ARIA labels and keyboard navigation
5. **Storybook**: Set up component documentation and testing
