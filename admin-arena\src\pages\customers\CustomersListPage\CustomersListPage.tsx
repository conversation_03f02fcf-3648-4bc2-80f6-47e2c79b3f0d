// Customers list page component
// Displays paginated list of customers with search and filtering

import React, { useState } from 'react'
import { FiSearch, FiFilter, FiEdit, FiEye, FiMail } from 'react-icons/fi'
import { Link } from '@tanstack/react-router'
import { useCustomers } from '../../../hooks/use-customers'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { DataTable } from '../../../components/ui/DataTable'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import { Customer, CustomerFilters } from '../../../types/api-types'
import { formatCurrency } from '../../../utils/format'
import styles from './CustomersListPage.module.scss'

export const CustomersListPage: React.FC = () => {
  const [filters, setFilters] = useState<CustomerFilters>({
    page: 1,
    page_size: 20,
    ordering: '-date_joined',
  })

  const {
    data: customersData,
    isLoading,
    error,
    refetch,
  } = useCustomers(filters)

  const handleSearch = (search: string) => {
    setFilters(prev => ({
      ...prev,
      search,
      page: 1,
    }))
  }

  const handleFilterChange = (newFilters: Partial<CustomerFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1,
    }))
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const columns = [
    {
      key: 'customer',
      title: 'Customer',
      render: (customer: Customer) => (
        <div className={styles.customerCell}>
          <div className={styles.customerInfo}>
            <h4 className={styles.customerName}>
              {customer.first_name} {customer.last_name}
            </h4>
            <p className={styles.customerEmail}>{customer.email}</p>
            {customer.phone_number && (
              <p className={styles.customerPhone}>{customer.phone_number}</p>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (customer: Customer) => (
        <Badge variant={customer.is_active ? 'success' : 'error'}>
          {customer.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'orders',
      title: 'Orders',
      render: (customer: Customer) => (
        <div className={styles.orderStats}>
          <span className={styles.orderCount}>{customer.orders_count}</span>
          <span className={styles.orderLabel}>orders</span>
        </div>
      ),
    },
    {
      key: 'total_spent',
      title: 'Total Spent',
      render: (customer: Customer) => (
        <span className={styles.totalSpent}>
          {formatCurrency(customer.total_spent)}
        </span>
      ),
    },
    {
      key: 'date_joined',
      title: 'Joined',
      render: (customer: Customer) => (
        new Date(customer.date_joined).toLocaleDateString()
      ),
    },
    {
      key: 'last_login',
      title: 'Last Login',
      render: (customer: Customer) => (
        customer.last_login 
          ? new Date(customer.last_login).toLocaleDateString()
          : 'Never'
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (customer: Customer) => (
        <div className={styles.actions}>
          <PermissionGuard permission="staff.view_customerproxy">
            <Button
              as={Link}
              to={`/customers/${customer.id}`}
              variant="ghost"
              size="sm"
              title="View customer"
            >
              <FiEye />
            </Button>
          </PermissionGuard>
          
          <PermissionGuard permission="staff.change_customerproxy">
            <Button
              as={Link}
              to={`/customers/${customer.id}/edit`}
              variant="ghost"
              size="sm"
              title="Edit customer"
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
          
          <Button
            variant="ghost"
            size="sm"
            title="Send email"
            onClick={() => {
              window.location.href = `mailto:${customer.email}`
            }}
          >
            <FiMail />
          </Button>
        </div>
      ),
    },
  ]

  if (isLoading && !customersData) {
    return <PageLoading message="Loading customers..." />
  }

  if (error) {
    return (
      <div className={styles.error}>
        <Card padding="lg">
          <h2>Error Loading Customers</h2>
          <p>Failed to load customers. Please try again.</p>
          <Button onClick={() => refetch()} variant="primary">
            Retry
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Customers</h1>
          <p className={styles.subtitle}>
            Manage customer accounts and information
          </p>
        </div>
      </div>

      <Card className={styles.filtersCard}>
        <div className={styles.filters}>
          <div className={styles.searchSection}>
            <Input
              type="text"
              placeholder="Search customers..."
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
              leftIcon={<FiSearch />}
              className={styles.searchInput}
            />
          </div>
          
          <div className={styles.filterSection}>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Implement advanced filters modal
                console.log('Open filters')
              }}
            >
              <FiFilter />
              Filters
            </Button>
          </div>
        </div>
      </Card>

      <Card className={styles.tableCard}>
        <DataTable
          data={customersData?.results || []}
          columns={columns}
          loading={isLoading}
          pagination={{
            current: filters.page || 1,
            pageSize: filters.page_size || 20,
            total: customersData?.count || 0,
            onChange: handlePageChange,
          }}
        />
      </Card>
    </div>
  )
}
