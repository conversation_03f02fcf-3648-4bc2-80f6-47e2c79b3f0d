@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-4;
}

.tableWrapper {
  overflow-x: auto;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  background-color: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: $font-size-sm;
}

.thead {
  background-color: $gray-50;
  border-bottom: 1px solid $gray-200;
}

.th {
  padding: $spacing-3 $spacing-4;
  text-align: left;
  font-weight: $font-weight-semibold;
  color: $gray-700;
  font-size: $font-size-xs;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
  
  &.align-center {
    text-align: center;
  }
  
  &.align-right {
    text-align: right;
  }
}

.tbody {
  background-color: white;
}

.tr {
  border-bottom: 1px solid $gray-100;
  transition: background-color 0.2s ease-in-out;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      background-color: $gray-50;
    }
  }
}

.td {
  padding: $spacing-4;
  vertical-align: top;
  color: $gray-900;
  
  &.align-center {
    text-align: center;
  }
  
  &.align-right {
    text-align: right;
  }
}

.loadingCell,
.emptyCell {
  padding: $spacing-8;
  text-align: center;
  color: $gray-500;
}

.loadingContent {
  @include flex-column-center;
  gap: $spacing-3;
  
  span {
    font-size: $font-size-sm;
    color: $gray-600;
  }
}

.emptyContent {
  @include flex-column-center;
  gap: $spacing-2;
  
  span {
    font-size: $font-size-base;
    color: $gray-500;
  }
}

.emptyCell {
  color: $gray-400;
  font-style: italic;
}

// Pagination styles
.pagination {
  @include flex-between;
  gap: $spacing-4;
  padding: $spacing-4 0;
  
  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.paginationInfo {
  font-size: $font-size-sm;
  color: $gray-600;
  
  @include mobile-only {
    text-align: center;
  }
}

.paginationControls {
  @include flex-center;
  gap: $spacing-2;
}

.paginationButton {
  @include flex-center;
  gap: $spacing-1;
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.pageNumbers {
  @include flex-center;
  gap: $spacing-1;
}

.pageNumber {
  @include flex-center;
  width: 32px;
  height: 32px;
  border: 1px solid $gray-300;
  background-color: white;
  color: $gray-700;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  border-radius: $border-radius;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover:not(.active):not(.ellipsis) {
    background-color: $gray-50;
    border-color: $gray-400;
  }
  
  &.active {
    background-color: $primary-500;
    border-color: $primary-500;
    color: white;
  }
  
  &.ellipsis {
    border: none;
    background: transparent;
    cursor: default;
    color: $gray-400;
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

// Responsive adjustments
@include mobile-only {
  .tableWrapper {
    border-radius: $border-radius;
  }
  
  .th,
  .td {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-xs;
  }
  
  .th {
    font-size: 10px;
  }
  
  .paginationControls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .pageNumbers {
    order: -1;
    width: 100%;
    justify-content: center;
    margin-bottom: $spacing-2;
  }
  
  .pageNumber {
    width: 28px;
    height: 28px;
    font-size: $font-size-xs;
  }
}
