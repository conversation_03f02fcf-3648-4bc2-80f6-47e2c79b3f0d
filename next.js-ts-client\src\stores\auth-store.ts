import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'


interface authStoreShape {
  isLoggedIn: boolean
  username: string | null
  regInitiated: boolean
  verificationCodeSubmitted: boolean
  passwordSubmitted: boolean
  customerDetailsSubmitted: boolean
  updateAuthInfoSubmitted: boolean
  // altUsername: string | null
  authInfoVerifyCodeSubmitted: boolean
  setIsLoggedIn: (isLoggedIn: boolean) => void
  // logout: () => void
  setUsername: (username: string | null) => void
  setRegInitiated: (regInitiated: boolean) => void
  setPasswordSubmitted: (passwordSubmitted: boolean) => void
  setVerificationCodeSubmitted: (verificationCodeSubmitted: boolean) => void
  setCustomerDetailsSubmitted: (customerDetailsSubmitted: boolean) => void
  setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted: boolean) => void
  // setAltUsername: (altUsername: string | null) => void
  setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted: boolean) => void
}

const authStore = create<authStoreShape>()(
  devtools(
    persist(
      (set) => ({
        isLoggedIn: false,
        username: null,
        regInitiated: false,
        verificationCodeSubmitted: false,
        passwordSubmitted: false,
        customerDetailsSubmitted: false,
        updateAuthInfoSubmitted: false,
        // altUsername: null,
        authInfoVerifyCodeSubmitted: false,

        setIsLoggedIn: (isLoggedIn: boolean) => {
          set({ isLoggedIn })
        },
        // logout: () => {
        //   set({ isLoggedIn: false })
        // },
        setUsername: (username: string | null) => {
          set({ username })
        },
        setRegInitiated: (regInitiated: boolean) => {
          set({ regInitiated })
        },
        setVerificationCodeSubmitted: (verificationCodeSubmitted: boolean) => {
          set({ verificationCodeSubmitted })
        },
        setPasswordSubmitted: (passwordSubmitted: boolean) => {
          set({ passwordSubmitted })
        },
        setCustomerDetailsSubmitted: (customerDetailsSubmitted: boolean) => {
          set({ customerDetailsSubmitted })
        },
        setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted: boolean) => {
          set({ updateAuthInfoSubmitted })
        },
        // setAltUsername: (altUsername: string | null) => {
        //   set({ altUsername })
        // },
        setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted: boolean) => {
          set({ authInfoVerifyCodeSubmitted })
        }
      }),
      {
        name: 'auth_store', // Name of the persisted store
        partialize: (state) => ({
          isLoggedIn: state.isLoggedIn,
          // username: state.username,
          // regInitiated: state.regInitiated,
        }),
      }
    )
  )
)

export default authStore
