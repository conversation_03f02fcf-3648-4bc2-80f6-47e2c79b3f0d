# Picky E-Commerce RESTful APIs

## Table of Contents

- [Audit](#audit)
- [Auth](#auth)
- [Cart](#cart)
- [Customers](#customers)
- [Orders](#orders)
- [Payments](#payments)
- [Products](#products)
- [Roles](#roles)
- [Staff](#staff)
- [Users](#users)
- [Wishlist](#wishlist)

---

## Audit

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/api/staff/audit/` | View audit logs (staff with audit permissions) |
| GET    | `/api/staff/audit/{id}/` | View audit log by ID |
| GET    | `/api/staff/audit/summary/` | Get audit summary statistics |

---

## Auth

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | `/api/staff/auth/check-permission/` | Check if user has specific permission |
| GET    | `/api/staff/auth/permissions/` | Get detailed permission info for current user |
| GET    | `/api/staff/auth/user/` | Get current staff user info |

---

## Cart

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/api/staff/cart/cart-items/` | Staff cart item management (read-only) |
| GET    | `/api/staff/cart/cart-items/{id}/` | Staff cart item management (read-only) |
| GET    | `/api/staff/cart/cart-items/popular_products/` | Most popular products in carts |
| GET    | `/api/staff/cart/carts/` | Staff cart management (read-only, analytics) |
| GET    | `/api/staff/cart/carts/{id}/` | Staff cart management (read-only, analytics) |
| GET    | `/api/staff/cart/carts/{id}/items/` | Detailed cart items for a specific cart |
| GET    | `/api/staff/cart/carts/abandoned/` | Abandoned carts |
| GET    | `/api/staff/cart/carts/analytics/` | Cart analytics data |
| POST   | `/api/staff/cart/carts/bulk_operations/`
| GET    | `/api/staff/cart/carts/summary/` | Cart summary statistics |

---

## Customers

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET/POST | `/api/staff/customers/addresses/` | Staff address management |
| GET/PUT/PATCH/DELETE | `/api/staff/customers/addresses/{id}/` | Staff address management |
| GET    | `/api/staff/customers/addresses/{id}/usage_stats/` | Usage stats for an address |
| GET    | `/api/staff/customers/addresses/geographic_distribution/` | Geographic distribution |
| GET/POST | `/api/staff/customers/customers/` | Staff customer management (CRUD, RBAC) |
| GET/PUT/PATCH/DELETE | `/api/staff/customers/customers/{id}/` | Staff customer management (CRUD, RBAC) |
| GET    | `/api/staff/customers/customers/{id}/activity/` | Detailed customer activity |
| GET    | `/api/staff/customers/customers/{id}/support_history/` | Customer support history |
| GET    | `/api/staff/customers/customers/analytics/` | Customer analytics data |
| POST   | `/api/staff/customers/customers/bulk_operations/` | Bulk operations on customers |
| GET    | `/api/staff/customers/customers/segments/` | Customer segment distribution |

---

## Orders

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET/POST | `/api/staff/orders/assignments/` | Manage order assignments |
| GET/PUT/PATCH/DELETE | `/api/staff/orders/assignments/{id}/` | Manage order assignments |
| GET    | `/api/staff/orders/bulk-operations/` | Track bulk order operations |
| GET    | `/api/staff/orders/bulk-operations/{id}/` | Track bulk order operations |
| GET    | `/api/staff/orders/bulk-operations/{id}/progress/` | Real-time progress of bulk operation |
| GET    | `/api/staff/orders/documents/` | Order documents (labels, invoices, warehouse docs) |
| GET    | `/api/staff/orders/documents/{id}/` | Order documents (labels, invoices, warehouse docs) |
| POST   | `/api/staff/orders/documents/{id}/mark_printed/` | Mark document as printed |
| GET    | `/api/staff/orders/documents/download_bulk/` | Download multiple documents as ZIP |
| GET/POST | `/api/staff/orders/notes/` | Manage order notes |
| GET/PUT/PATCH/DELETE | `/api/staff/orders/notes/{id}/` | Manage order notes |
| GET/POST | `/api/staff/orders/orders/` | Staff order management (CRUD, RBAC) |
| GET/PUT/PATCH/DELETE | `/api/staff/orders/orders/{id}/` | Staff order management (CRUD, RBAC) |
| POST   | `/api/staff/orders/orders/{id}/add_note/` | Add note to order |
| POST   | `/api/staff/orders/orders/{id}/assign/` | Assign order to staff member |
| PATCH  | `/api/staff/orders/orders/{id}/update_status/` | Update order delivery status (audit trail) |
| POST   | `/api/staff/orders/orders/bulk_assign/` | Bulk assign orders |
| POST   | `/api/staff/orders/orders/bulk_generate_documents/` | Bulk generate documents |
| POST   | `/api/staff/orders/orders/bulk_update_status/` | Bulk update order status |
| GET    | `/api/staff/orders/orders/dashboard_stats/` | Dashboard statistics for orders |
| GET    | `/api/staff/orders/orders/my_assignments/` | Orders assigned to current staff |
| GET    | `/api/staff/orders/status-history/` | View order status history |
| GET    | `/api/staff/orders/status-history/{id}/` | View order status history |

---

## Payments

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET/POST | `/api/staff/payments/disputes/` | Payment dispute management |
| GET/PATCH | `/api/staff/payments/disputes/{id}/` | Payment dispute management |
| POST   | `/api/staff/payments/disputes/{id}/resolve/` | Resolve a dispute |
| POST   | `/api/staff/payments/disputes/assign_disputes/` | Assign disputes to staff |
| GET    | `/api/staff/payments/disputes/summary/` | Dispute summary statistics |
| GET/POST | `/api/staff/payments/payment-options/` | Staff payment option management |
| GET/PUT/PATCH/DELETE | `/api/staff/payments/payment-options/{id}/` | Staff payment option management |
| GET    | `/api/staff/payments/payment-options/{id}/transactions/` | Recent transactions for payment method |
| GET    | `/api/staff/payments/payment-options/analytics/` | Payment analytics data |
| POST   | `/api/staff/payments/payment-options/bulk_operations/` | Bulk operations on payments |
| GET    | `/api/staff/payments/payment-options/monitoring/` | Real-time payment monitoring |
| GET    | `/api/staff/payments/paypal-orders/` | Staff PayPal order management (read-only) |
| GET    | `/api/staff/payments/paypal-orders/{id}/` | Staff PayPal order management (read-only) |
| GET    | `/api/staff/payments/paypal-orders/{id}/dispute_info/` | Dispute info for PayPal order |
| GET    | `/api/staff/payments/transaction-audits/` | Payment transaction audit logs (read-only) |
| GET    | `/api/staff/payments/transaction-audits/{id}/` | Payment transaction audit logs (read-only) |

---

## Products

| Method | Endpoint | Description |
|--------|----------|-------------|
| DELETE | `/api/staff/products/associations/delete_association/` | Delete product type attribute association |
| GET    | `/api/staff/products/associations/` | List all associations |
| POST   | `/api/staff/products/associations/` | Create new association |
| GET    | `/api/staff/products/associations/{id}/` | Get specific association |
| PATCH  | `/api/staff/products/associations/{id}/` | Update association |
| DELETE | `/api/staff/products/associations/{id}/` | Delete association |
| GET    | `/api/staff/products/associations/product_type_attributes/` | Get product type attributes (search/filter) |
| POST   | `/api/staff/products/associations/save_association/` | Save/update product type attribute association |
| GET/POST | `/api/staff/products/attribute-values/` | Staff attribute value management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/attribute-values/{id}/` | Staff attribute value management |
| POST   | `/api/staff/products/attribute-values/bulk_create/` | Bulk create attribute values |
| GET/POST | `/api/staff/products/attributes/` | Staff attribute management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/attributes/{id}/` | Staff attribute management |
| GET    | `/api/staff/products/audit/` | View product audit logs |
| GET    | `/api/staff/products/audit/{id}/` | View product audit logs |
| GET/POST | `/api/staff/products/brand-product-types/` | Manage brand-product type associations |
| GET/PUT/PATCH/DELETE | `/api/staff/products/brand-product-types/{id}/` | Manage brand-product type associations |
| POST   | `/api/staff/products/brand-product-types/bulk_associate/` | Bulk associate product types with brand |
| GET/POST | `/api/staff/products/brands/` | Staff brand management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/brands/{id}/` | Staff brand management |
| GET    | `/api/staff/products/bulk-operations/` | View bulk operation status |
| GET    | `/api/staff/products/bulk-operations/{id}/` | View bulk operation status |
| GET/POST | `/api/staff/products/categories/` | Staff category management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/categories/{id}/` | Staff category management |
| POST   | `/api/staff/products/categories/{id}/move/` | Move category to new parent |
| GET    | `/api/staff/products/categories/tree/` | Get category tree structure |
| GET/POST | `/api/staff/products/discounts/` | Staff discount management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/discounts/{id}/` | Staff discount management |
| POST   | `/api/staff/products/discounts/{id}/apply_to_variants/` | Apply discount to product variants |
| GET/POST | `/api/staff/products/images/` | Staff product image management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/images/{id}/` | Staff product image management |
| GET/POST | `/api/staff/products/product-attribute-values/` | Manage product-level attribute value associations |
| GET/PUT/PATCH/DELETE | `/api/staff/products/product-attribute-values/{id}/` | Manage product-level attribute value associations |
| POST   | `/api/staff/products/product-attribute-values/bulk_associate/` | Bulk associate attribute values with product |
| GET/POST | `/api/staff/products/product-types/` | Staff product type management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/product-types/{id}/` | Staff product type management |
| POST   | `/api/staff/products/product-types/{id}/associate_attributes/` | Associate attributes with product type |
| GET    | `/api/staff/products/product-types/{id}/attributes/` | Get attributes for product type |
| GET/POST | `/api/staff/products/products/` | Staff product management (CRUD, RBAC) |
| GET/PUT/PATCH/DELETE | `/api/staff/products/products/{id}/` | Staff product management (CRUD, RBAC) |
| PATCH  | `/api/staff/products/products/{id}/change_status/` | Change product active status |
| GET    | `/api/staff/products/products/analytics/` | Product analytics data |
| POST   | `/api/staff/products/products/bulk_operations/` | Bulk operations on products |
| POST   | `/api/staff/products/products/create_with_variants/` | Create product with variants |
| GET    | `/api/staff/products/reviews/` | Staff review management (read-only, moderation) |
| GET/DELETE | `/api/staff/products/reviews/{id}/` | Staff review management (read-only, moderation) |
| GET/POST | `/api/staff/products/variant-attribute-values/` | Manage product variant attribute value associations |
| GET/PUT/PATCH/DELETE | `/api/staff/products/variant-attribute-values/{id}/` | Manage product variant attribute value associations |
| POST   | `/api/staff/products/variant-attribute-values/bulk_associate/` | Bulk associate attribute values with variant |
| PATCH  | `/api/staff/products/variant-attribute-values/bulk_update_status/` | Bulk update active status of variant attribute associations |
| GET/POST | `/api/staff/products/variants/` | Staff product variant management |
| GET/PUT/PATCH/DELETE | `/api/staff/products/variants/{id}/` | Staff product variant management |
| PATCH  | `/api/staff/products/variants/{id}/update_stock/` | Update stock quantity |
| GET    | `/api/staff/products/variants/{id}/attribute_values/` | Get all associated attribute values by product variant |
| PATCH  | `/api/staff/products/variant-attribute-values/{id}/reorder/` | Individual reorder of variant attribute value |
| PATCH  | `/api/staff/products/variant-attribute-values/reorder_drag_drop/` | Drag-and-drop reorder of variant attribute values |
| PATCH  | `/api/staff/products/variant-attribute-values/bulk_update_order/` | Bulk order update for variant attribute values |
| PATCH  | `/api/staff/products/images/{id}/reorder/` | Individual reorder of product image |
| PATCH  | `/api/staff/products/images/reorder_drag_drop/` | Drag-and-drop reorder of product images |

---

## Roles

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET/POST | `/api/staff/roles/` | Manage roles (enhanced Group proxy) |
| GET/PUT/PATCH/DELETE | `/api/staff/roles/{id}/` | Manage roles (enhanced Group proxy) |
| POST   | `/api/staff/roles/{id}/add_permission/` | Add permission to role |
| GET    | `/api/staff/roles/{id}/members/` | Get all members of a role |
| GET    | `/api/staff/roles/{id}/permissions/` | Get detailed permissions for a role |
| POST   | `/api/staff/roles/{id}/remove_permission/` | Remove permission from role |

---

## Staff

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | `/api/staff/staff-management/create_staff_user/` | Create new staff user with profile/role |
| GET/POST | `/api/staff/staff-profiles/` | Manage staff profiles |
| GET/PUT/PATCH/DELETE | `/api/staff/staff-profiles/{id}/` | Manage staff profiles |
| POST   | `/api/staff/staff-profiles/{id}/change_status/` | Change staff member status |
| GET    | `/api/staff/staff-profiles/{id}/direct_reports/` | Get direct reports for staff member |
| GET    | `/api/staff/staff-profiles/department_summary/` | Summary of staff by department |

---

## Users

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/api/staff/users/` | View/manage users (group assignments, superuser toggle) |
| GET    | `/api/staff/users/{id}/` | View/manage users (group assignments, superuser toggle) |
| GET    | `/api/staff/users/{id}/groups/` | Get all groups for a user |
| POST   | `/api/staff/users/{id}/toggle_staff/` | Toggle staff status (superuser only) |
| GET    | `/api/staff/users/analytics/` | User analytics/statistics |
| POST   | `/api/staff/users/bulk_operations/` | Bulk operations on users |
| GET    | `/api/staff/users/staff_summary/` | Summary of staff users |

---

## Wishlist

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/api/staff/wishlist/wishlists/` | Staff wishlist management (read-only, analytics) |
| GET    | `/api/staff/wishlist/wishlists/{id}/` | Staff wishlist management (read-only, analytics) |
| GET    | `/api/staff/wishlist/wishlists/analytics/` | Wishlist analytics data |
| GET    | `/api/staff/wishlist/wishlists/behavior_analysis/` | Customer behavior analysis |
| POST   | `/api/staff/wishlist/wishlists/bulk_operations/` | Bulk operations on wishlist items |
| GET    | `/api/staff/wishlist/wishlists/conversion_opportunities/` | High-potential conversion opportunities |
| GET    | `/api/staff/wishlist/wishlists/customer_insights/` | Customer-specific wishlist insights |
| GET    | `/api/staff/wishlist/wishlists/marketing_insights/` | Marketing insights/recommendations |
| GET    | `/api/staff/wishlist/wishlists/product_stats/` | Wishlist statistics by product |
| GET    | `/api/staff/wishlist/wishlists/trends/` | Wishlist trends over time |

---

> **Note:** For full details on request/response payloads and permission requirements, see the API schema and documentation.
