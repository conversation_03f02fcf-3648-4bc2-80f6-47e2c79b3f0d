---
description: Environment & configuration rules (Vite)
globs:
  - src/**/*.ts*
alwaysApply: true
---

### Environment & configuration

Do
- Use `VITE_*` for all client-exposed variables (e.g., `VITE_API_BASE_URL`).
- Document vars in `README.md` and provide `.env.example`.
- Read via `import.meta.env.VITE_*` only.

Avoid
- Hardcoding URLs/keys.
- Storing secrets in repo; use `.env.local` (gitignored).

Reference
- Vite env & modes — https://vite.dev/guide/env-and-mode
