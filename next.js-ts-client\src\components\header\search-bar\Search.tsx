'use client'

import Link from 'next/link'
// navigation via window.location used instead of next/router here
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react'
import { IoMdSearch } from 'react-icons/io'

import styles from './Search.module.scss'
import { useCategories } from '@/src/hooks/product-hooks'

// Interface for the Category object
interface Category {
  id: number
  title: string
  slug: string
  level: number
  parent?: number | null // The parent category, if applicable (can be null or undefined)
  children?: Category[] // Nested children categories (optional)
}

// Enhanced Category interface with simplified properties
interface EnhancedCategory extends Category {
  hasChildren: boolean
  matchScore?: number // For search relevance
  depth: number // Calculated hierarchy depth
  children?: EnhancedCategory[] // Override to use enhanced type
}

// Utility function to calculate category depth in hierarchy
const calculateCategoryDepth = (
  categoryId: number,
  allCategories: Category[],
  currentDepth = 0
): number => {
  const category = allCategories.find((cat) => cat.id === categoryId)
  if (!category || !category.parent) {
    return currentDepth
  }
  return calculateCategoryDepth(
    category.parent,
    allCategories,
    currentDepth + 1
  )
}

// Utility function to enhance categories with new properties
const enhanceCategory = (
  category: Category,
  allCategories: Category[]
): EnhancedCategory => {
  const children = allCategories.filter((cat) => cat.parent === category.id)
  const hasChildren = children.length > 0
  const depth = calculateCategoryDepth(category.id, allCategories)

  return {
    ...category,
    hasChildren,
    depth,
    children: hasChildren
      ? children.map((child) => enhanceCategory(child, allCategories))
      : undefined,
  }
}

const Search = () => {
  // Simplified state management
  const [searchValue, setSearchValue] = useState<string>('')
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false)

  // suggestions are derived later via useMemo after categories and helper definitions

  // navigation via window.location (no router instance needed)

  // Ref to the search container to detect clicks outside of it
  const searchRef = useRef<HTMLDivElement>(null)

  // Fetch all categories using the custom hook `useCategories`
  const { data: categories = [] } = useCategories() as { data: Category[] }

  // Enhanced recursive function to find all children for a given category
  const findAllChildrenRecursive = useCallback(
    (categoryId: number, allCategories: Category[]): EnhancedCategory[] => {
      // Find all categories whose parent matches the given category ID
      const children = allCategories.filter((cat) => cat.parent === categoryId)

      // For each child, recursively find its own children and enhance them
      return children.map((child) => {
        const enhancedChild = enhanceCategory(child, allCategories)
        return {
          ...enhancedChild,
          children: findAllChildrenRecursive(child.id, allCategories),
        }
      })
    },
    []
  )

  // Derive suggestions from searchValue + categories without storing in state to avoid update loops
  const suggestedCategories = useMemo<EnhancedCategory[]>(() => {
    if (!searchValue.trim()) return []

    const lowerCaseSearchValue = searchValue.toLowerCase()

    const matchedCategories = categories.filter((category) =>
      category.title.toLowerCase().includes(lowerCaseSearchValue)
    )

    const enhancedCategoriesWithChildren = matchedCategories.map((category) => {
      const enhanced = enhanceCategory(category, categories)
      return {
        ...enhanced,
        children: findAllChildrenRecursive(category.id, categories),
        matchScore:
          lowerCaseSearchValue === category.title.toLowerCase()
            ? 100
            : category.title.toLowerCase().startsWith(lowerCaseSearchValue)
            ? 80
            : 50,
      }
    })

    return enhancedCategoriesWithChildren.sort(
      (a, b) => (b.matchScore || 0) - (a.matchScore || 0)
    )
  }, [searchValue, categories, findAllChildrenRecursive])

  // useEffect hook to update the suggestions list based on the search input
  // suggestions are derived via useMemo above; no setState inside effect required

  // useEffect hook to handle clicks outside the search component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // If the click is outside the search box, close the suggestions dropdown
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    // Add event listener for mouse clicks
    document.addEventListener('mousedown', handleClickOutside)

    // Cleanup the event listener when the component is unmounted
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Handler for form submission (search submit)
  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    // If the search value is valid (non-empty), navigate to the search results page
    if (searchValue.trim()) {
      // Use native navigation to avoid next/navigation typing issues in this simple case
      const url = `/products/?search=${encodeURIComponent(searchValue)}`
      window.location.assign(url)

      // Close the suggestions dropdown
      setShowSuggestions(false)
    }
  }

  // Enhanced render function for categories with proper structure
  const renderCategories = (category: EnhancedCategory) => {
    return (
      <div
        key={category.id}
        className={`${styles.category_item} ${
          category.hasChildren ? styles.expandable : ''
        }`}
      >
        <div className={styles.category_header}>
          {/* Category link */}
          <Link
            href={`/products/category/${category.slug}`}
            className={`${styles.category_link} ${
              category.hasChildren
                ? styles.parent_category
                : styles.leaf_category
            }`}
            onClick={() => setShowSuggestions(false)}
          >
            <span
              className={`${styles.category_title} ${
                styles[`level_${Math.min(category.depth, 3)}`]
              }`}
            >
              {category.title}
            </span>
          </Link>
        </div>

        {/* Always render children if they exist */}
        {category.children && category.children.length > 0 && (
          <div className={styles.child_categories}>
            {category.children.map((child) => renderCategories(child))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={styles.search} ref={searchRef}>
      {/* Form for the search input */}
      <form onSubmit={handleSearchSubmit}>
        <input
          type='text'
          placeholder='Search...'
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          autoComplete='off'
        />
        <button type='submit'>
          <IoMdSearch /> {/* Search icon */}
        </button>
      </form>

      {/* If there are suggestions to show */}
      {showSuggestions && (
        <div className={styles.search_suggestions}>
          <div
            className={styles.backdrop}
            onClick={() => setShowSuggestions(false)}
          ></div>{' '}
          {/* Clickable backdrop to close dropdown */}
          <div className={styles.suggestions}>
            {/* Render the suggested categories or show a "No suggestions" message */}
            {suggestedCategories.length > 0 ? (
              suggestedCategories.map((category) => renderCategories(category))
            ) : (
              <p className={styles.no_suggestions}>No suggestions found</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default Search
