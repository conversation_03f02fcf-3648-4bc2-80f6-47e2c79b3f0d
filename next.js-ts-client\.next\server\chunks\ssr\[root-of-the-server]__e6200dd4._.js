module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/lib/query-client.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * TanStack Query Client Configuration for E-Commerce Application
 * 
 * This configuration is optimized for a customer-facing e-commerce application
 * with appropriate cache settings to balance performance and data freshness.
 * 
 * Key Configuration Principles:
 * - Reduce unnecessary API calls to improve performance and user experience
 * - Set appropriate staleTime based on how frequently data changes
 * - Use gcTime (garbage collection time) to keep data in cache for reasonable periods
 * - Implement smart retry logic for failed requests
 * - Disable refetchOnWindowFocus for better UX in e-commerce scenarios
 * 
 * Based on TanStack Query v5 best practices:
 * - https://tanstack.com/query/v5/docs/react/guides/important-defaults
 */ __turbopack_context__.s([
    "cacheUtils",
    ()=>cacheUtils,
    "queryClient",
    ()=>queryClient,
    "queryUtils",
    ()=>queryUtils
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
;
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: {
        queries: {
            // Data is considered fresh for 5 minutes by default
            // This prevents unnecessary refetches while ensuring data isn't too stale
            staleTime: 5 * 60 * 1000,
            // Keep cached data for 10 minutes after it becomes inactive
            // This allows for instant navigation back to previously viewed pages
            gcTime: 10 * 60 * 1000,
            // Smart retry configuration for better error handling
            retry: (failureCount, error)=>{
                // Don't retry on client errors (4xx) except for specific cases
                // 408: Request Timeout - might be temporary network issue
                // 429: Too Many Requests - should retry with backoff
                if (error?.status >= 400 && error?.status < 500 && ![
                    408,
                    429
                ].includes(error.status)) {
                    return false // Client errors shouldn't be retried
                    ;
                }
                // Retry up to 3 times for server errors and network issues
                return failureCount < 3;
            },
            // Exponential backoff for retries to avoid overwhelming the server
            // Starts at 1s, then 2s, 4s, 8s, capped at 30s
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
            // Disabled for better UX in e-commerce
            // Prevents unnecessary refetches when user switches browser tabs/windows
            refetchOnWindowFocus: false,
            // Always refetch when network connection is restored
            // This ensures data is fresh after coming back offline
            refetchOnReconnect: 'always',
            // Only execute queries when online
            // Prevents failed requests when user is offline
            networkMode: 'online'
        },
        mutations: {
            // Don't retry mutations by default as they can have side effects
            // Mutations should be handled explicitly by the caller
            retry: false,
            // Only execute mutations when online
            networkMode: 'online'
        }
    }
});
/**
 * Entity-Specific Query Defaults
 * 
 * These configurations override the global defaults for specific entity types
 * based on how frequently their data changes and their importance to the UX.
 */ // Products change infrequently - longer cache times for better performance
queryClient.setQueryDefaults([
    'products'
], {
    staleTime: 10 * 60 * 1000,
    gcTime: 15 * 60 * 1000
});
// Categories are very stable - longest cache times
queryClient.setQueryDefaults([
    'categories'
], {
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000
});
// Brands are very stable - similar to categories
queryClient.setQueryDefaults([
    'brands'
], {
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000
});
// Cart data should always be fresh - critical for checkout process
queryClient.setQueryDefaults([
    'cart'
], {
    staleTime: 0,
    gcTime: 5 * 60 * 1000
});
// Cart items - need to be fresh but can be cached briefly
queryClient.setQueryDefaults([
    'cart_items'
], {
    staleTime: 0,
    gcTime: 5 * 60 * 1000
});
// Simple cart - optimized for quick cart previews
queryClient.setQueryDefaults([
    'simple_cart'
], {
    staleTime: 1 * 60 * 1000,
    gcTime: 5 * 60 * 1000
});
// Orders are dynamic but don't change as frequently as cart
queryClient.setQueryDefaults([
    'orders'
], {
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000
});
// Order items - similar to orders but may need more freshness
queryClient.setQueryDefaults([
    'order_items'
], {
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000
});
// Admin orders - may need more frequent updates for admin users
queryClient.setQueryDefaults([
    'orders_admin'
], {
    staleTime: 1 * 60 * 1000,
    gcTime: 10 * 60 * 1000
});
// User/customer data changes moderately - balance freshness and performance
queryClient.setQueryDefaults([
    'customer_details'
], {
    staleTime: 5 * 60 * 1000,
    gcTime: 15 * 60 * 1000
});
// Customer addresses - change infrequently
queryClient.setQueryDefaults([
    'customer_addresses'
], {
    staleTime: 15 * 60 * 1000,
    gcTime: 30 * 60 * 1000
});
// Reviews don't change often - can be cached longer
queryClient.setQueryDefaults([
    'reviews'
], {
    staleTime: 15 * 60 * 1000,
    gcTime: 30 * 60 * 1000
});
// Wishlist can change frequently but not as critical as cart
queryClient.setQueryDefaults([
    'wishlist_items'
], {
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000
});
/**
 * Authentication Mutation Defaults
 * 
 * Global error handling for authentication mutations
 * This provides consistent error handling across all auth-related mutations
 */ queryClient.setMutationDefaults([
    'auth',
    'login'
], {
    mutationFn: async (variables)=>{
        // This will be overridden by actual mutation functions
        throw new Error('Mutation function not implemented');
    },
    onError: (error)=>{
        console.error('Authentication error:', error);
    }
});
const queryUtils = {
    /**
   * Invalidate all queries for a specific entity
   * This triggers a refetch of all queries matching the entity key
   * @param entity - The entity type to invalidate (e.g., 'products', 'cart')
   */ invalidateEntity: (entity)=>{
        return queryClient.invalidateQueries({
            queryKey: [
                entity
            ]
        });
    },
    /**
   * Remove all queries for a specific entity from cache
   * This completely removes the data, forcing a fresh fetch on next request
   * @param entity - The entity type to remove (e.g., 'products', 'cart')
   */ removeEntity: (entity)=>{
        return queryClient.removeQueries({
            queryKey: [
                entity
            ]
        });
    },
    /**
   * Get cached data for a specific query
   * Returns the cached data if it exists, otherwise returns undefined
   * @param queryKey - The query key to retrieve data for
   * @returns The cached data or undefined
   */ getQueryData: (queryKey)=>{
        return queryClient.getQueryData(queryKey);
    },
    /**
   * Set cached data for a specific query
   * Updates the cache with new data, useful for optimistic updates
   * @param queryKey - The query key to update
   * @param data - The new data to set in cache
   */ setQueryData: (queryKey, data)=>{
        return queryClient.setQueryData(queryKey, data);
    },
    /**
   * Prefetch a query to populate the cache
   * Useful for preloading data that will be needed soon
   * @param queryKey - The query key to prefetch
   * @param queryFn - The function to fetch the data
   */ prefetchQuery: (queryKey, queryFn)=>{
        return queryClient.prefetchQuery({
            queryKey,
            queryFn
        });
    },
    /**
   * Clear all cached data
   * This removes all queries from the cache, forcing fresh fetches
   * Useful for logging out or clearing user-specific data
   */ clear: ()=>{
        return queryClient.clear();
    }
};
const cacheUtils = {
    /**
   * Update product data in cache after mutations
   * Updates both the specific product detail and invalidates product lists
   * @param productId - The ID of the product to update
   * @param updater - Function to update the product data
   */ updateProduct: (productId, updater)=>{
        queryClient.setQueryData([
            'products',
            'detail',
            productId
        ], updater);
        // Also invalidate product lists to ensure consistency
        queryClient.invalidateQueries({
            queryKey: [
                'products'
            ]
        });
    },
    /**
   * Update cart data in cache
   * Ensures cart data is consistent across the application
   * @param updater - Function to update the cart data
   */ updateCart: (updater)=>{
        queryClient.setQueryData([
            'cart'
        ], updater);
    },
    /**
   * Update simple cart data in cache
   * Used for quick cart previews and header cart displays
   * @param updater - Function to update the simple cart data
   */ updateSimpleCart: (updater)=>{
        queryClient.setQueryData([
            'simple_cart'
        ], updater);
    },
    /**
   * Add item to wishlist cache
   * Optimistically updates the wishlist cache
   * @param item - The item to add to the wishlist
   */ addToWishlist: (item)=>{
        queryClient.setQueryData([
            'wishlist_items'
        ], (old)=>{
            if (!old) return [
                item
            ];
            // Avoid duplicates
            const exists = old.some((existingItem)=>existingItem.id === item.id);
            if (exists) return old;
            return [
                ...old,
                item
            ];
        });
    },
    /**
   * Remove item from wishlist cache
   * Optimistically updates the wishlist cache
   * @param itemId - The ID of the item to remove from wishlist
   */ removeFromWishlist: (itemId)=>{
        queryClient.setQueryData([
            'wishlist_items'
        ], (old)=>{
            if (!old) return [];
            return old.filter((item)=>item.id !== itemId);
        });
    },
    /**
   * Update customer data in cache
   * Ensures customer data is consistent across the application
   * @param updater - Function to update the customer data
   */ updateCustomer: (updater)=>{
        queryClient.setQueryData([
            'customer_details'
        ], updater);
    },
    /**
   * Update customer addresses in cache
   * Ensures address data is consistent across the application
   * @param updater - Function to update the customer addresses
   */ updateCustomerAddresses: (updater)=>{
        queryClient.setQueryData([
            'customer_addresses'
        ], updater);
    },
    /**
   * Update order data in cache
   * Ensures order data is consistent across the application
   * @param updater - Function to update the order data
   */ updateOrder: (updater)=>{
        queryClient.setQueryData([
            'orders'
        ], updater);
    },
    /**
   * Update order items in cache
   * Ensures order item data is consistent across the application
   * @param updater - Function to update the order items
   */ updateOrderItems: (updater)=>{
        queryClient.setQueryData([
            'order_items'
        ], updater);
    }
};
}),
"[project]/src/providers/react-query-provider.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>ReactQueryProvider
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function ReactQueryProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"],
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {}, void 0, false, {
                fileName: "[project]/src/providers/react-query-provider.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/providers/react-query-provider.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        isLoggedIn: false,
        username: null,
        regInitiated: false,
        verificationCodeSubmitted: false,
        passwordSubmitted: false,
        customerDetailsSubmitted: false,
        updateAuthInfoSubmitted: false,
        // altUsername: null,
        authInfoVerifyCodeSubmitted: false,
        setIsLoggedIn: (isLoggedIn)=>{
            set({
                isLoggedIn
            });
        },
        // logout: () => {
        //   set({ isLoggedIn: false })
        // },
        setUsername: (username)=>{
            set({
                username
            });
        },
        setRegInitiated: (regInitiated)=>{
            set({
                regInitiated
            });
        },
        setVerificationCodeSubmitted: (verificationCodeSubmitted)=>{
            set({
                verificationCodeSubmitted
            });
        },
        setPasswordSubmitted: (passwordSubmitted)=>{
            set({
                passwordSubmitted
            });
        },
        setCustomerDetailsSubmitted: (customerDetailsSubmitted)=>{
            set({
                customerDetailsSubmitted
            });
        },
        setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted)=>{
            set({
                updateAuthInfoSubmitted
            });
        },
        // setAltUsername: (altUsername: string | null) => {
        //   set({ altUsername })
        // },
        setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted)=>{
            set({
                authInfoVerifyCodeSubmitted
            });
        }
    }), {
    name: 'auth_store',
    partialize: (state)=>({
            isLoggedIn: state.isLoggedIn
        })
})));
const __TURBOPACK__default__export__ = authStore;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/src/lib/api-client.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000/api"),
    withCredentials: true
});
class APIClient {
    endpoint;
    constructor(endpoint){
        this.endpoint = endpoint;
    }
    get = async (config)=>{
        try {
            const response = await axiosInstance.get(this.endpoint, config);
            return response.data;
        } catch (error) {
            this.handleError(error);
            // Add a return statement to ensure a return value in case of an error
            throw new Error(error.message);
        }
    };
    // This is for paginated responses from the server
    getAll = async (config)=>{
        try {
            const response = await axiosInstance.get(this.endpoint, config);
            console.log(`${response.config.url}`);
            return response.data;
        } catch (error) {
            this.handleError(error);
            // Add a return statement to ensure a return value in case of an error
            throw new Error(error.message);
        }
    };
    post = async (data, config)=>{
        try {
            const response = await axiosInstance.post(this.endpoint, data, config);
            return response.data;
        } catch (error) {
            this.handleError(error);
            // Add a return statement to ensure a return value in case of an error
            throw new Error(error.message);
        }
    };
    patch = async (data, config)=>{
        try {
            const response = await axiosInstance.patch(this.endpoint, data, config);
            return response.data;
        } catch (error) {
            this.handleError(error);
            throw new Error(error.message);
        }
    };
    delete = async (itemId)=>{
        try {
            const response = await axiosInstance.delete(`${this.endpoint}/${itemId}/`);
            return response.data;
        } catch (error) {
            this.handleError(error);
            // Add a return statement to ensure a return value in case of an error
            throw new Error(error.message);
        }
    };
    handleError = (error)=>{
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
            // ✅ Don't log cancelled requests as errors
            if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
                // This is a cancelled request, don't log it as an error
                throw error;
            }
            // Don't log expected authentication errors to reduce console noise
            const isAuthError = error.response?.status === 401 || error.response?.status === 403;
            if (!isAuthError) {
                console.error("Error message: ", error.message);
                if (error.response) {
                    console.error("Response data: ", error.response.data);
                    console.error("Response status: ", error.response.status);
                } else if (error.request) {
                    console.error("Request data: ", error.request);
                } else {
                    console.error("Error config: ", error.config);
                }
            }
            throw {
                message: error.message,
                response: error.response
            };
        } else {
            console.error("Error: ", error);
        }
        throw error;
    };
}
const __TURBOPACK__default__export__ = APIClient;
}),
"[project]/src/constants/constants.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Cart
__turbopack_context__.s([
    "CACHE_KEY_CART_ITEMS",
    ()=>CACHE_KEY_CART_ITEMS,
    "CACHE_KEY_ORDERS",
    ()=>CACHE_KEY_ORDERS,
    "CACHE_KEY_ORDERS_ADMIN",
    ()=>CACHE_KEY_ORDERS_ADMIN,
    "CACHE_KEY_ORDER_ITEMS",
    ()=>CACHE_KEY_ORDER_ITEMS,
    "CACHE_KEY_PRODUCTS",
    ()=>CACHE_KEY_PRODUCTS,
    "CUSTOMER_ADDRESSES",
    ()=>CUSTOMER_ADDRESSES,
    "CUSTOMER_DETAILS",
    ()=>CUSTOMER_DETAILS,
    "ITEMS_PER_PAGE",
    ()=>ITEMS_PER_PAGE,
    "SIMPLE_CART",
    ()=>SIMPLE_CART,
    "WISHLIST_ITEMS",
    ()=>WISHLIST_ITEMS
]);
const CACHE_KEY_CART_ITEMS = 'cart_items';
const SIMPLE_CART = 'simple_cart';
const CACHE_KEY_ORDER_ITEMS = 'order_items';
const CACHE_KEY_ORDERS = 'orders';
const CACHE_KEY_ORDERS_ADMIN = 'orders_admin';
const CUSTOMER_DETAILS = 'customer_details';
const CUSTOMER_ADDRESSES = 'customer_addresses';
const CACHE_KEY_PRODUCTS = 'products';
const WISHLIST_ITEMS = 'wishlist_items';
const ITEMS_PER_PAGE = Number(("TURBOPACK compile-time value", "2"));
}),
"[project]/src/hooks/auth-hooks.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useChangeAuthInfo",
    ()=>useChangeAuthInfo,
    "useChangePassword",
    ()=>useChangePassword,
    "useInitResetPassword",
    ()=>useInitResetPassword,
    "useLogin",
    ()=>useLogin,
    "useLogout",
    ()=>useLogout,
    "useRegister",
    ()=>useRegister,
    "useResetPasswordConfirm",
    ()=>useResetPasswordConfirm,
    "useSendAuthInfoVerifyCode",
    ()=>useSendAuthInfoVerifyCode,
    "useSendUpdateAuthInfo",
    ()=>useSendUpdateAuthInfo,
    "useSendVerifyCode",
    ()=>useSendVerifyCode,
    "useSendVerifyRegCredentials",
    ()=>useSendVerifyRegCredentials,
    "useSetPassword",
    ()=>useSetPassword
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-ssr] (ecmascript)");
;
;
;
;
const useRegister = ()=>{
    const { setUsername } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    // In AuthClient Response has defined as: Request = Response 
    // If request data is different do not forget to specify the types here. 
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/register/initiate/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data),
        onSuccess: (data)=>{
            console.log(data);
            setUsername(data.username);
        }
    });
    return {
        mutation
    };
};
const useSendVerifyRegCredentials = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/auth/register/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
const useSetPassword = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/register/set-password/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
const useSendUpdateAuthInfo = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/update/');
    // const { setAltUsername } = authStore()
    const authInfoMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.patch(data)
    });
    return {
        authInfoMutation
    };
};
const useSendVerifyCode = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
const useLogin = ()=>{
    const { setIsLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/login/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data),
        onSuccess: ()=>{
            // const { access } = data.data
            setIsLoggedIn(true);
        }
    });
    return {
        mutation
    };
};
const useLogout = ()=>{
    const { setIsLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/logout/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>apiClient.post(),
        onSuccess: ()=>{
            console.log('Logout was success');
            setIsLoggedIn(false);
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                ]
            });
            // queryClient.refetchQueries({
            //   queryKey: [CUSTOMER_DETAILS]
            // })
            queryClient.removeQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                ]
            });
        }
    });
    return {
        mutation
    };
};
const useChangePassword = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/password/change/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
const useInitResetPassword = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/password/reset/request/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (newPassword)=>apiClient.post(newPassword)
    });
    return {
        mutation
    };
};
const useResetPasswordConfirm = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/auth/password/reset/confirm/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
const useChangeAuthInfo = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`auth/profile/contact/update/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.patch(data),
        onSuccess: ()=>{
        // const { access } = data.data
        // login()
        }
    });
    return {
        mutation
    };
};
const useSendAuthInfoVerifyCode = ()=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/auth/profile/contact/verify/');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data)
    });
    return {
        mutation
    };
};
}),
"[project]/src/stores/cart-store.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
;
// {
//   title: title,
//   product_image: `https://res.cloudinary.com/dev-kani/${item.product_image[0]?.image}`,
//   quantity: 1,
//   price: item.price,
//   sku: item.sku
// }
const cartStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        cartId: null,
        selectedVariant: null,
        cartItem: {
            id: null,
            product_id: null,
            product_variant: null,
            quantity: null,
            extra_data: {}
        },
        customer: null,
        selectedAddress: null,
        // paymentOptions: [], // unused
        selectedPaymentOption: null,
        // Selection state (persist-safe)
        selectedItemIds: [],
        selectAllItems: false,
        setCustomer: (customer)=>set({
                customer: customer
            }),
        setSelectedAddress: (address)=>set({
                selectedAddress: address
            }),
        // setPaymentOptions: (option) => set({ paymentOptions: option }),
        setSelectedPaymentOption: (paymentOption)=>set({
                selectedPaymentOption: paymentOption
            }),
        setCartId: (newCartId)=>set({
                cartId: newCartId
            }),
        // setCartItems: (cartItemData) => set({ selectedVariant: cartItemData }),
        setCartItemId: (id)=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        id
                    }
                })),
        // setProductId: (product_id) => set((state) => ({
        //   cartItems: { ...state.cartItems, product_id }
        // })),
        // setProductVariant: (product_variant) => set((state) => ({
        //   cartItems: { ...state.cartItems, product_variant },
        // })),
        setProductVariant: (product_variant)=>set({
                selectedVariant: product_variant
            }),
        // setQuantity: (quantity) => set((state) => ({
        //   cartItems: { ...state.cartItems, quantity }
        // })),
        setExtraData: (extra_data)=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        extra_data
                    }
                })),
        resetExtraData: ()=>set((state)=>({
                    cartItem: {
                        ...state.cartItem,
                        extra_data: {}
                    }
                })),
        // Selection methods using serializable array
        toggleItemSelection: (itemId)=>set((state)=>{
                const current = Array.isArray(state.selectedItemIds) ? state.selectedItemIds : [];
                const setCopy = new Set(current);
                if (setCopy.has(itemId)) setCopy.delete(itemId);
                else setCopy.add(itemId);
                return {
                    selectedItemIds: Array.from(setCopy)
                };
            }),
        toggleSelectAll: (allItemIds)=>set((state)=>{
                const current = Array.isArray(state.selectedItemIds) ? state.selectedItemIds : [];
                const allSelected = allItemIds.every((id)=>current.includes(id));
                if (allSelected) {
                    // Deselect all
                    return {
                        selectedItemIds: [],
                        selectAllItems: false
                    };
                } else {
                    // Select all
                    return {
                        selectedItemIds: allItemIds,
                        selectAllItems: true
                    };
                }
            }),
        clearSelection: ()=>set({
                selectedItemIds: [],
                selectAllItems: false
            }),
        setSelectedItems: (itemIds)=>set({
                selectedItemIds: itemIds
            }),
        isItemSelected: (itemId)=>{
            const state = get();
            return Array.isArray(state.selectedItemIds) ? state.selectedItemIds.includes(itemId) : false;
        }
    }), {
    name: 'cart_store',
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
    version: 1,
    migrate: (persistedState, _ver)=>{
        // reference param to satisfy linter
        void _ver;
        // Migrate old Set-based `selectedCartItems` if present
        if (!persistedState || typeof persistedState !== 'object') return persistedState;
        const ps = persistedState;
        if (ps.selectedCartItems) {
            const old = ps.selectedCartItems;
            let selectedItemIds;
            if (Array.isArray(old)) selectedItemIds = old;
            else if (typeof old === 'object' && old !== null && Array.isArray(old.data)) {
                selectedItemIds = old.data.map((v)=>Number(v));
            } else if (Symbol.iterator in Object(old)) {
                selectedItemIds = Array.from(old);
            } else {
                selectedItemIds = [];
            }
            return {
                ...ps,
                selectedItemIds
            };
        }
        return ps;
    },
    partialize: (state)=>({
            cartId: state.cartId,
            selectedItemIds: state.selectedItemIds
        })
}));
const __TURBOPACK__default__export__ = cartStore;
}),
"[project]/src/hooks/cart-hooks.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useAddToCart",
    ()=>useAddToCart,
    "useCart",
    ()=>useCart,
    "useDeleteCartItem",
    ()=>useDeleteCartItem,
    "useShippingCalculation",
    ()=>useShippingCalculation,
    "useSimpleCart",
    ()=>useSimpleCart,
    "useUpdateCart",
    ()=>useUpdateCart,
    "useUpdateCartCustomer",
    ()=>useUpdateCartCustomer
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-ssr] (ecmascript)");
;
;
;
;
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/cart/');
const useAddToCart = (product, qty)=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { cartId, setCartId, setCartItemId, selectedVariant, cartItem } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const { extra_data } = cartItem;
    const apiClient2 = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/items/`);
    console.log(cartItem);
    console.log(extra_data);
    const addToCartMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ product_id, product_variant, quantity, extra_data })=>apiClient2.post({
                // cart_id,
                product_id,
                product_variant: product_variant,
                quantity,
                extra_data
            }),
        onSuccess: (data)=>{
            setCartItemId(parseInt(data.id));
            // Only invalidate simple cart cache to avoid triggering expensive useCart queries
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"]
                ]
            });
        },
        onError: (error)=>{
            console.error('Error adding item to cart:', error);
        }
    });
    const createCartMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>apiClient.post({}),
        onSuccess: (data)=>{
            if (!selectedVariant) {
                return;
            }
            console.log('cart id (res) in createCartMutation', data);
            setCartId(data.id);
            addToCartMutation.mutate({
                // cart_id: data.id,
                product_id: product.id,
                product_variant: selectedVariant.id,
                quantity: qty,
                extra_data: extra_data
            });
            // Only invalidate simple cart cache to avoid triggering expensive useCart queries
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"]
                ]
            });
        },
        onError: (error)=>{
            console.error('Error creating cart:', error);
        }
    });
    const handleAddToCart = ()=>{
        if (!selectedVariant) {
            return; // Early return if no variant selected
        }
        if (cartId) {
            addToCartMutation.mutate({
                // cart_id: cartId,
                product_id: product.id,
                product_variant: selectedVariant.id,
                quantity: qty,
                extra_data: extra_data
            });
        } else {
            createCartMutation.mutate({});
        }
    };
    return {
        handleAddToCart,
        isPending: addToCartMutation.isPending || createCartMutation.isPending,
        error: addToCartMutation.error || createCartMutation.error
    };
};
const useCart = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
            cartId
        ],
        queryFn: apiClient.get,
        enabled: !!cartId,
        // keepPreviousData: true,
        // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
        // initialData:  Here we can add categories as static data
        // refetchOnMount: true,
        staleTime: 0
    });
};
const useSimpleCart = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/simple`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"],
            cartId
        ],
        queryFn: apiClient.get,
        enabled: !!cartId
    });
};
const useDeleteCartItem = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/items`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (itemId)=>apiClient.delete(itemId),
        // onMutate: async (itemId) => {
        //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)
        //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)
        //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
        //     const updatedCartItems = oldData?.cart_items.filter(
        //       (item) => item.id !== itemId
        //     )
        //     return { ...oldData, cart_items: updatedCartItems }
        //   })
        //   return { previousCartItems }
        // },
        onSuccess: ()=>{
            // Invalidate both regular cart and simple cart queries
            // queryClient.invalidateQueries({
            //   queryKey: [CACHE_KEY_CART_ITEMS],
            // })
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"]
                ]
            });
        }
    });
// const handleDeleteCartItem = (itemId) => {
//   mutate({ itemId })
// }
// return { isPending, isError, mutate }
};
const useUpdateCart = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const handleQuantityUpdate = (itemId, newQuantity)=>{
        const cartItemApiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/items/${itemId}/`) // Item-specific endpoint
        ;
        mutation.mutate({
            itemId,
            newQuantity,
            apiClient: cartItemApiClient
        });
    };
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ newQuantity, apiClient })=>{
            // Using the specific APIClient instance for this item
            return apiClient.patch({
                quantity: newQuantity
            });
        },
        onSuccess: ()=>{
            // Invalidate both regular cart and simple cart queries
            // queryClient.invalidateQueries({
            //   queryKey: [CACHE_KEY_CART_ITEMS],
            // })
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"]
                ]
            });
        }
    });
    return {
        mutation,
        handleQuantityUpdate
    };
};
const useUpdateCartCustomer = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>{
            // Send PATCH request to update cart with customer
            return apiClient.patch({});
        },
        onSuccess: ()=>{
            // Invalidate both regular cart and simple cart queries
            // queryClient.invalidateQueries({
            //   queryKey: [CACHE_KEY_CART_ITEMS],
            // })
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIMPLE_CART"]
                ]
            });
        },
        onError: (error)=>{
            console.error('Error updating cart customer:', error);
        }
    });
    return {
        updateCartCustomer: mutation.mutate,
        isPending: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
};
const useShippingCalculation = ()=>{
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/cart/${cartId}/items/calculate_shipping/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>apiClient.post(data),
        onSuccess: ()=>{
        // No need to invalidate cache since we use the response data directly
        // The shipping calculation response already contains the updated cart data
        },
        onError: (error)=>{
            console.error('Error calculating shipping:', error);
        }
    });
    return {
        calculateShipping: mutation.mutate,
        isPending: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess,
        data: mutation.data
    };
};
}),
"[project]/src/hooks/customer-hooks.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useCustomerDetails",
    ()=>useCustomerDetails,
    "useUpdateCustomer",
    ()=>useUpdateCustomer
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
;
;
;
const useCustomerDetails = (enabled = true)=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/customers/me/');
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
        ],
        queryFn: ()=>apiClient.get(),
        enabled
    });
};
const useUpdateCustomer = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/customers/me/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (customerData)=>apiClient.patch(customerData),
        onSuccess: ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CUSTOMER_DETAILS"]
                ]
            });
        }
    });
    return {
        mutation
    };
};
}),
"[project]/src/hooks/wishlist-hooks.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useDeleteWishlistItem",
    ()=>useDeleteWishlistItem,
    "useToggleWishlist",
    ()=>useToggleWishlist,
    "useWishlist",
    ()=>useWishlist
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-ssr] (ecmascript)");
;
;
;
const useWishlist = (page, enabled = true)=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/wishlist/`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"],
            page
        ],
        queryFn: ()=>apiClient.getAll({
                params: {
                    page: page
                }
            }),
        enabled
    });
};
const useToggleWishlist = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/wishlist/toggle_product/`);
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (productId)=>apiClient.post({
                "product_id": `${productId}`
            }),
        // onMutate: async (itemId) => {
        //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)
        //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)
        //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
        //     const updatedCartItems = oldData?.cart_items.filter(
        //       (item) => item.id !== itemId
        //     )
        //     return { ...oldData, cart_items: updatedCartItems }
        //   })
        //   return { previousCartItems }
        // },
        onSuccess: ()=>{
            // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"]
                ]
            });
        }
    });
    // const handleDeleteCartItem = (itemId) => {
    //   mutate({ itemId })
    // }
    // return { isPending, isError, mutate }
    return mutation;
};
const useDeleteWishlistItem = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/wishlist');
    const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (itemId)=>{
            // This will correctly construct the URL as /wishlist/123/
            return apiClient.delete(itemId);
        },
        onSuccess: ()=>{
            // Invalidate the wishlist query to refetch the latest data
            queryClient.invalidateQueries({
                queryKey: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WISHLIST_ITEMS"]
                ]
            });
        },
        onError: (error)=>{
            // Optional: Add error handling
            console.error('Failed to delete wishlist item:', error);
        }
    });
    return mutation;
};
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/components/utils/logo/Logo.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "logo": "Logo-module-scss-module__VZ4g6W__logo",
});
}),
"[project]/src/components/utils/logo/Logo.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.module.scss [app-ssr] (css module)");
;
;
;
const Logo = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        href: "/",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                children: "PC"
            }, void 0, false, {
                fileName: "[project]/src/components/utils/logo/Logo.tsx",
                lineNumber: 9,
                columnNumber: 6
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "House"
            }, void 0, false, {
                fileName: "[project]/src/components/utils/logo/Logo.tsx",
                lineNumber: 9,
                columnNumber: 17
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/utils/logo/Logo.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Logo;
}),
"[project]/src/components/header/navbar/navigation-card/NavigationCard.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "level-0": "NavigationCard-module-scss-module__8oXgDG__level-0",
  "level-1": "NavigationCard-module-scss-module__8oXgDG__level-1",
  "level-2": "NavigationCard-module-scss-module__8oXgDG__level-2",
  "level-3": "NavigationCard-module-scss-module__8oXgDG__level-3",
  "list": "NavigationCard-module-scss-module__8oXgDG__list",
  "nav__card": "NavigationCard-module-scss-module__8oXgDG__nav__card",
  "slideDown": "NavigationCard-module-scss-module__8oXgDG__slideDown",
});
}),
"[project]/src/components/utils/alert/Alert.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "alert": "Alert-module-scss-module__QYFb8W__alert",
  "error": "Alert-module-scss-module__QYFb8W__error",
  "highlight": "Alert-module-scss-module__QYFb8W__highlight",
  "info": "Alert-module-scss-module__QYFb8W__info",
  "success": "Alert-module-scss-module__QYFb8W__success",
  "warning": "Alert-module-scss-module__QYFb8W__warning",
});
}),
"[project]/src/components/utils/alert/Alert.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.module.scss [app-ssr] (css module)");
;
;
const Alert = ({ variant, message, textSize, textAlign = 'left', highlightWords = [] })=>{
    const escapeRegExp = (string)=>{
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special characters
        ;
    };
    const getHighlightedMessage = (text)=>{
        if (!highlightWords.length) {
            return text;
        }
        // Escape each word in the highlightWords array
        const escapedHighlightWords = highlightWords.map((word)=>escapeRegExp(word));
        const parts = text.split(new RegExp(`(${escapedHighlightWords.join('|')})`, 'gi'));
        return parts.map((part, index)=>highlightWords.some((word)=>word.toLowerCase() === part.toLowerCase()) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].highlight,
                children: part
            }, index, false, {
                fileName: "[project]/src/components/utils/alert/Alert.tsx",
                lineNumber: 28,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0)) : part);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"][variant]} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].alert}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            style: {
                fontSize: `${textSize}px`,
                textAlign: textAlign
            },
            children: getHighlightedMessage(message)
        }, void 0, false, {
            fileName: "[project]/src/components/utils/alert/Alert.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/utils/alert/Alert.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Alert;
}),
"[project]/src/components/utils/getErrorMessage.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getErrorMessage",
    ()=>getErrorMessage
]);
const getErrorMessage = (error)=>{
    if (error?.response?.data) {
        return Object.values(error.response.data).flat().join(', ');
    }
    return error?.message;
};
}),
"[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/navigation-card/NavigationCard.module.scss [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$getErrorMessage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/getErrorMessage.ts [app-ssr] (ecmascript)");
;
;
;
;
;
// Utility function to build a hierarchical structure of categories
const buildHierarchy = (categories)=>{
    const categoryMap = new Map() // Map to hold categories by their IDs
    ;
    // Initialize the map with empty children arrays
    categories.forEach((cat)=>categoryMap.set(cat.id, {
            ...cat,
            children: []
        }));
    const rootCategories = [] // Array to hold top-level categories
    ;
    categories.forEach((cat)=>{
        if (cat.parent === null) {
            rootCategories.push(categoryMap.get(cat.id)); // Add top-level categories (parent is null) to rootCategories
        } else {
            const parentCategory = categoryMap.get(cat.parent);
            if (parentCategory) {
                parentCategory.children.push(categoryMap.get(cat.id)); // Add the current category to its parent's children array
            }
        }
    });
    return rootCategories // Return the hierarchical structure
    ;
};
// Main functional component for the navigation card
const NavigationCard = ({ isPending, error, categories, setIsOpen })=>{
    const hierarchicalCategories = buildHierarchy(categories) // Build hierarchical structure of categories
    ;
    // Recursive function to render categories and their children
    const renderCategories = (items, level = 0)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].list} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"][`level-${level}`]}`,
            children: items.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: `/products/category/${item.slug}`,
                            onClick: ()=>setIsOpen(false),
                            children: [
                                item.title,
                                " "
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
                            lineNumber: 53,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        item.children && item.children.length > 0 && renderCategories(item.children, level + 1)
                    ]
                }, item.id, true, {
                    fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)))
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 49,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        onMouseLeave: ()=>setIsOpen(false),
        onMouseOver: ()=>setIsOpen(true),
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].nav__card} container`,
        children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 73,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            variant: "error",
            message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$getErrorMessage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorMessage"])(error)
        }, void 0, false, {
            fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
            lineNumber: 75,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : renderCategories(hierarchicalCategories)
    }, void 0, false, {
        fileName: "[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx",
        lineNumber: 67,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = NavigationCard;
}),
"[project]/src/stores/filter-store.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Zustand store (filterStore.ts)
__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const filterStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        productTypeId: 0,
        currentCategory: '',
        categoryProductTypeMap: {},
        selectedFilters: {},
        setProductTypeId: (proType)=>set({
                productTypeId: proType
            }),
        setCurrentCategory: (category)=>{
            const state = get();
            if (state.currentCategory !== category) {
                // Category changed, reset filters and update current category
                const cachedProductTypeId = state.categoryProductTypeMap[category];
                set({
                    currentCategory: category,
                    selectedFilters: {},
                    // Only set productTypeId if we have cached data, otherwise keep current value
                    ...cachedProductTypeId && {
                        productTypeId: cachedProductTypeId
                    }
                });
            }
        },
        setCategoryProductType: (category, productTypeId)=>{
            const state = get();
            // Only update if the productTypeId is different to avoid unnecessary re-renders
            if (state.categoryProductTypeMap[category] !== productTypeId || state.productTypeId !== productTypeId) {
                set({
                    categoryProductTypeMap: {
                        ...state.categoryProductTypeMap,
                        [category]: productTypeId
                    },
                    productTypeId: productTypeId
                });
            }
        },
        updateFilter: (filterName, filterValue)=>set((state)=>({
                    selectedFilters: {
                        ...state.selectedFilters,
                        [filterName]: filterValue
                    }
                })),
        resetFilters: ()=>set({
                selectedFilters: {}
            }),
        shouldFetchFilters: (category)=>{
            const state = get();
            // Only fetch filters if we don't have cached productTypeId for this category
            return !state.categoryProductTypeMap[category];
        }
    }), {
    name: 'filter-store',
    partialize: (state)=>({
            categoryProductTypeMap: state.categoryProductTypeMap
        })
}));
const __TURBOPACK__default__export__ = filterStore;
}),
"[project]/src/hooks/product-hooks.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useCategories",
    ()=>useCategories,
    "useProductFilterOptions",
    ()=>useProductFilterOptions,
    "useProducts",
    ()=>useProducts
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$filter$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/filter-store.ts [app-ssr] (ecmascript)");
;
;
;
;
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/products/categories/');
const useCategories = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'categories'
        ],
        queryFn: ({ signal })=>apiClient.get({
                signal
            }),
        staleTime: 24 * 60 * 60 * 1000,
        // initialData:  Here we can add categories as static data
        // cacheTime: 1000 * 60 * 60, <-- This is wrong!
        gcTime: 1000 * 60 * 60,
        retry: (failureCount, error)=>{
            // ✅ Don't retry on cancelled requests
            if (error.name === 'CanceledError' || error.message === 'canceled') {
                return false;
            }
            return failureCount < 3;
        }
    });
const useProducts = (slug, page, queryString, searchQuery = '')=>{
    const { selectedFilters } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$filter$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    // Initialize API client based on whether searchQuery exists or not
    const apiClient = searchQuery ? new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/products/?search=${searchQuery}`) : new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](`/products/category/${slug}/`);
    const queryParams = new URLSearchParams(queryString);
    // Add filters to the query parameters
    Object.entries(selectedFilters).forEach(([key, value])=>{
        if (key === 'price_range' && Array.isArray(value)) {
            queryParams.set('min_price', value[0].toString());
            queryParams.set('max_price', value[1].toString());
        } else if (Array.isArray(value)) {
            value.forEach((val)=>queryParams.append(key, val.toString()));
        } else {
            queryParams.set(key, value.toString());
        }
    });
    // If searchQuery exists, add it to queryParams
    if (searchQuery) {
        queryParams.set('search', searchQuery);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_KEY_PRODUCTS"],
            slug,
            selectedFilters,
            page,
            queryString,
            searchQuery
        ],
        queryFn: ()=>apiClient.getAll({
                params: Object.fromEntries(queryParams)
            })
    });
};
const useProductFilterOptions = (productTypeId)=>{
    const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('/products/product-filter-options/');
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'filterOptions',
            productTypeId
        ],
        queryFn: ()=>apiClient.get({
                params: {
                    product_type_id: productTypeId
                }
            }),
        enabled: !!productTypeId && productTypeId > 0,
        staleTime: 1000 * 60 * 60 * 24,
        gcTime: 1000 * 60 * 60 * 24,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        retry: 1
    });
};
}),
"[project]/src/components/header/navbar/Navbar.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "navbar__links": "Navbar-module-scss-module__deXsXG__navbar__links",
});
}),
"[project]/src/components/header/navbar/Navbar.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa6/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/navigation-card/NavigationCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/product-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/Navbar.module.scss [app-ssr] (css module)");
;
;
;
;
;
;
const Navbar = ()=>{
    const { isPending, error, data: categories = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCategories"])();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navbar,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navbar__links,
                    onMouseOver: ()=>setIsOpen(true),
                    onMouseLeave: ()=>setIsOpen(false),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            children: "All Products"
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                            lineNumber: 20,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaAnglesDown"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                                lineNumber: 21,
                                columnNumber: 14
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                            lineNumber: 21,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                    lineNumber: 15,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$navigation$2d$card$2f$NavigationCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isPending: isPending,
                error: error,
                categories: categories,
                isOpen: isOpen,
                setIsOpen: setIsOpen
            }, void 0, false, {
                fileName: "[project]/src/components/header/navbar/Navbar.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Navbar;
}),
"[project]/src/components/header/search-bar/Search.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "backdrop": "Search-module-scss-module__pWWbXW__backdrop",
  "category_header": "Search-module-scss-module__pWWbXW__category_header",
  "category_item": "Search-module-scss-module__pWWbXW__category_item",
  "category_link": "Search-module-scss-module__pWWbXW__category_link",
  "category_title": "Search-module-scss-module__pWWbXW__category_title",
  "child_categories": "Search-module-scss-module__pWWbXW__child_categories",
  "expand_toggle": "Search-module-scss-module__pWWbXW__expand_toggle",
  "expandable": "Search-module-scss-module__pWWbXW__expandable",
  "expanded": "Search-module-scss-module__pWWbXW__expanded",
  "focused": "Search-module-scss-module__pWWbXW__focused",
  "leaf_category": "Search-module-scss-module__pWWbXW__leaf_category",
  "level_0": "Search-module-scss-module__pWWbXW__level_0",
  "level_1": "Search-module-scss-module__pWWbXW__level_1",
  "level_2": "Search-module-scss-module__pWWbXW__level_2",
  "level_3": "Search-module-scss-module__pWWbXW__level_3",
  "no_suggestions": "Search-module-scss-module__pWWbXW__no_suggestions",
  "parent_category": "Search-module-scss-module__pWWbXW__parent_category",
  "product_count": "Search-module-scss-module__pWWbXW__product_count",
  "search": "Search-module-scss-module__pWWbXW__search",
  "search_suggestions": "Search-module-scss-module__pWWbXW__search_suggestions",
  "slideIn": "Search-module-scss-module__pWWbXW__slideIn",
  "suggestions": "Search-module-scss-module__pWWbXW__suggestions",
});
}),
"[project]/src/components/header/search-bar/Search.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
// navigation via window.location used instead of next/router here
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/search-bar/Search.module.scss [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/product-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
// Utility function to calculate category depth in hierarchy
const calculateCategoryDepth = (categoryId, allCategories, currentDepth = 0)=>{
    const category = allCategories.find((cat)=>cat.id === categoryId);
    if (!category || !category.parent) {
        return currentDepth;
    }
    return calculateCategoryDepth(category.parent, allCategories, currentDepth + 1);
};
// Utility function to enhance categories with new properties
const enhanceCategory = (category, allCategories)=>{
    const children = allCategories.filter((cat)=>cat.parent === category.id);
    const hasChildren = children.length > 0;
    const depth = calculateCategoryDepth(category.id, allCategories);
    return {
        ...category,
        hasChildren,
        depth,
        children: hasChildren ? children.map((child)=>enhanceCategory(child, allCategories)) : undefined
    };
};
const Search = ()=>{
    // Simplified state management
    const [searchValue, setSearchValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [showSuggestions, setShowSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // suggestions are derived later via useMemo after categories and helper definitions
    // navigation via window.location (no router instance needed)
    // Ref to the search container to detect clicks outside of it
    const searchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Fetch all categories using the custom hook `useCategories`
    const { data: categories = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$product$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCategories"])();
    // Enhanced recursive function to find all children for a given category
    const findAllChildrenRecursive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((categoryId, allCategories)=>{
        // Find all categories whose parent matches the given category ID
        const children = allCategories.filter((cat)=>cat.parent === categoryId);
        // For each child, recursively find its own children and enhance them
        return children.map((child)=>{
            const enhancedChild = enhanceCategory(child, allCategories);
            return {
                ...enhancedChild,
                children: findAllChildrenRecursive(child.id, allCategories)
            };
        });
    }, []);
    // Derive suggestions from searchValue + categories without storing in state to avoid update loops
    const suggestedCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!searchValue.trim()) return [];
        const lowerCaseSearchValue = searchValue.toLowerCase();
        const matchedCategories = categories.filter((category)=>category.title.toLowerCase().includes(lowerCaseSearchValue));
        const enhancedCategoriesWithChildren = matchedCategories.map((category)=>{
            const enhanced = enhanceCategory(category, categories);
            return {
                ...enhanced,
                children: findAllChildrenRecursive(category.id, categories),
                matchScore: lowerCaseSearchValue === category.title.toLowerCase() ? 100 : category.title.toLowerCase().startsWith(lowerCaseSearchValue) ? 80 : 50
            };
        });
        return enhancedCategoriesWithChildren.sort((a, b)=>(b.matchScore || 0) - (a.matchScore || 0));
    }, [
        searchValue,
        categories,
        findAllChildrenRecursive
    ]);
    // useEffect hook to update the suggestions list based on the search input
    // suggestions are derived via useMemo above; no setState inside effect required
    // useEffect hook to handle clicks outside the search component
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            // If the click is outside the search box, close the suggestions dropdown
            if (searchRef.current && !searchRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };
        // Add event listener for mouse clicks
        document.addEventListener('mousedown', handleClickOutside);
        // Cleanup the event listener when the component is unmounted
        return ()=>{
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    // Handler for form submission (search submit)
    const handleSearchSubmit = (event)=>{
        event.preventDefault();
        // If the search value is valid (non-empty), navigate to the search results page
        if (searchValue.trim()) {
            // Use native navigation to avoid next/navigation typing issues in this simple case
            const url = `/products/?search=${encodeURIComponent(searchValue)}`;
            window.location.assign(url);
            // Close the suggestions dropdown
            setShowSuggestions(false);
        }
    };
    // Enhanced render function for categories with proper structure
    const renderCategories = (category)=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].category_item} ${category.hasChildren ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].expandable : ''}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].category_header,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: `/products/category/${category.slug}`,
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].category_link} ${category.hasChildren ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].parent_category : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].leaf_category}`,
                        onClick: ()=>setShowSuggestions(false),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].category_title} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"][`level_${Math.min(category.depth, 3)}`]}`,
                            children: category.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/search-bar/Search.tsx",
                            lineNumber: 186,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 177,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/search-bar/Search.tsx",
                    lineNumber: 175,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                category.children && category.children.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].child_categories,
                    children: category.children.map((child)=>renderCategories(child))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/search-bar/Search.tsx",
                    lineNumber: 198,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, category.id, true, {
            fileName: "[project]/src/components/header/search-bar/Search.tsx",
            lineNumber: 169,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].search,
        ref: searchRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSearchSubmit,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        placeholder: "Search...",
                        value: searchValue,
                        onChange: (e)=>setSearchValue(e.target.value),
                        onFocus: ()=>setShowSuggestions(true),
                        autoComplete: "off"
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "submit",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IoMdSearch"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                                lineNumber: 219,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            " "
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 218,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            showSuggestions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].search_suggestions,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].backdrop,
                        onClick: ()=>setShowSuggestions(false)
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 226,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].suggestions,
                        children: suggestedCategories.length > 0 ? suggestedCategories.map((category)=>renderCategories(category)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].no_suggestions,
                            children: "No suggestions found"
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/search-bar/Search.tsx",
                            lineNumber: 236,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/search-bar/Search.tsx",
                        lineNumber: 231,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/header/search-bar/Search.tsx",
                lineNumber: 225,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/header/search-bar/Search.tsx",
        lineNumber: 207,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Search;
}),
"[project]/src/components/header/Header.module.scss [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "active": "Header-module-scss-module__yUQ6Nq__active",
  "cart": "Header-module-scss-module__yUQ6Nq__cart",
  "divider": "Header-module-scss-module__yUQ6Nq__divider",
  "dropdown": "Header-module-scss-module__yUQ6Nq__dropdown",
  "dropdown_container": "Header-module-scss-module__yUQ6Nq__dropdown_container",
  "dropdown_footer": "Header-module-scss-module__yUQ6Nq__dropdown_footer",
  "dropdown_header": "Header-module-scss-module__yUQ6Nq__dropdown_header",
  "dropdown_menu": "Header-module-scss-module__yUQ6Nq__dropdown_menu",
  "header": "Header-module-scss-module__yUQ6Nq__header",
  "header__badge": "Header-module-scss-module__yUQ6Nq__header__badge",
  "header__bottom_nav": "Header-module-scss-module__yUQ6Nq__header__bottom_nav",
  "header__end": "Header-module-scss-module__yUQ6Nq__header__end",
  "header__icon": "Header-module-scss-module__yUQ6Nq__header__icon",
  "header__login": "Header-module-scss-module__yUQ6Nq__header__login",
  "header__login_links": "Header-module-scss-module__yUQ6Nq__header__login_links",
  "header__logo": "Header-module-scss-module__yUQ6Nq__header__logo",
  "header__search": "Header-module-scss-module__yUQ6Nq__header__search",
  "header__sign_in": "Header-module-scss-module__yUQ6Nq__header__sign_in",
  "header__top": "Header-module-scss-module__yUQ6Nq__header__top",
  "menu_item": "Header-module-scss-module__yUQ6Nq__menu_item",
  "warning_link": "Header-module-scss-module__yUQ6Nq__warning_link",
  "wishlist": "Header-module-scss-module__yUQ6Nq__wishlist",
});
}),
"[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/customer-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$wishlist$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/wishlist-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bs/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ri/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/navbar/Navbar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/search-bar/Search.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/header/Header.module.scss [app-ssr] (css module)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { data } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSimpleCart"])();
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const { mutation } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLogout"])();
    const { data: wishlistItems } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$wishlist$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWishlist"])(1, isLoggedIn);
    const { data: customerData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCustomerDetails"])(isLoggedIn);
    const handleLinkClick = (event)=>{
        if (event.target.tagName === 'A') {
            setIsOpen(false);
        }
    };
    const handleLogout = ()=>{
        mutation.mutate();
    };
    const cartItemsQuantity = (cart_items)=>{
        let item_qty = 0;
        if (cart_items) {
            cart_items.forEach((item)=>{
                item_qty += item.quantity;
            });
        }
        return item_qty;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{}, [
        customerData,
        isLoggedIn
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__top} container`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__logo,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 57,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 56,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__search,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$search$2d$bar$2f$Search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 62,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__end,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/account/wishlist",
                                    title: "wishlist",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].wishlist,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__badge,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: wishlistItems?.count || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 73,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 72,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__icon,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaHeart"], {}, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 76,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 75,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 67,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/checkout/cart",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cart,
                                    title: "cart",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__badge,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: cartItemsQuantity(data?.cart_items || [])
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 83,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 82,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__icon,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaCartPlus"], {}, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 86,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 85,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__sign_in,
                                    onMouseEnter: ()=>isLoggedIn && setIsOpen(true),
                                    onMouseLeave: ()=>isLoggedIn && setIsOpen(false),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__login} ${!isLoggedIn ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__login_links : ''}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        !isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/login/",
                                                                    children: "Sign In"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 104,
                                                                    columnNumber: 23
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                ' | ',
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/register/initiate/",
                                                                    children: "Sign Up"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 106,
                                                                    columnNumber: 23
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true),
                                                        isLoggedIn && customerData?.first_name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                "Hello, ",
                                                                customerData.first_name
                                                            ]
                                                        }, void 0, true),
                                                        isLoggedIn && !customerData?.first_name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].warning_link,
                                                            href: "/account/profile",
                                                            children: "!Complete your profile"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 113,
                                                            columnNumber: 21
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 101,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaCaretDown"], {}, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 123,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 122,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 96,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        isOpen && isLoggedIn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_container} ${isOpen ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: handleLinkClick,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_header,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: [
                                                                "Welcome back, ",
                                                                customerData?.first_name || 'User'
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 136,
                                                            columnNumber: 23
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_menu,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MdOutlineAccountCircle"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 143,
                                                                            columnNumber: 27
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 142,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/profile",
                                                                        children: "My Account"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 145,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 141,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BsBox2"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 149,
                                                                            columnNumber: 27
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 148,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/orders",
                                                                        children: "My Orders"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 151,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 147,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].menu_item,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BsBox2"], {}, void 0, false, {
                                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                                            lineNumber: 155,
                                                                            columnNumber: 27
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 154,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        href: "/account/wishlist",
                                                                        children: "My WishList"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 157,
                                                                        columnNumber: 25
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 153,
                                                                columnNumber: 23
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 140,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown_footer,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].menu_item,
                                                            onClick: handleLogout,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RiLogoutCircleRLine"], {}, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 168,
                                                                        columnNumber: 27
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 167,
                                                                    columnNumber: 25
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: "Sign Out"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 170,
                                                                    columnNumber: 25
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 166,
                                                            columnNumber: 23
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 165,
                                                        columnNumber: 21
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 134,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/header/Header.tsx",
                                            lineNumber: 128,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/header/Header.tsx",
                                    lineNumber: 91,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/header/Header.tsx",
                            lineNumber: 66,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 54,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__bottom_nav,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$module$2e$scss__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header__bottom} container`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$navbar$2f$Navbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 182,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/header/Header.tsx",
                lineNumber: 180,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Header;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__e6200dd4._.js.map