from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.exceptions import ValidationError

User = get_user_model()


class StaffProfile(models.Model):
    """
    Extended profile for staff users with organizational information
    """
    DEPARTMENT_CHOICES = [
        ('PRODUCT', 'Product Management'),
        ('ORDER', 'Order Management'),
        ('CUSTOMER', 'Customer Management'),
        ('CONTENT', 'Content Management'),
        ('FINANCE', 'Finance & Analytics'),
        ('ADMIN', 'Administration'),
        ('IT', 'Information Technology'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('ON_LEAVE', 'On Leave'),
        ('TERMINATED', 'Terminated'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='staff_profile'
    )
    employee_id = models.Char<PERSON>ield(
        max_length=20,
        unique=True,
        editable=False,
        help_text="System-generated unique employee ID"
    )
    department = models.CharField(
        max_length=20,
        choices=DEPARTMENT_CHOICES,
        help_text="Primary department"
    )
    position_title = models.CharField(
        max_length=100,
        help_text="Official job title"
    )
    manager = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='direct_reports',
        help_text="Direct manager/supervisor"
    )
    hire_date = models.DateField(
        help_text="Date of hire"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='ACTIVE'
    )
    notes = models.TextField(
        blank=True,
        help_text="Administrative notes about the staff member"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['department', 'status']),
            models.Index(fields=['manager', 'status']),
            models.Index(fields=['employee_id']),
        ]

    def __str__(self):
        return f"{self.employee_id} - {self.user.email} ({self.position_title})"

    def clean(self):
        """Validate staff profile data"""
        if self.manager and self.manager.user == self.user:
            raise ValidationError("Staff member cannot be their own manager")

        if not self.user.is_staff:
            raise ValidationError("User must have staff status to have a staff profile")

        # Check for circular management dependencies
        if self.manager:
            self._check_circular_management(self.manager)

    def _check_circular_management(self, manager, visited=None):
        """
        Recursively check for circular management chains
        Prevents scenarios like: A manages B, B manages C, C manages A
        """
        if visited is None:
            visited = set()

        if manager.id in visited:
            raise ValidationError(
                f"Circular management dependency detected. "
                f"Setting {manager.full_name} as manager would create a circular chain."
            )

        visited.add(manager.id)

        # If the potential manager has a manager, check recursively
        if manager.manager:
            self._check_circular_management(manager.manager, visited)

        # Check if any of the current user's direct reports would create a cycle
        if hasattr(self, 'id') and self.id:
            current_reports = StaffProfile.objects.filter(manager=self, status='ACTIVE')
            for report in current_reports:
                if report.id == manager.id:
                    raise ValidationError(
                        f"Cannot set {manager.full_name} as manager because they are "
                        f"currently a direct report of this staff member."
                    )

    @property
    def full_name(self):
        """Get full name from customer profile if available"""
        try:
            customer = self.user.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except:
            return self.user.email.split('@')[0]

    @property
    def is_manager(self):
        """Check if this staff member manages others"""
        return self.direct_reports.filter(status='ACTIVE').exists()

    @property
    def team_size(self):
        """Get number of direct reports"""
        return self.direct_reports.filter(status='ACTIVE').count()

    def get_management_chain(self):
        """
        Get the complete management chain from this staff member up to the top
        Returns a list of StaffProfile objects representing the chain
        """
        chain = []
        current = self.manager
        visited = set()

        while current and current.id not in visited:
            chain.append(current)
            visited.add(current.id)
            current = current.manager

            # Safety check to prevent infinite loops
            if len(chain) > 10:  # Reasonable max depth
                break

        return chain

    def get_all_subordinates(self):
        """
        Get all subordinates (direct and indirect) under this staff member
        Returns a queryset of StaffProfile objects
        """
        subordinates = set()

        def collect_subordinates(manager):
            direct_reports = StaffProfile.objects.filter(
                manager=manager,
                status='ACTIVE'
            )
            for report in direct_reports:
                if report.id not in subordinates:
                    subordinates.add(report.id)
                    collect_subordinates(report)

        collect_subordinates(self)

        return StaffProfile.objects.filter(id__in=subordinates)


class Role(Group):
    """
    Proxy model for Django's Group model to provide role-specific functionality
    Makes roles feel like first-class entities with enhanced methods
    """

    class Meta:
        proxy = True
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def __str__(self):
        return self.name

    def get_permissions(self):
        """Get all permissions for this role"""
        return self.permissions.all()

    def get_permission_codenames(self):
        """Get permission codenames as a list"""
        return list(self.permissions.values_list('codename', flat=True))

    def has_permission(self, permission_codename):
        """Check if role has a specific permission"""
        return self.permissions.filter(codename=permission_codename).exists()

    def get_users(self):
        """Get all active users with this role"""
        return self.user_set.filter(is_active=True)

    def get_staff_users(self):
        """Get all active staff users with this role"""
        return self.user_set.filter(is_active=True, is_staff=True)

    @property
    def member_count(self):
        """Total active members"""
        return self.user_set.filter(is_active=True).count()

    @property
    def staff_member_count(self):
        """Active staff members"""
        return self.user_set.filter(is_active=True, is_staff=True).count()

    @property
    def permission_count(self):
        """Total permissions assigned to this role"""
        return self.permissions.count()

    def add_permission(self, permission_codename):
        """Add a permission to this role by codename"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.permissions.add(permission)
            return True
        except Permission.DoesNotExist:
            return False

    def remove_permission(self, permission_codename):
        """Remove a permission from this role by codename"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.permissions.remove(permission)
            return True
        except Permission.DoesNotExist:
            return False

    def get_permission_summary(self):
        """Get a summary of permissions grouped by app"""
        permissions = self.permissions.select_related('content_type').all()
        summary = {}
        for perm in permissions:
            app_label = perm.content_type.app_label
            if app_label not in summary:
                summary[app_label] = []
            summary[app_label].append({
                'codename': perm.codename,
                'name': perm.name,
                'model': perm.content_type.model
            })
        return summary


class GroupMembership(models.Model):
    """
    Track group assignments with additional metadata
    Extends Django's built-in User-Group relationship
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='staff_group_memberships'
    )
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='staff_memberships'
    )
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='assigned_staff_memberships'
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(
        blank=True,
        help_text="Optional notes about this group assignment"
    )

    class Meta:
        unique_together = ['user', 'group']
        ordering = ['-assigned_at']
        verbose_name = "Group Membership"
        verbose_name_plural = "Group Memberships"

    def __str__(self):
        return f"{self.user.email} in {self.group.name}"


class PermissionAudit(models.Model):
    """
    Log all permission-related actions for the audit trail
    """
    ACTION_CHOICES = [
        # Group/Role Management
        ('group_created', 'Group Created'),
        ('group_updated', 'Group Updated'),
        ('group_deleted', 'Group Deleted'),
        ('role_created', 'Role Created'),
        ('role_updated', 'Role Updated'),
        ('role_deleted', 'Role Deleted'),

        # User-Group Assignments
        ('user_added_to_group', 'User Added to Group'),
        ('user_removed_from_group', 'User Removed from Group'),
        ('bulk_users_assigned', 'Bulk Users Assigned'),

        # Permission Management
        ('permission_granted', 'Permission Granted'),
        ('permission_revoked', 'Permission Revoked'),
        ('permission_added_to_role', 'Permission Added to Role'),
        ('permission_removed_from_role', 'Permission Removed from Role'),

        # Staff Management
        ('staff_user_created', 'Staff User Created'),
        ('staff_profile_created', 'Staff Profile Created'),
        ('staff_profile_updated', 'Staff Profile Updated'),
        ('staff_status_changed', 'Staff Status Changed'),
        ('user_staff_toggled', 'User Staff Status Toggled'),

        # Security Events
        ('unauthorized_access_attempt', 'Unauthorized Access Attempt'),
        ('permission_check_failed', 'Permission Check Failed'),
        ('suspicious_activity', 'Suspicious Activity'),
    ]

    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        db_index=True
    )
    performed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True
    )
    target_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='staff_permission_audits'
    )
    target_group = models.ForeignKey(
        Group,
        on_delete=models.SET_NULL,
        null=True
    )
    details = models.JSONField(
        default=dict,
        help_text="Additional details about the action"
    )
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['performed_by', 'timestamp']),
        ]
        verbose_name = "Permission Audit"
        verbose_name_plural = "Permission Audits"

    def __str__(self):
        return f"{self.action} by {self.performed_by} at {self.timestamp}"


class APIAccessLog(models.Model):
    """
    Track API access for monitoring and security
    """
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True
    )
    endpoint = models.CharField(max_length=255, db_index=True)
    method = models.CharField(max_length=10)
    status_code = models.IntegerField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    response_time = models.FloatField(
        help_text="Response time in seconds"
    )
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['endpoint', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['status_code', 'timestamp']),
        ]
        verbose_name = "API Access Log"
        verbose_name_plural = "API Access Logs"

    def __str__(self):
        user_info = self.user.email if self.user else "Anonymous"
        return f"{self.method} {self.endpoint} by {user_info} - {self.status_code}"
