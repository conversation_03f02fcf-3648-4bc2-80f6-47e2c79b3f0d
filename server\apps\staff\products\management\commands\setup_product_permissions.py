from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.products.models import (
    Product, Category, Brand, ProductType, Attribute, AttributeValue, Review, Discount,
    ProductVariant, ProductImage, BrandProductType, ProductTypeAttribute,
    ProductAttributeValue, ProductVariantAttributeValue
)
from apps.staff.products.models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy,
    AttributeValueProxy, ProductVariantProxy, ProductImageProxy, ReviewProxy,
    DiscountProxy, BrandProductTypeProxy, ProductTypeAttributeProxy,
    ProductAttributeValueProxy, ProductVariantAttributeValueProxy
)


class Command(BaseCommand):
    help = 'Setup product-related groups and permissions for staff RBAC system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset product-related groups and permissions before creating new ones',
        )
    
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting product-related groups and permissions...')
            self.reset_product_permissions()
        
        self.stdout.write('Setting up product-related groups and permissions...')
        self.setup_product_permissions()
        self.stdout.write(self.style.SUCCESS('Product permissions setup completed!'))
    
    def reset_product_permissions(self):
        """Reset product-related groups and permissions"""
        product_groups = [
            'Product Manager',
            'Product Editor', 
            'Inventory Manager',
            'Content Manager'
        ]
        
        for group_name in product_groups:
            try:
                group = Group.objects.get(name=group_name)
                # Remove product-related permissions
                product_permissions = Permission.objects.filter(
                    content_type__app_label='products'
                )
                group.permissions.remove(*product_permissions)
                self.stdout.write(f'Reset permissions for group: {group_name}')
            except Group.DoesNotExist:
                pass
    
    def setup_product_permissions(self):
        """Setup product-related groups and permissions"""
        
        # Define permission sets
        product_permissions = self.get_model_permissions(Product)
        category_permissions = self.get_model_permissions(Category)
        brand_permissions = self.get_model_permissions(Brand)
        product_type_permissions = self.get_model_permissions(ProductType)
        attribute_permissions = self.get_model_permissions(Attribute)
        attribute_value_permissions = self.get_model_permissions(AttributeValue)
        review_permissions = self.get_model_permissions(Review)
        discount_permissions = self.get_model_permissions(Discount)
        product_variant_permissions = self.get_model_permissions(ProductVariant)
        product_image_permissions = self.get_model_permissions(ProductImage)

        # Association model permissions
        brand_product_type_permissions = self.get_model_permissions(BrandProductType)
        product_type_attribute_permissions = self.get_model_permissions(ProductTypeAttribute)
        product_attribute_value_permissions = self.get_model_permissions(ProductAttributeValue)
        variant_attribute_value_permissions = self.get_model_permissions(ProductVariantAttributeValue)

        # Get proxy model permissions
        product_proxy_permissions = self.get_model_permissions(ProductProxy)
        variant_proxy_permissions = self.get_model_permissions(ProductVariantProxy)
        image_proxy_permissions = self.get_model_permissions(ProductImageProxy)
        review_proxy_permissions = self.get_model_permissions(ReviewProxy)
        discount_proxy_permissions = self.get_model_permissions(DiscountProxy)
        brand_product_type_proxy_permissions = self.get_model_permissions(BrandProductTypeProxy)
        
        # Product Manager - Full access to all product operations
        product_manager_permissions = (
            product_permissions + category_permissions + brand_permissions +
            product_type_permissions + attribute_permissions + attribute_value_permissions +
            review_permissions + discount_permissions + product_variant_permissions +
            product_image_permissions + brand_product_type_permissions +
            product_type_attribute_permissions + product_attribute_value_permissions +
            variant_attribute_value_permissions + product_proxy_permissions +
            variant_proxy_permissions + image_proxy_permissions + review_proxy_permissions +
            discount_proxy_permissions + brand_product_type_proxy_permissions
        )
        
        # Product Editor - CRUD on products, limited access to other models
        product_editor_permissions = (
            product_permissions + product_variant_permissions +
            [p for p in category_permissions if 'view' in p or 'change' in p] +
            [p for p in brand_permissions if 'view' in p or 'change' in p] +
            [p for p in attribute_permissions if 'view' in p] +
            [p for p in attribute_value_permissions if 'view' in p] +
            [p for p in review_permissions if 'view' in p] +
            [p for p in product_image_permissions if 'view' in p or 'add' in p or 'change' in p] +
            [p for p in product_attribute_value_permissions if 'view' in p or 'add' in p or 'change' in p] +
            [p for p in variant_attribute_value_permissions if 'view' in p or 'add' in p or 'change' in p]
        )

        # Inventory Manager - Stock management only
        inventory_manager_permissions = (
            [p for p in product_permissions if 'view' in p or 'change' in p] +
            product_variant_permissions +
            [p for p in category_permissions if 'view' in p] +
            [p for p in brand_permissions if 'view' in p] +
            [p for p in product_image_permissions if 'view' in p] +
            variant_proxy_permissions
        )

        # Content Manager - Categories and content editing
        content_manager_permissions = (
            [p for p in product_permissions if 'view' in p or 'change' in p] +
            category_permissions + product_image_permissions +
            [p for p in brand_permissions if 'view' in p] +
            [p for p in review_permissions if 'view' in p or 'moderate' in p] +
            review_proxy_permissions
        )
        
        # Create/update groups with permissions
        groups_config = [
            ('Product Manager', product_manager_permissions),
            ('Product Editor', product_editor_permissions),
            ('Inventory Manager', inventory_manager_permissions),
            ('Content Manager', content_manager_permissions),
        ]
        
        for group_name, permission_codenames in groups_config:
            group, created = Group.objects.get_or_create(name=group_name)
            
            # Get permission objects
            permissions = []
            for codename in permission_codenames:
                try:
                    if '.' in codename:
                        app_label, perm_codename = codename.split('.')
                        permission = Permission.objects.get(
                            codename=perm_codename,
                            content_type__app_label=app_label
                        )
                    else:
                        permission = Permission.objects.get(codename=codename)
                    permissions.append(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {codename}')
                    )
            
            # Set permissions for the group
            group.permissions.set(permissions)
            
            action = 'Created' if created else 'Updated'
            self.stdout.write(
                self.style.SUCCESS(
                    f'{action} group "{group_name}" with {len(permissions)} permissions'
                )
            )
    
    def get_model_permissions(self, model):
        """Get all permissions for a model"""
        content_type = ContentType.objects.get_for_model(model)
        permissions = Permission.objects.filter(content_type=content_type)
        return [f'{content_type.app_label}.{p.codename}' for p in permissions]
