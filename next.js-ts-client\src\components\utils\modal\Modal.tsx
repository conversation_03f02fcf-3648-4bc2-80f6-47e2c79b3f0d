import Underlay from '../underlay/Underlay'
import { IoIosClose } from "react-icons/io"
import styles from './Modal.module.scss'

interface Props {
  title: string
  message: string
  show: boolean
  btn1?: string
  btn2?: string
  onClose: () => void
  onConfirm: () => void
}

const Modal = ({ message, title, show, onClose, onConfirm, btn1 = 'Yes', btn2 = 'No' }: Props) => {
  if (!show) return null

  return (
    <Underlay isOpen={show} onClose={onClose} bgOpacity={0.8}>
      <div className={styles.modal}>
        <button className={styles.modal_close} onClick={onClose}>
          <i><IoIosClose /></i>
        </button>
        <div className={styles.modal_body}>
          <h6 className={styles.modal_body__title}>{title}</h6>
          <p className={styles.modal_body__message}>{message}</p>
          <div className={styles.modal_actions}>
            <button className={styles.btn_confirm} onClick={onConfirm}>{btn1}</button>
            <button className={styles.modal_empty} onClick={onClose}>{btn2}</button>
          </div>
        </div>
      </div>
    </Underlay>
  )
}

export default Modal
