# Redis Cache Service Implementation
# Comprehensive caching service for cart operations with intelligent invalidation

import hashlib
import json
import pickle
from typing import Dict, Any, Optional, List
from django.core.cache import caches
from django.core.cache.utils import make_template_fragment_key
from django.utils import timezone
from django.conf import settings
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class RedisCacheService:
    """Advanced Redis caching service with intelligent cache management"""
    
    def __init__(self):
        self.default_cache = caches['default']
        self.shipping_cache = caches.get('shipping', self.default_cache)
        self.packing_cache = caches.get('packing', self.default_cache)
        
        # Cache timeouts (in seconds)
        self.timeouts = {
            'cart_data': 3600,      # 1 hour
            'shipping_calc': 1800,   # 30 minutes
            'packing_result': 7200,  # 2 hours
            'product_data': 86400,   # 24 hours
            'carrier_rates': 900,    # 15 minutes
        }
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache keys"""
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(hashlib.md5(
                    json.dumps(arg, sort_keys=True).encode('utf-8')
                ).hexdigest()[:8])
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            key_parts.append(hashlib.md5(kwargs_str.encode('utf-8')).hexdigest()[:8])
        
        return ':'.join(key_parts)
    
    def set_with_tags(self, cache_name: str, key: str, value: Any, 
                     timeout: int, tags: List[str] = None):
        """Set cache value with tags for group invalidation"""
        cache = getattr(self, f"{cache_name}_cache", self.default_cache)
        
        # Store the main value
        cache.set(key, value, timeout)
        
        # Store tags for group invalidation
        if tags:
            for tag in tags:
                tag_key = f"tag:{tag}"
                tagged_keys = cache.get(tag_key, set())
                if not isinstance(tagged_keys, set):
                    tagged_keys = set()
                tagged_keys.add(key)
                cache.set(tag_key, tagged_keys, timeout + 3600)  # Tags live longer
    
    def invalidate_by_tags(self, cache_name: str, tags: List[str]):
        """Invalidate all cache entries with given tags"""
        cache = getattr(self, f"{cache_name}_cache", self.default_cache)
        
        keys_to_delete = set()
        for tag in tags:
            tag_key = f"tag:{tag}"
            tagged_keys = cache.get(tag_key, set())
            if isinstance(tagged_keys, set):
                keys_to_delete.update(tagged_keys)
                cache.delete(tag_key)
        
        if keys_to_delete:
            cache.delete_many(list(keys_to_delete))
            logger.info(f"Invalidated {len(keys_to_delete)} cache entries for tags: {tags}")


class CartCacheService(RedisCacheService):
    """Specialized caching service for cart operations"""
    
    def cache_cart_data(self, cart, timeout: int = None) -> str:
        """Cache comprehensive cart data"""
        timeout = timeout or self.timeouts['cart_data']
        
        # Prepare cart data for caching
        cart_data = {
            'id': str(cart.id),
            'customer_id': cart.customer_id,
            'created_at': cart.created_at.isoformat(),
            'last_updated': timezone.now().isoformat(),
        }
        
        # Add computed fields
        try:
            cart_data.update({
                'total_weight': float(cart.get_cart_weight()),
                'item_count': cart.cart_items.count(),
                'shipping_cost': float(cart.shipping_cost) if cart.shipping_cost else 0,
                'packing_cost': float(cart.packing_cost) if cart.packing_cost else 0,
            })
        except Exception as e:
            logger.warning(f"Error computing cart data for {cart.id}: {e}")
        
        # Cache with multiple keys for different access patterns
        cache_key = self.generate_cache_key('cart', cart.id)
        customer_key = self.generate_cache_key('customer_cart', cart.customer_id) if cart.customer_id else None
        
        tags = [f"cart:{cart.id}"]
        if cart.customer_id:
            tags.append(f"customer:{cart.customer_id}")
        
        self.set_with_tags('default', cache_key, cart_data, timeout, tags)
        
        if customer_key:
            self.default_cache.set(customer_key, str(cart.id), timeout)
        
        return cache_key
    
    def get_cached_cart_data(self, cart_id) -> Optional[Dict]:
        """Get cached cart data"""
        cache_key = self.generate_cache_key('cart', cart_id)
        return self.default_cache.get(cache_key)
    
    def cache_cart_items(self, cart_id, items_data: List[Dict], timeout: int = None) -> str:
        """Cache cart items data"""
        timeout = timeout or self.timeouts['cart_data']
        
        cache_key = self.generate_cache_key('cart_items', cart_id)
        tags = [f"cart:{cart_id}"]
        
        # Add product tags for invalidation
        for item in items_data:
            if 'product_id' in item:
                tags.append(f"product:{item['product_id']}")
            if 'product_variant_id' in item:
                tags.append(f"variant:{item['product_variant_id']}")
        
        self.set_with_tags('default', cache_key, items_data, timeout, tags)
        return cache_key
    
    def get_cached_cart_items(self, cart_id) -> Optional[List[Dict]]:
        """Get cached cart items"""
        cache_key = self.generate_cache_key('cart_items', cart_id)
        return self.default_cache.get(cache_key)
    
    def invalidate_cart_cache(self, cart_id):
        """Invalidate all cart-related cache entries"""
        tags = [f"cart:{cart_id}"]
        self.invalidate_by_tags('default', tags)
        self.invalidate_by_tags('shipping', tags)
        self.invalidate_by_tags('packing', tags)


class ShippingCacheService(RedisCacheService):
    """Specialized caching service for shipping calculations"""
    
    def generate_shipping_signature(self, cart_items, destination) -> str:
        """Generate signature for shipping calculation"""
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'dimensions': [
                    float(item.product_variant.length),
                    float(item.product_variant.width),
                    float(item.product_variant.height)
                ]
            })
        
        # Sort for consistency
        items_data.sort(key=lambda x: x['sku'])
        
        destination_data = {
            'country': destination.country if destination else 'US',
            'postal_code': getattr(destination, 'postal_code', ''),
            'state': getattr(destination, 'state', ''),
        }
        
        combined_data = {
            'items': items_data,
            'destination': destination_data
        }
        
        return hashlib.md5(
            json.dumps(combined_data, sort_keys=True).encode('utf-8')
        ).hexdigest()
    
    def cache_shipping_calculation(self, cart_items, destination, result: Dict, 
                                 timeout: int = None) -> str:
        """Cache shipping calculation result"""
        timeout = timeout or self.timeouts['shipping_calc']
        
        signature = self.generate_shipping_signature(cart_items, destination)
        cache_key = self.generate_cache_key('shipping', signature)
        
        # Prepare cache data
        cache_data = {
            'result': result,
            'signature': signature,
            'cached_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timezone.timedelta(seconds=timeout)).isoformat(),
        }
        
        # Generate tags for invalidation
        tags = ['shipping_calc']
        for item in cart_items:
            tags.extend([
                f"product:{item.product.id}",
                f"variant:{item.product_variant.id}"
            ])
        
        if destination:
            tags.append(f"destination:{destination.country}")
        
        self.set_with_tags('shipping', cache_key, cache_data, timeout, tags)
        return cache_key
    
    def get_cached_shipping_calculation(self, cart_items, destination) -> Optional[Dict]:
        """Get cached shipping calculation"""
        signature = self.generate_shipping_signature(cart_items, destination)
        cache_key = self.generate_cache_key('shipping', signature)
        
        cached_data = self.shipping_cache.get(cache_key)
        if cached_data:
            # Check if cache is still valid
            expires_at = timezone.datetime.fromisoformat(cached_data['expires_at'])
            if timezone.now() < expires_at:
                return cached_data['result']
            else:
                # Remove expired cache
                self.shipping_cache.delete(cache_key)
        
        return None
    
    def cache_carrier_rate(self, carrier_code: str, package_signature: str, 
                          destination_signature: str, rate_data: Dict, 
                          timeout: int = None) -> str:
        """Cache individual carrier rate"""
        timeout = timeout or self.timeouts['carrier_rates']
        
        cache_key = self.generate_cache_key(
            'carrier_rate', carrier_code, package_signature, destination_signature
        )
        
        cache_data = {
            'rate': rate_data,
            'carrier': carrier_code,
            'cached_at': timezone.now().isoformat(),
        }
        
        tags = [f"carrier:{carrier_code}", 'carrier_rates']
        self.set_with_tags('shipping', cache_key, cache_data, timeout, tags)
        return cache_key
    
    def get_cached_carrier_rate(self, carrier_code: str, package_signature: str, 
                               destination_signature: str) -> Optional[Dict]:
        """Get cached carrier rate"""
        cache_key = self.generate_cache_key(
            'carrier_rate', carrier_code, package_signature, destination_signature
        )
        
        cached_data = self.shipping_cache.get(cache_key)
        return cached_data['rate'] if cached_data else None


class PackingCacheService(RedisCacheService):
    """Specialized caching service for packing calculations"""
    
    def generate_packing_signature(self, cart_items) -> str:
        """Generate signature for packing calculation"""
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'length': float(item.product_variant.length),
                'width': float(item.product_variant.width),
                'height': float(item.product_variant.height),
                'product_type': item.product.product_type.name,
            })
        
        # Sort for consistency
        items_data.sort(key=lambda x: (x['sku'], x['quantity']))
        
        return hashlib.md5(
            json.dumps(items_data, sort_keys=True).encode('utf-8')
        ).hexdigest()
    
    def cache_packing_result(self, cart_items, packing_result, timeout: int = None) -> str:
        """Cache packing calculation result"""
        timeout = timeout or self.timeouts['packing_result']
        
        signature = self.generate_packing_signature(cart_items)
        cache_key = self.generate_cache_key('packing', signature)
        
        # Serialize packing result
        cache_data = {
            'result': self._serialize_packing_result(packing_result),
            'signature': signature,
            'cached_at': timezone.now().isoformat(),
            'item_count': len(cart_items),
        }
        
        # Generate tags
        tags = ['packing_calc']
        for item in cart_items:
            tags.extend([
                f"product:{item.product.id}",
                f"variant:{item.product_variant.id}",
                f"product_type:{item.product.product_type.id}"
            ])
        
        self.set_with_tags('packing', cache_key, cache_data, timeout, tags)
        return cache_key
    
    def get_cached_packing_result(self, cart_items):
        """Get cached packing result"""
        signature = self.generate_packing_signature(cart_items)
        cache_key = self.generate_cache_key('packing', signature)
        
        cached_data = self.packing_cache.get(cache_key)
        if cached_data:
            return self._deserialize_packing_result(cached_data['result'])
        
        return None
    
    def _serialize_packing_result(self, packing_result) -> Dict:
        """Serialize packing result for caching"""
        return {
            'total_cost': float(packing_result.total_cost),
            'total_weight': float(packing_result.total_weight),
            'total_volume': float(packing_result.total_volume),
            'success': packing_result.success,
            'method_used': packing_result.method_used,
            'calculation_time': packing_result.calculation_time,
            'warnings': packing_result.warnings,
            'boxes': [self._serialize_packed_box(box) for box in packing_result.boxes],
            'unpacked_items': packing_result.unpacked_items,
        }
    
    def _serialize_packed_box(self, packed_box) -> Dict:
        """Serialize packed box for caching"""
        return {
            'box_id': packed_box.box.id,
            'box_title': packed_box.box.title,
            'utilization': float(packed_box.utilization),
            'total_weight': float(packed_box.total_weight),
            'total_cost': float(packed_box.total_cost),
            'efficiency_score': float(packed_box.efficiency_score),
            'items': [self._serialize_packed_item(item) for item in packed_box.items],
        }
    
    def _serialize_packed_item(self, packed_item) -> Dict:
        """Serialize packed item for caching"""
        return {
            'sku': packed_item.sku,
            'quantity': packed_item.quantity,
            'weight': float(packed_item.weight),
            'volume': float(packed_item.volume),
        }
    
    def _deserialize_packing_result(self, data) -> 'PackingResult':
        """Deserialize cached data back to PackingResult"""
        from apps.shipping.services.packing import PackingResult, PackedBox, PackedItem
        from apps.shipping.models import Box
        from decimal import Decimal
        
        # Reconstruct boxes
        boxes = []
        for box_data in data['boxes']:
            try:
                box = Box.objects.get(id=box_data['box_id'])
                
                # Reconstruct items
                items = []
                for item_data in box_data['items']:
                    items.append(PackedItem(
                        sku=item_data['sku'],
                        quantity=item_data['quantity'],
                        weight=Decimal(str(item_data['weight'])),
                        volume=Decimal(str(item_data['volume']))
                    ))
                
                boxes.append(PackedBox(
                    box=box,
                    items=items,
                    utilization=box_data['utilization'],
                    total_weight=Decimal(str(box_data['total_weight'])),
                    total_cost=Decimal(str(box_data['total_cost'])),
                    efficiency_score=box_data['efficiency_score']
                ))
            except Box.DoesNotExist:
                # Box was deleted, skip this box
                continue
        
        return PackingResult(
            boxes=boxes,
            total_cost=Decimal(str(data['total_cost'])),
            total_weight=Decimal(str(data['total_weight'])),
            total_volume=Decimal(str(data['total_volume'])),
            unpacked_items=data['unpacked_items'],
            success=data['success'],
            calculation_time=data['calculation_time'],
            method_used=data['method_used'],
            warnings=data['warnings']
        )


class CacheMonitor:
    """Monitor cache performance and health"""
    
    def __init__(self):
        self.cache_services = {
            'cart': CartCacheService(),
            'shipping': ShippingCacheService(),
            'packing': PackingCacheService(),
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        import redis
        
        stats = {}
        
        try:
            # Redis connection stats
            redis_client = redis.Redis.from_url(settings.CACHES['default']['LOCATION'])
            redis_info = redis_client.info()
            
            stats['redis'] = {
                'memory_usage': redis_info.get('used_memory_human'),
                'connected_clients': redis_info.get('connected_clients'),
                'keyspace_hits': redis_info.get('keyspace_hits', 0),
                'keyspace_misses': redis_info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(
                    redis_info.get('keyspace_hits', 0),
                    redis_info.get('keyspace_misses', 0)
                )
            }
            
            # Cache-specific stats
            for cache_name, service in self.cache_services.items():
                cache = getattr(service, f"{cache_name}_cache", service.default_cache)
                stats[cache_name] = {
                    'backend': cache.__class__.__name__,
                    'timeout': service.timeouts.get(f'{cache_name}_data', 3600),
                }
        
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate percentage"""
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0
    
    def warm_cache(self, cache_type: str = 'all'):
        """Warm cache with frequently accessed data"""
        # Implementation for cache warming
        pass
    
    def clear_expired_cache(self):
        """Clear expired cache entries"""
        # Implementation for cache cleanup
        pass
