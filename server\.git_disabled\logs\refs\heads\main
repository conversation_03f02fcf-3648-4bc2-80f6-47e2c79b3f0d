0000000000000000000000000000000000000000 278064410a68f0d0766881c19d60fafb918cc602 Kanishka <<EMAIL>> 1697863304 +0530	commit (initial): before adding auto product ordering feature
278064410a68f0d0766881c19d60fafb918cc602 c07f187c54a819d60efb3c1d8141faa503034c7e Kanishka <<EMAIL>> 1697888689 +0530	commit: before add product variant images
c07f187c54a819d60efb3c1d8141faa503034c7e ac88686a673728833b18d457623224bebb7b3bd5 Kanishka <<EMAIL>> 1698157454 +0530	commit: before new database design
ac88686a673728833b18d457623224bebb7b3bd5 29f025173e2e0113e6456f930ce0085f36f46394 Ka<PERSON><PERSON> <<EMAIL>> 1698226174 +0530	commit: code refactoring
29f025173e2e0113e6456f930ce0085f36f46394 29f025173e2e0113e6456f930ce0085f36f46394 Kanishka <<EMAIL>> 1698226592 +0530	Branch: renamed refs/heads/master to refs/heads/main
29f025173e2e0113e6456f930ce0085f36f46394 3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 Kanishka <<EMAIL>> 1699944817 +0530	commit: stage one completed
3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 5bc9acabe7ec202d6e72545247c4d2a001d1d6fc dev-kani <<EMAIL>> 1739272788 +0530	merge picky-pc-hardware-store-v3.0.0: Fast-forward
5bc9acabe7ec202d6e72545247c4d2a001d1d6fc cdf1e45d0d03e63ca848183d19828cb580ca5cc2 dev-kani <<EMAIL>> 1739278612 +0530	merge Feature: Fast-forward
cdf1e45d0d03e63ca848183d19828cb580ca5cc2 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739280625 +0530	merge Feature: Fast-forward
d6df113964f532b33dc52c4735c2e13fb8203554 1f62d06b884c29b5bfa3629df18a660b1a5f87ef dev-kani <<EMAIL>> 1739282753 +0530	merge Feature: Fast-forward
1f62d06b884c29b5bfa3629df18a660b1a5f87ef 22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 dev-kani <<EMAIL>> 1739282932 +0530	commit: ci-cd file modified
22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 517fb0b026a3e37e29dd07bb7a887298f426b5fb dev-kani <<EMAIL>> 1739306789 +0530	commit: removed pytest env configs
517fb0b026a3e37e29dd07bb7a887298f426b5fb fb3aa27624609b93b6b74919eabbebd316b36cf7 dev-kani <<EMAIL>> 1739308872 +0530	commit: pytest config updated
fb3aa27624609b93b6b74919eabbebd316b36cf7 278185872c87e6318f62cf3fffbe0c5abe408df2 dev-kani <<EMAIL>> 1739322136 +0530	commit: removed .vscode, disabled ci-cd
278185872c87e6318f62cf3fffbe0c5abe408df2 7dca269345e449e20b83b440c4a335bccf1ecf84 dev-kani <<EMAIL>> 1741416344 +0530	commit: dockerfiles and entrypoint.sh updated
7dca269345e449e20b83b440c4a335bccf1ecf84 dd9debb5ae5e7803e0fa47b94bed409a9265132a dev-kani <<EMAIL>> 1741437539 +0530	commit: dockerfile and entrypoint.sh updated
dd9debb5ae5e7803e0fa47b94bed409a9265132a 62bb6a6eb7bd2bd1e3f6d35c50bfdfb46dd2c015 dev-kani <<EMAIL>> 1741438738 +0530	commit: dockerfiles and entrypoint.sh updated
62bb6a6eb7bd2bd1e3f6d35c50bfdfb46dd2c015 8eddd78f8dd47686c38d772028c0c24516d10fc6 dev-kani <<EMAIL>> 1741444401 +0530	commit: log settings updated
8eddd78f8dd47686c38d772028c0c24516d10fc6 1a7bfa15e8617b188e8e70b359419e805e2f4686 dev-kani <<EMAIL>> 1741447063 +0530	commit: Dockerfile updated
1a7bfa15e8617b188e8e70b359419e805e2f4686 3604f56c3f9c56e0bad2ab8da6bcc25ee70a0557 dev-kani <<EMAIL>> 1741456186 +0530	commit: production db settings updated
3604f56c3f9c56e0bad2ab8da6bcc25ee70a0557 216fa6d6b34474a0e0316b719aa57597db12cbc2 dev-kani <<EMAIL>> 1741511804 +0530	commit: production/common whitenoise setting updated
216fa6d6b34474a0e0316b719aa57597db12cbc2 4aea29318fa863ab7069d5e01068fa242869186d dev-kani <<EMAIL>> 1741512778 +0530	commit: entrypoint.sh updated to workers 2
4aea29318fa863ab7069d5e01068fa242869186d 17282a3c42712d1ae9af0e13d2ac77c428a611c3 dev-kani <<EMAIL>> 1741514953 +0530	commit: entrypoint.sh updated
17282a3c42712d1ae9af0e13d2ac77c428a611c3 ecc1116dd3ad4d069b7237c0710285ef16502340 dev-kani <<EMAIL>> 1745333056 +0530	commit: before adding ER diagram
ecc1116dd3ad4d069b7237c0710285ef16502340 5900001bfc10306798a119b015b4412920b9a073 dev-kani <<EMAIL>> 1746926119 +0530	commit: before changing product attribute value links
5900001bfc10306798a119b015b4412920b9a073 bb63937ce895905f0735bfb1c85a3fb8b960ceb9 dev-kani <<EMAIL>> 1747752936 +0530	commit: before adding attribute filters to the frontend
bb63937ce895905f0735bfb1c85a3fb8b960ceb9 f31c92d5d1ae0664835acab317eaecdbc3bb6f07 dev-kani <<EMAIL>> 1749362903 +0530	commit: pc breaks down
f31c92d5d1ae0664835acab317eaecdbc3bb6f07 1b4942dd97b318d2b2a01902828d08024a630263 Kanishka Samarathunga <<EMAIL>> 1751189987 +0530	commit: before adding Staff app
1b4942dd97b318d2b2a01902828d08024a630263 1b4942dd97b318d2b2a01902828d08024a630263 Kanishka Samarathunga <<EMAIL>> 1751192809 +0530	Branch: renamed refs/heads/main to refs/heads/main
1b4942dd97b318d2b2a01902828d08024a630263 8abadae4b0aa176ddff87153f0bbba00a205a336 Kanishka Samarathunga <<EMAIL>> 1751959701 +0530	commit: before adding order-mgt-endpoints
