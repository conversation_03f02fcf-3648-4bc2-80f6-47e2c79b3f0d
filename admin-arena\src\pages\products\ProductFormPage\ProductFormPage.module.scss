@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerLeft {
  @include flex-start;
  gap: $spacing-4;
  flex: 1;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-3;
  }
}

.backButton {
  @include flex-center;
  gap: $spacing-2;
  color: $gray-600;

  &:hover {
    color: $gray-900;
  }
}

.titleSection {
  flex: 1;
}

.title {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.subtitle {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-base;
}

.headerActions {
  @include flex-start;
  gap: $spacing-3;

  @include mobile-only {
    width: 100%;
    justify-content: stretch;
  }
}

.form {
  width: 100%;
}

.formGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: $spacing-6;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.variantsCard {
  grid-column: 1 / -1;
  margin-top: $spacing-6;
}

.variantsHeader {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;

  h2 {
    margin: 0;
  }
}

.variantsSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.variantCard {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  padding: $spacing-4;
  background: $gray-50;
}

.variantHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-4;

  h4 {
    margin: 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
}

.variantForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.switchGroup {
  @include flex-between;
  align-items: center;
  gap: $spacing-3;
  padding: $spacing-3;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  background: $gray-50;

  label {
    font-weight: $font-weight-medium;
    color: $gray-900;
    margin: 0;
  }
}

.mainCard {
  height: fit-content;
}

.sideCard {
  height: fit-content;
}

.formSection {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;

  label {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.textarea {
  @include input-base;
  resize: vertical;
  min-height: 100px;
  font-family: $font-family-sans;
}

.select {
  @include input-base;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $spacing-3 center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: $spacing-10;
  appearance: none;

  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

.checkboxLabel {
  @include flex-start;
  gap: $spacing-2;
  cursor: pointer;

  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    border: 1px solid $gray-300;
    border-radius: $border-radius-sm;
    background-color: white;
    cursor: pointer;

    &:checked {
      background-color: $primary-500;
      border-color: $primary-500;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
    }
  }

  span {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.help {
  margin: 0;
  font-size: $font-size-xs;
  color: $gray-500;
}

.error {
  font-size: $font-size-xs;
  color: $red-600;
  margin-top: $spacing-1;
}

// Image upload section (for future implementation)
.imageUpload {
  @include flex-column;
  gap: $spacing-4;
}

.uploadArea {
  @include flex-column-center;
  gap: $spacing-3;
  padding: $spacing-8;
  border: 2px dashed $gray-300;
  border-radius: $border-radius-lg;
  background-color: $gray-50;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: $primary-400;
    background-color: $primary-50;
  }

  &.dragOver {
    border-color: $primary-500;
    background-color: $primary-100;
  }
}

.uploadIcon {
  width: 48px;
  height: 48px;
  color: $gray-400;
}

.uploadText {
  text-align: center;

  h4 {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $gray-900;
  }

  p {
    margin: 0;
    font-size: $font-size-sm;
    color: $gray-600;
  }
}

.imagePreview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: $spacing-3;
}

.imageItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: $border-radius;
  overflow: hidden;
  border: 1px solid $gray-200;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.removeImage {
  position: absolute;
  top: $spacing-1;
  right: $spacing-1;
  width: 24px;
  height: 24px;
  @include flex-center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: $border-radius-full;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: $red-600;
  }

  svg {
    width: 12px;
    height: 12px;
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }

  .title {
    font-size: $font-size-xl;
  }

  .headerActions {
    flex-direction: column;

    button {
      width: 100%;
      justify-content: center;
    }
  }

  .formGrid {
    grid-template-columns: 1fr;
  }

  .variantsCard {
    margin-top: $spacing-4;
  }

  .formRow {
    grid-template-columns: 1fr;
  }
}

// React Select custom styles
:global(.react-select__control) {
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  min-height: 42px;
}

:global(.react-select__control:hover) {
  border-color: $gray-400;
}

:global(.react-select__control--is-focused) {
  border-color: $primary-500;
  box-shadow: 0 0 0 1px $primary-500;
}

:global(.react-select__value-container) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__placeholder) {
  color: $gray-500;
}

:global(.react-select__single-value) {
  color: $gray-900;
}

:global(.react-select__option) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-50;
  color: $primary-900;
}

:global(.react-select__option--is-selected) {
  background-color: $primary-500;
  color: $white;
}

:global(.react-select__menu) {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  box-shadow: $shadow-lg;
  z-index: 9999;
}

:global(.react-select__menu-list) {
  padding: $spacing-1;
}