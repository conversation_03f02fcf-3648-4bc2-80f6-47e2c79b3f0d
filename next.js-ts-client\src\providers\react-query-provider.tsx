'use client'

import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { queryClient } from '@/src/lib/query-client'

/**
 * React Query Provider Component
 * 
 * This component provides the TanStack Query client to the entire application.
 * It uses the pre-configured queryClient from query-client.ts which includes:
 * - Optimized cache settings for e-commerce data
 * - Entity-specific query defaults
 * - Smart retry logic
 * - Disabled refetchOnWindowFocus for better UX
 * 
 * The queryClient is created once and reused across the application
 * to maintain cache consistency and performance.
 */
export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools />
    </QueryClientProvider>
  )
}
