import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { CUSTOMER_DETAILS } from "../constants/constants"
import APIClient from "../lib/api-client"
import { CustomerShape } from "../types/store-types"
import { UpdateCustomerShape } from '@/app/(auth)/register/create-customer/page'


/**
 * Hook to fetch customer details
 * 
 * Cache settings are now handled globally in query-client.ts:
 * - staleTime: 5 minutes (customer_details default)
 * - gcTime: 15 minutes
 * - refetchOnWindowFocus: false (disabled for better UX)
 * - Smart retry logic for failed requests
 * 
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useCustomerDetails = (enabled: boolean = true) => {
  const apiClient = new APIClient<CustomerShape>('/customers/me/')

  return useQuery({
    queryKey: [CUSTOMER_DETAILS],
    queryFn: () => apiClient.get(),
    enabled,
  })
}

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/customers/me/`)

  const mutation = useMutation({
    mutationFn: (customerData: Partial<UpdateCustomerShape>) => apiClient.patch(customerData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}

