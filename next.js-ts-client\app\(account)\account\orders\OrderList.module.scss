@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;

// Mobile-first base styles
.no_orders {
  padding: 3rem 0;
  text-align: center;

  p {
    font-size: $font-size-5;
    padding: 1rem;
  }

  a {
    color: $primary-blue;

    &:hover {
      text-decoration: underline;
    }
  }
}

.container {
  padding: 0 1rem;
}

// Mobile-first responsive adjustments for view toggle
:global(.view-toggle) {
  margin-bottom: 1rem;

  button {
    padding: 0.5rem 1rem;
    font-size: 14px;
  }
}

// Mobile-first responsive adjustments for orders table
:global(.orders-table) {
  overflow-x: auto;

  table {
    min-width: 600px;
    font-size: 14px;
  }

  th,
  td {
    padding: 0.5rem;
  }
}

// Mobile-first responsive adjustments for orders list
:global(.orders-list) {
  .order-card {
    margin-bottom: 1rem;
    padding: 1rem;

    .order-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .order-items {
      margin: 1rem 0;

      .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;

        .item-image {
          width: 100%;
          max-width: 150px;
          height: auto;
        }
      }
    }

    .order-footer {
      flex-direction: column;
      gap: 1rem;

      .order-actions {
        width: 100%;

        button {
          width: 100%;
          margin-bottom: 0.5rem;
        }
      }
    }
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .container {
    padding: 0;
  }

  :global(.view-toggle) {
    margin-bottom: 2rem;

    button {
      padding: 0.75rem 1.5rem;
      font-size: 16px;
    }
  }

  :global(.orders-table) {
    overflow-x: visible;

    table {
      min-width: auto;
      font-size: 16px;
    }

    th,
    td {
      padding: 1rem;
    }
  }

  :global(.orders-list) {
    .order-card {
      margin-bottom: 1.5rem;
      padding: 1.5rem;

      .order-header {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
      }

      .order-items {
        margin: 1.5rem 0;

        .order-item {
          flex-direction: row;
          align-items: center;
          gap: 1rem;

          .item-image {
            width: auto;
            max-width: 100px;
            height: 100px;
          }
        }
      }

      .order-footer {
        flex-direction: row;
        gap: 2rem;

        .order-actions {
          width: auto;

          button {
            width: auto;
            margin-bottom: 0;
            margin-right: 0.5rem;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}
