'use client'

import { useState } from 'react'
import Image from 'next/image'
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import {
  ProductShape,
  ProductVariant,
} from '../../../../../src/types/product-types'
import { getAllProductImages } from '../utils/product-utils'
import styles from './ProductImageGallery.module.scss'

interface ProductImageGalleryProps {
  product: ProductShape
  selectedImage: string
  selectedVariant: ProductVariant | null
  onImageClick: (image: string) => void
}

export default function ProductImageGallery({
  product,
  selectedImage,
  selectedVariant,
  onImageClick,
}: ProductImageGalleryProps) {
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 })
  const [isZoomed, setIsZoomed] = useState(false)

  const allImages = getAllProductImages(product, selectedVariant || undefined)

  // Add a default placeholder image if no images are found
  if (allImages.length === 0) {
    allImages.push({
      id: 0,
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xNzUgMTUwSDIyNVYyNTBIMTc1VjE1MFoiIGZpbGw9IiNEREREREQiLz4KPHN2Zz4K',
      alt: 'No image available',
    })
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed) return

    const { left, top, width, height } = e.currentTarget.getBoundingClientRect()
    const x = ((e.clientX - left) / width) * 100
    const y = ((e.clientY - top) / height) * 100

    setHoverPosition({ x, y })
  }

  const navigateImage = (direction: 'prev' | 'next') => {
    const currentIndex = allImages.findIndex((img) => img.url === selectedImage)
    if (currentIndex === -1) return

    let newIndex
    if (direction === 'prev') {
      newIndex = (currentIndex - 1 + allImages.length) % allImages.length
    } else {
      newIndex = (currentIndex + 1) % allImages.length
    }

    onImageClick(allImages[newIndex].url)
  }

  return (
    <section className={styles.product_images}>
      <div className={styles.product_images__list}>
        {allImages.map((image) => (
          <div
            key={image.id}
            className={`${styles.thumbnail} ${
              selectedImage === image.url ? styles.active : ''
            }`}
            onClick={() => onImageClick(image.url)}
          >
            <Image
              src={image.url}
              alt={image.alt}
              width={60}
              height={60}
              sizes='60px'
              style={{
                objectFit: 'cover',
                width: '100%',
                height: '100%',
              }}
            />
          </div>
        ))}
      </div>

      <div className={styles.product_image}>
        <div
          className={`${styles.image_container} ${
            isZoomed ? styles.zoomed_container : ''
          }`}
          onMouseEnter={() => setIsZoomed(true)}
          onMouseLeave={() => setIsZoomed(false)}
          onMouseMove={handleMouseMove}
          // set CSS variable for dynamic transform-origin
          style={
            {
              ['--zoom-origin' as any]: `${hoverPosition.x}% ${hoverPosition.y}%`,
            } as React.CSSProperties
          }
        >
          <Image
            src={selectedImage}
            alt={
              allImages.find((img) => img.url === selectedImage)?.alt ||
              'Product image'
            }
            width={500}
            height={500}
            sizes='(max-width: 768px) 100vw, 50vw'
            priority
            className={isZoomed ? styles.zoomed_image : ''}
          />

          {allImages.length > 1 && (
            <div className={styles.image_navigation}>
              <button
                className={styles.nav_button}
                onClick={() => navigateImage('prev')}
                aria-label='Previous image'
              >
                <FaChevronLeft />
              </button>
              <button
                className={styles.nav_button}
                onClick={() => navigateImage('next')}
                aria-label='Next image'
              >
                <FaChevronRight />
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
