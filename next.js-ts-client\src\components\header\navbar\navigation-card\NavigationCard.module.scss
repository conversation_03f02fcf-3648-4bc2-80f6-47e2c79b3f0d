@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.nav__card {
  width: 100%;
  position: absolute;
  background-color: $sky-lighter-blue;
  padding: $padding-2;
  z-index: 3;
  border-radius: 0 0 $border-radius-2 $border-radius-2;
  box-shadow: $box-shadow-blue-1;

  // Smooth appearance animation
  animation: slideDown 0.25s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin: calc($padding-1 / 2) 0;

      a {
        display: block;
        padding: calc($padding-1 / 2) $padding-1;
        border-radius: $border-radius-1;
        font-weight: normal;
        color: $primary-dark-text-color;
        transition: all 0.2s ease;
        text-decoration: none;

        &:hover {
          color: $primary-blue;
          text-decoration: none;
          transform: translateX(2px);
        }

        &:focus {
          outline: 2px solid $primary-blue;
          outline-offset: 1px;
          background-color: rgba(255, 255, 255, 0.15);
        }

        &:focus-visible {
          outline: 2px solid $primary-blue;
          outline-offset: 1px;
        }
      }
    }
  }

  // Alternatively, use this reusable mixin for Level List Styling
  // @include levelStyles(5, $primary-dark, 15px);

  // Level 0 styles (Horizontal layout with wrapping)
  .level-0 {
    @include flexbox(flex-start, flex-start, row, wrap);
    gap: $padding-1;
    margin-bottom: $padding-1;

    li {
      margin: 0;
      min-width: 100px; // Ensure consistent minimum width

      a {
        font-size: $font-size-3;
        font-weight: bold;
        color: $primary-dark-blue;
        padding: $padding-1 $padding-2;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }

  // Level 1 styles (Vertical layout)
  .level-1 {
    margin-left: $padding-1;
    margin-top: calc($padding-1 / 3);
    border-left: 2px solid rgba(255, 255, 255, 0.1);
    padding-left: calc($padding-1 / 2);

    li {
      margin: calc($padding-1 / 3) 0;

      a {
        font-size: $font-size-3;
        font-weight: 600;
        color: $primary-blue;
        padding: calc($padding-1 / 3) calc($padding-1 / 2);
      }
    }
  }

  // Level 2 styles (Vertical layout)
  .level-2 {
    margin-left: $padding-1;
    margin-top: calc($padding-1 / 4);

    li {
      margin: calc($padding-1 / 4) 0;

      a {
        font-size: $font-size-3;
        color: $primary-dark;
        padding: calc($padding-1 / 4) calc($padding-1 / 2);
        font-weight: 500;
      }
    }
  }

  // Level 3 styles
  .level-3 {
    margin-left: $padding-1;
    margin-top: calc($padding-1 / 4);

    li {
      margin: calc($padding-1 / 5) 0;

      a {
        font-size: $font-size-3;
        font-style: italic;
        color: $primary-lighter-text-color;
        padding: calc($padding-1 / 5) calc($padding-1 / 2);
        font-weight: 400;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: $mobile) {
    padding: $padding-1;

    .level-0 {
      gap: $padding-1;

      li {
        min-width: 90px;
        flex: 1 0 45%; // Two items per row on mobile

        a {
          padding: calc($padding-1 / 2) $padding-1;
          font-size: $font-size-3;
        }
      }
    }

    .level-1 {
      margin-left: $padding-1;
      padding-left: calc($padding-1 / 2);
    }

    .level-2,
    .level-3 {
      margin-left: $padding-1;
    }
  }

  @media (min-width: $mobile) {
    .level-0 {
      li {
        flex: 1 0 20%; // Items take up equal space and wrap as needed
      }
    }
  }

  @media (min-width: 768px) {
    .level-0 {
      li {
        flex: 1 0 16.66%; // Six items per row on larger screens
      }
    }
  }
}