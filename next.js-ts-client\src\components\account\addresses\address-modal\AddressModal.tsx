import { useF<PERSON>, SubmitHand<PERSON> } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { addressSchema } from "@/src/schemas/schemas"
import Underlay from "@/src/components/utils/underlay/Underlay"
import styles from './AddressModal.module.scss'
import { CountrySelect, StateSelect } from 'react-country-state-city'
import 'react-country-state-city/dist/react-country-state-city.css'
import { useState, useEffect } from "react"


type AddressFormInputs = z.infer<typeof addressSchema>

interface Props {
  onSubmit: (data: AddressFormInputs) => void
  onClose: () => void
  initialData?: AddressFormInputs
  isEditing: boolean
  isOpen: boolean
}

const AddressModal = ({ onSubmit, onClose, initialData, isEditing, isOpen }: Props) => {
  const [countryId, setCountryId] = useState<number | null>(null)
  const [stateId, setStateId] = useState<number | null>(null)

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<AddressFormInputs>({
    resolver: zodResolver(addressSchema),
    defaultValues: initialData || {
      country: '',
      country_code: '',
      state_or_region: ''
    },
  })

  // Set initial country and state if editing
  useEffect(() => {
    if (initialData?.country && initialData.country_code) {
      // Note: react-country-state-city requires countryId, so we'll need to handle this differently
      // For now, we'll set the values directly
      setValue('country', initialData.country)
      setValue('country_code', initialData.country_code)
      setValue('state_or_region', initialData.state_or_region || '')
    }
  }, [initialData, setValue])

  const onSubmitForm: SubmitHandler<AddressFormInputs> = (data) => {
    onSubmit(data)
  }

  const handleCountryChange = (country: any) => {
    if (country) {
      setCountryId(country.id)
      setValue('country', country.name)
      setValue('country_code', country.iso2)
      setStateId(null) // Reset state when country changes
      setValue('state_or_region', '')
    } else {
      setCountryId(null)
      setValue('country', '')
      setValue('country_code', '')
      setValue('state_or_region', '')
    }
  }

  const handleStateChange = (state: any) => {
    if (state) {
      setStateId(state.id)
      setValue('state_or_region', state.name)
    } else {
      setStateId(null)
      setValue('state_or_region', '')
    }
  }

  return (
    <Underlay isOpen={isOpen}>
      <div className={styles.modal_content}>
        <h3>{isEditing ? 'Edit Address' : 'Add New Address'}</h3>
        <form id="address-form" onSubmit={handleSubmit(onSubmitForm)}>
          <div>
            <label htmlFor='full_name'>Full Name:</label>
            <input id='full_name' type="text" {...register('full_name')} />
            {errors.full_name && <p>{errors.full_name.message}</p>}
          </div>
          <div>
            <label htmlFor='street_name'>Street Name:</label>
            <input id='street_name' type="text" {...register('street_name')} />
            {errors.street_name && <p>{errors.street_name.message}</p>}
          </div>
          <div>
            <label htmlFor='address_line_1'>Address Line 1: (Optional)</label>
            <input id='address_line_1' type="text" {...register('address_line_1')} />
            {errors.address_line_1 && <p>{errors.address_line_1.message}</p>}
          </div>
          <div>
            <label htmlFor='address_line_2'>Address Line 2: (Optional)</label>
            <input id='address_line_2' type="text" {...register('address_line_2')} />
            {errors.address_line_2 && <p>{errors.address_line_2.message}</p>}
          </div>
          <div>
            <label htmlFor='postal_code'>Postal Code:</label>
            <input id='postal_code' type="text" {...register('postal_code')} />
            {errors.postal_code && <p>{errors.postal_code.message}</p>}
          </div>
          <div>
            <label htmlFor='city_or_village'>City or Village:</label>
            <input id='city_or_village' type="text" {...register('city_or_village')} />
            {errors.city_or_village && <p>{errors.city_or_village.message}</p>}
          </div>

          <div>
            <label>Country:</label>
            <CountrySelect
              onChange={handleCountryChange}
              placeHolder="Select Country"
              value={countryId}
              // To limit countries, you can use the countryCode prop
              // Example: limit to US, CA, UK only
              countryCode={["NO", "SE", "DK", "FI"]}
            />
            <input type="hidden" {...register('country')} />
            {errors.country && <p>{errors.country.message}</p>}
          </div>

          <div>
            <label>State/Region:</label>
            <StateSelect
              countryid={countryId}
              onChange={handleStateChange}
              placeHolder="Select State/Region"
              value={stateId}
              disabled={!countryId}
            />
            <input type="hidden" {...register('state_or_region')} />
            {errors.state_or_region && <p>{errors.state_or_region.message}</p>}
          </div>

          <div>
            <label htmlFor='country_code'>Country Code:</label>
            <input
              id='country_code'
              type="text"
              {...register('country_code')}
              readOnly
              placeholder="Auto-filled from country selection"
            />
            {errors.country_code && <p>{errors.country_code.message}</p>}
          </div>
        </form>
        <div className={styles.modal_buttons}>
          <button type="submit" form="address-form">{isEditing ? 'Update' : 'Save'}</button>
          <button type="button" onClick={onClose}>Cancel</button>
        </div>
      </div>
    </Underlay>
  )
}

export default AddressModal