@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.product_details {
  padding: 1rem 0.5rem;

  @media (width > 375px) {
    padding: 0;
  }
}

.title_and_rating {
  margin-bottom: 1rem;
}

.product_title {
  font-size: $font-size-7;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: 0.5rem;
}

.rating_container {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
}

.price_container {
  padding: $padding-1 0;
  position: relative;
}

.discount_badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: $primary-red;
  color: white;
  padding: $padding-1;
  border-radius: $border-radius-1;
  font-size: $font-size-1;
  font-weight: bold;
}

.price {
  @include flexbox(flex-start, baseline);
  gap: 0.5rem;
}

.current_price {
  font-size: $font-size-5;
  color: $primary-red;
  font-weight: bold;
}

.original_price {
  font-size: $font-size-3;
  text-decoration: line-through;
  color: $primary-lighter-text-color;
}

.savings {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-top: 0.25rem;
}

.divider {
  border: none;
  height: 1px;
  background-color: #e7e7e7;
  margin: 0.5rem 0;
}

.product_variants {
  padding: $padding-1 0;
  margin-top: 0.25rem;

  h3 {
    text-transform: capitalize;
    font-size: $font-size-3;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: $primary-dark-text-color;
  }
}

.selected_value_display {
  margin: 0 0 0 0.3rem;
  color: $primary-blue;
}

.variants {
  margin: 0.25rem 0;
  @include flexbox(flex-start, center);
  flex-wrap: wrap;
  gap: 0.5rem;

  &:hover {
    cursor: pointer;
  }
}

.variant {
  padding: $padding-1 $padding-3;
  @include flexbox(center, center, column);
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-2;

  &:hover {
    background-color: #f7fafa;
    border-color: $primary-blue;
  }
}

.variant__highlight {
  border: 2px solid $primary-blue;

  p:nth-child(2) {
    font-weight: bold;
  }
}

.stock_status {
  margin: 0.5rem 0;
}

.stock_indicator {
  font-weight: bold;
  padding: $padding-1 0;
}

.in_stock {
  color: $primary-green;
}

.low_stock {
  color: $primary-red;
}

.out_of_stock {
  color: $primary-red;
}

.product_quantity {
  margin: 0.5rem 0;

  p {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: $primary-dark-text-color;
  }
}

.quantity__controls {
  @include flexbox;
  gap: 0.5rem;

  button {
    width: 28px;
    height: 28px;
    @include flexbox(center, center);
    background-color: #f0f2f2;
    border: 1px solid #d5d9d9;
    border-radius: $border-radius-1;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: $sky-lighter-blue;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    i {
      font-size: $font-size-1;
      @include flexbox(center, center);
    }
  }

  input {
    width: 40px;
    height: 28px;
    text-align: center;
    border: 1px solid #d5d9d9;
    border-radius: $border-radius-1;
  }
}

.checkout {
  margin: 1.5rem 0;
  display: grid;
  gap: 1rem;
  width: 100%;
  grid-template-columns: 1fr;
  grid-template-areas:
    'add-to-cart'
    'buy-now'
    'wishlist';

  @media (width > 425px) {
    grid-template-columns: 1fr 1fr auto;
    grid-template-areas: 'add-to-cart buy-now wishlist';
    align-items: center;
  }
}

.buy_now_btn {
  @include btn(#fff, $primary-blue);
  text-transform: uppercase;
  width: 100%;
  padding: 0.6rem 0rem;
  transition: all 0.2s ease-in-out;
  grid-area: buy-now;

  &:hover:not(:disabled) {
    background-color: color.adjust($primary-blue, $lightness: -5%);
  }

  &:disabled {
    background-color: $primary-blue;
    color: #fff;
    opacity: 0.6;
    cursor: not-allowed;
    border: none;
  }
}

.wishlist_wrapper {
  grid-area: wishlist;
  @include flexbox(center, center);
}

// Secondary attribute selector styles
.option_selector {
  margin: 1rem 0;

  &.next_to_select {
    border: 2px solid $primary-blue;
    border-radius: $border-radius-2;
    padding: $padding-2;
    background-color: rgba(0, 145, 207, 0.05);
  }
}

.selector_title {
  font-size: $font-size-3;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.selector_values {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
  flex-wrap: wrap;
}

.selector_value {
  padding: $padding-1 $padding-2;
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-1;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  min-height: 40px;
  @include flexbox(center, center);

  &:hover:not(:disabled) {
    border-color: $primary-blue;
    background-color: #f7fafa;
  }

  &.selected_value {
    border: 2px solid $primary-blue;
    background-color: rgba(0, 145, 207, 0.1);
    font-weight: 600;
  }

  &.unavailable_value {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f5f5f5;

    &:hover {
      border-color: #d5d9d9;
      background-color: #f5f5f5;
    }
  }

  &:disabled {
    cursor: not-allowed;
  }
}

.color_image_button {
  width: 35px;
  height: 35px;
  border-radius: $border-radius-1;
  overflow: hidden;
  @include flexbox(center, center);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &.selected_color_image {
    border: 3px solid $primary-blue;
  }

  &.unavailable_color_image {
    opacity: 0.5;
    filter: grayscale(50%);
  }
}

.color_swatch {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #ddd;
}

.attribute_text {
  font-size: $font-size-2;
  font-weight: 500;
}

.missing_attr_msg {
  font-weight: 400;
  color: $primary-blue;
  margin-left: 0.5rem;
  font-size: $font-size-2;
  font-style: italic;
}