import styles from './FilterSkeleton.module.scss'

const FilterSkeleton = () => (
  <div className={styles.filter_skeleton}>
    {/* Price filter skeleton */}
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      <div className={styles.price_range_skeleton}>
        <div className={styles.range_item}>
          <div className={styles.range_label_skeleton}></div>
          <div className={styles.range_slider_skeleton}></div>
          <div className={styles.range_value_skeleton}></div>
        </div>
        <div className={styles.range_item}>
          <div className={styles.range_label_skeleton}></div>
          <div className={styles.range_slider_skeleton}></div>
          <div className={styles.range_value_skeleton}></div>
        </div>
      </div>
    </div>
    
    {/* Condition filter skeleton */}
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      {[...Array(3)].map((_, i) => (
        <div key={i} className={styles.filter_option_skeleton}>
          <div className={styles.radio_skeleton}></div>
          <div className={styles.option_label_skeleton}></div>
        </div>
      ))}
    </div>
    
    {/* Brands filter skeleton */}
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      {[...Array(5)].map((_, i) => (
        <div key={i} className={styles.filter_option_skeleton}>
          <div className={styles.checkbox_skeleton}></div>
          <div className={styles.option_label_skeleton}></div>
        </div>
      ))}
    </div>

    {/* Attribute filters skeleton */}
    <div className={styles.attribute_filters_skeleton}>
      {[...Array(2)].map((_, sectionIndex) => (
        <div key={sectionIndex} className={styles.attribute_section}>
          <div className={styles.attribute_title_skeleton}></div>
          {[...Array(4)].map((_, i) => (
            <div key={i} className={styles.filter_option_skeleton}>
              <div className={styles.checkbox_skeleton}></div>
              <div className={styles.option_label_skeleton}></div>
            </div>
          ))}
        </div>
      ))}
    </div>
  </div>
)

export default FilterSkeleton
