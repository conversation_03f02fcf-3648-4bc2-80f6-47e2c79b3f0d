from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.contrib.auth import get_user_model
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView, TokenObtainPairView
from .tasks.tasks import delete_unverified_user
from .authentication import (InitiateRegistrationRateThrottle, UserRegistrationJWTAuthentication)
from .serializers import (ConsolidatedUserSerializer, VerificationSerializer, SetPasswordSerializer,
                          CustomLoginSerializer, ChangePrimaryAuthMethodSerializer, ChangePasswordSerializer,
                          PasswordResetConfirmSerializer, PasswordResetRequestSerializer, UpdateUserSerializer,
                          ContactUpdateSerializer, RegVerificationSerializer)
from .utils import generate_verification_code, send_sms, send_email
from social_django.utils import psa, load_strategy, load_backend

User = get_user_model()


class InitiateRegistrationView(APIView):
    # throttle_classes = [InitiateRegistrationRateThrottle]  # Disabled in development

    def post(self, request):
        serializer = ConsolidatedUserSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data.get('email')
            phone_number = serializer.validated_data.get('phone_number')

            print(phone_number)
            print(email)

            code = generate_verification_code()

            try:
                if email:
                    subject = 'Verification Code'
                    message = f'Your verification code is: {code}'
                    print(message, flush=True)
                    send_email(email, subject, message)
                    cache.set(f'verification_code_{email}', code, timeout=900)  # 15 minutes
                    cache.delete(f'verification_attempts_{email}')  # Reset attempts counter
                    return Response({'message': 'Verification code sent', 'username': email}, status=status.HTTP_200_OK)
                elif phone_number:
                    text = f'Your verification code is: {code}'
                    print(text, flush=True)
                    # send_sms(phone_number, text)
                    cache.set(f'verification_code_{phone_number}', code, timeout=900)  # 15 minutes
                    cache.delete(f'verification_attempts_{phone_number}')  # Reset attempts counter
                    return Response({'message': 'Verification code sent', 'username': str(phone_number)},
                                    status=status.HTTP_200_OK)
            except Exception as e:
                return Response({'error': f'Failed to send verification code: {str(e)}'},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyCodeView(APIView):
    def post(self, request):
        serializer = RegVerificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data.get('email')
        phone_number = serializer.validated_data.get('phone_number')
        code = serializer.validated_data['code']
        key = f'verification_code_{email or phone_number}'
        attempt_key = f'verification_attempts_{email or phone_number}'

        # Retrieve the stored code and attempts from the cache
        stored_code = cache.get(key)
        attempts = cache.get(attempt_key, 0)

        # If code is not found or expired
        if not stored_code:
            return Response({'error': 'Verification code has expired'}, status=status.HTTP_400_BAD_REQUEST)

        # If code is incorrect and attempts are less than 3, increment attempt count and allow retry
        if stored_code != code:
            attempts += 1
            cache.set(attempt_key, attempts, timeout=900)  # 15-minute timeout for attempts as well
            if attempts >= 3:
                cache.delete(key)
                cache.delete(attempt_key)
                return Response({'error': 'Verification code invalid. Maximum attempts reached.'},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'error': f'Invalid verification code.'},
                            status=status.HTTP_400_BAD_REQUEST)

        # If code is correct, delete the verification keys and create the user
        cache.delete(key)
        cache.delete(attempt_key)

        try:
            user = User.objects.create_user(email=email, phone_number=phone_number)
            if email:
                user.is_email_verified = True
            else:
                user.is_phone_verified = True
            user.save()

            # Schedule the deletion task 15 minutes later
            # delete_unverified_user.apply_async((user.id,), countdown=900)  # Disabled in development mode

        except Exception as e:
            return Response({'error': f'Failed to create user: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Generate token and return successful response
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)

        response = Response({'message': 'Verification successful'}, status=status.HTTP_200_OK)
        response.set_cookie(
            'short_access_token',
            access_token,
            max_age=900,  # JWT cookie with be discarded after 15 min  
            httponly=True,
            secure=settings.AUTH_COOKIE_SECURE,
            samesite=settings.AUTH_COOKIE_SAMESITE
        )

        return response


class SetPasswordView(APIView):
    authentication_classes = [UserRegistrationJWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = SetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user

            # Ensure either email or phone is verified before setting password
            if not user.is_email_verified and not user.is_phone_verified:
                return Response({"error": "Email or phone number not verified"},
                                status=status.HTTP_400_BAD_REQUEST)

            # Set password and activate the user
            user.set_password(serializer.validated_data['password'])
            user.password_set = True
            user.save()

            # Delete the short access token
            response = Response({'message': 'Password set successfully'}, status=status.HTTP_200_OK)
            response.delete_cookie('short_access_token')

            # Issue login tokens
            refresh = RefreshToken.for_user(user)
            response.data = {
                'access': str(refresh.access_token),
                'refresh': str(refresh),
            }

            # Set cookies for access and refresh tokens
            response.set_cookie(
                'access',
                str(refresh.access_token),
                max_age=settings.AUTH_COOKIE_ACCESS_MAX_AGE,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=True,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )
            response.set_cookie(
                'refresh',
                str(refresh),
                max_age=settings.AUTH_COOKIE_REFRESH_MAX_AGE,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=True,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )

            return response
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomLoginView(APIView):
    """
    Unified login view that handles both regular and staff user login using JWT tokens.
    - Validates the user's credentials using a custom login serializer.
    - If valid, generates access and refresh tokens with appropriate lifetimes.
    - For staff users: shorter token lifetimes (8 hours access, 7 days refresh)
    - For regular users: longer token lifetimes (90 days for both)
    - Stores tokens in cookies for seamless experience.
    """

    def post(self, request, *args, **kwargs):
        serializer = CustomLoginSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']

        # Generate tokens with appropriate lifetimes based on user type
        refresh = RefreshToken.for_user(user)

        # Set staff-specific token lifetimes
        if user.is_staff:
            from datetime import timedelta
            # Override token lifetimes for staff users
            refresh.set_exp(lifetime=timedelta(days=7))  # 7 days for staff refresh
            refresh.access_token.set_exp(lifetime=timedelta(hours=8))  # 8 hours for staff access

        response = Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user_type': 'staff' if user.is_staff else 'customer',
            'message': 'Login successful'
        }, status=status.HTTP_200_OK)

        # Set cookies with appropriate max_age based on user type
        if user.is_staff:
            # Staff users get shorter-lived cookies
            access_max_age = 8 * 60 * 60  # 8 hours
            refresh_max_age = 7 * 24 * 60 * 60  # 7 days
        else:
            # Regular users get longer-lived cookies
            access_max_age = settings.AUTH_COOKIE_ACCESS_MAX_AGE
            refresh_max_age = settings.AUTH_COOKIE_REFRESH_MAX_AGE

        response.set_cookie(
            'access',
            str(refresh.access_token),
            max_age=access_max_age,
            path=settings.AUTH_COOKIE_PATH,
            secure=settings.AUTH_COOKIE_SECURE,
            httponly=settings.AUTH_COOKIE_HTTP_ONLY,
            samesite=settings.AUTH_COOKIE_SAMESITE,
        )
        response.set_cookie(
            'refresh',
            str(refresh),
            max_age=refresh_max_age,
            path=settings.AUTH_COOKIE_PATH,
            secure=settings.AUTH_COOKIE_SECURE,
            httponly=settings.AUTH_COOKIE_HTTP_ONLY,
            samesite=settings.AUTH_COOKIE_SAMESITE,
        )

        return response


class LogoutView(APIView):
    """
    Unified logout view that handles both regular and staff user logout.
    - Deletes both the access and refresh tokens stored in cookies.
    - Also clears any legacy admin cookies that might exist.
    - Returns an HTTP 204 response (No Content) after a successful logout.
    """

    def post(self, request, *args, **kwargs):
        response = Response({
            'message': 'Successfully logged out'
        }, status=status.HTTP_200_OK)

        # Clear main authentication cookies
        response.delete_cookie('access', path='/')
        response.delete_cookie('refresh', path='/')

        # Clear any legacy admin cookies that might exist
        response.delete_cookie('admin_access_token', path='/', samesite='None')
        response.delete_cookie('admin_refresh_token', path='/', samesite='None')

        return response


class PasswordResetRequestView(APIView):
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email_or_phone = serializer.validated_data['email_or_phone']
            try:
                user = User.objects.get(email=email_or_phone)
                is_email = True
            except ObjectDoesNotExist:
                try:
                    user = User.objects.get(phone_number=email_or_phone)
                    is_email = False
                except ObjectDoesNotExist:
                    return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

            code = generate_verification_code()
            if is_email:
                subject = "Password Reset"
                message = f"Your password reset code is: {code}"
                # send_email(user.email, subject, message)
                print(f"Your password reset code is: {code}")
            else:
                # send_sms(user.phone_number, f"Your password reset code is: {code}")
                print(f"Your password reset code is: {code}")

            # Store the code in cache or database for verification
            cache.set(f'password_reset_code_{user.id}', code, timeout=900)  # 15 minutes
            cache.set(f'user_{email_or_phone}', user.id, timeout=900)  # 15 minutes

            return Response({'message': 'Verification code sent', 'email_or_phone': str(email_or_phone)},
                            status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(APIView):
    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            email_or_phone = serializer.validated_data['email_or_phone']
            user_id = cache.get(f'user_{email_or_phone}')
            stored_code = cache.get(f'password_reset_code_{user_id}')

            # Check if the code matches and hasn't expired
            if not stored_code:
                return Response({"error": "The reset code has expired. Please request a new code."},
                                status=status.HTTP_400_BAD_REQUEST)
            if stored_code != serializer.validated_data['code']:
                return Response({"error": "Invalid code."}, status=status.HTTP_400_BAD_REQUEST)

            # Reset the user's password
            user = User.objects.get(id=user_id)
            user.set_password(serializer.validated_data['new_password'])
            user.save()

            # Clean up cache
            cache.delete(f'password_reset_code_{user_id}')
            cache.delete(f'user_{email_or_phone}')

            return Response({"message": "Password reset successful."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            if not user.check_password(serializer.validated_data['old_password']):
                return Response({"error": "Wrong password"}, status=status.HTTP_400_BAD_REQUEST)

            user.set_password(serializer.validated_data['new_password'])
            user.save()

            # Send notification email/SMS
            if user.email:
                subject = "Password Change Notification"
                message = "Your password has been changed."
                send_email(user.email, subject, message)
            elif user.phone_number:
                send_sms(user.phone_number, "Password Change Notification")

            return Response({"message": "Password changed successfully"}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UpdateContactInfo(APIView):
    """
    Handles the updating of a user's contact information (email or phone number).
    - Requires the user to be authenticated.
    - Validates the provided email or phone number.
    - Generates and sends a verification code to the new contact information.
    - Limits the number of verification attempts to prevent abuse.
    """
    permission_classes = [IsAuthenticated]

    def patch(self, request):
        serializer = ConsolidatedUserSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data.get('email')
            phone_number = serializer.validated_data.get('phone_number')
            user = request.user
            key = f'verification_attempts_{user.id}'
            attempts = cache.get(key, 0)

            if attempts >= 6:
                return Response({"error": "Too many attempts. Please wait before trying again."},
                                status=status.HTTP_429_TOO_MANY_REQUESTS)

            code = generate_verification_code()
            try:
                if email:
                    if user.email == email and user.is_email_verified:
                        return Response({"message": "Email is already verified."}, status=status.HTTP_200_OK)

                    subject = 'Verification Code'
                    message = f'Your verification code is: {code}'
                    print(message, flush=True)  # Debugging output
                    # send_email(email, subject, message)

                    cache.set(f'verification_code_{user.id}_email', code, timeout=900)
                    cache.set(f'pending_email_{user.id}', email, timeout=900)  # Store the new email temporarily
                    cache.set(key, attempts + 1, timeout=900)
                    return Response({'message': 'Verification code sent to email.', 'username': email},
                                    status=status.HTTP_200_OK)

                elif phone_number:
                    if user.phone_number == phone_number and user.is_phone_verified:
                        return Response({"message": "Phone number is already verified."}, status=status.HTTP_200_OK)

                    text = f'Your verification code is: {code}'
                    print(text, flush=True)  # Debugging output
                    # send_sms(phone_number, text)

                    cache.set(f'verification_code_{user.id}_phone', code, timeout=900)
                    cache.set(f'pending_phone_{user.id}', phone_number, timeout=900)  # Store the new phone temporarily
                    cache.set(key, attempts + 1, timeout=900)
                    return Response({'message': 'Verification code sent to phone.', 'username': str(phone_number)},
                                    status=status.HTTP_200_OK)

                else:
                    return Response({"error": "Either email or phone number must be provided."},
                                    status=status.HTTP_400_BAD_REQUEST)

            except Exception as e:
                return Response({'error': f'Failed to send verification code: {str(e)}'},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyUpdateContactInfo(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = VerificationSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            code = serializer.validated_data.get('code')

            stored_email_code = cache.get(f'verification_code_{user.id}_email')
            stored_phone_code = cache.get(f'verification_code_{user.id}_phone')

            # Verify and update email
            if stored_email_code == code:
                new_email = cache.get(f'pending_email_{user.id}')
                if new_email:
                    user.email = new_email
                    user.is_email_verified = True
                    cache.delete(f'verification_code_{user.id}_email')
                    cache.delete(f'pending_email_{user.id}')
                    user.save()
                    return Response({"message": "Email updated successfully."}, status=status.HTTP_200_OK)

            # Verify and update the phone number
            elif stored_phone_code == code:
                new_phone_number = cache.get(f'pending_phone_{user.id}')
                if new_phone_number:
                    user.phone_number = new_phone_number
                    user.is_phone_verified = True
                    cache.delete(f'verification_code_{user.id}_phone')
                    cache.delete(f'pending_phone_{user.id}')
                    user.save()
                    return Response({"message": "Phone number updated successfully."}, status=status.HTTP_200_OK)

            return Response({"error": "Invalid verification code."}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Currently not functional
class ChangePrimaryAuthMethodView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = ChangePrimaryAuthMethodSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            new_method = serializer.validated_data['new_primary_method']

            if new_method == 'email' and not user.email:
                return Response({"error": "Email not set for this account"}, status=status.HTTP_400_BAD_REQUEST)
            elif new_method == 'phone' and not user.phone_number:
                return Response({"error": "Phone number not set for this account"}, status=status.HTTP_400_BAD_REQUEST)

            # Update the primary auth method (you might need to add a field to your User model for this)
            user.primary_auth_method = new_method
            user.save()

            return Response({"message": f"Primary authentication method changed to {new_method}"},
                            status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Overrides the default JWT token obtain view.
    - Responsible for issuing an access token and refresh token when a user logs in.
    - Stores these tokens in HTTP-only cookies for improved security.
    """

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            access_token = response.data.get('access')
            refresh_token = response.data.get('refresh')

            response.set_cookie(
                'access',
                access_token,
                max_age=settings.AUTH_COOKIE_ACCESS_MAX_AGE,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=settings.AUTH_COOKIE_HTTP_ONLY,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )
            response.set_cookie(
                'refresh',
                refresh_token,
                max_age=settings.AUTH_COOKIE_REFRESH_MAX_AGE,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=settings.AUTH_COOKIE_HTTP_ONLY,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )

        return response


# class CustomTokenRefreshView(TokenRefreshView):
#     def post(self, request, *args, **kwargs):
#         refresh_token = request.COOKIES.get('refresh')
#
#         if refresh_token:
#             request.data['refresh'] = refresh_token
#
#         response = super().post(request, *args, **kwargs)
#
#         if response.status_code == 200:
#             access_token = response.data.get('access')
#
#             response.set_cookie(
#                 'access',
#                 access_token,
#                 max_age=settings.AUTH_COOKIE_ACCESS_MAX_AGE,
#                 path=settings.AUTH_COOKIE_PATH,
#                 secure=settings.AUTH_COOKIE_SECURE,
#                 httponly=settings.AUTH_COOKIE_HTTP_ONLY,
#                 samesite=settings.AUTH_COOKIE_SAMESITE,
#             )
#
#         return response


class CustomTokenRefreshView(TokenRefreshView):
    """
    Unified token refresh view that handles both regular and staff users.
    - Checks for the refresh token in cookies.
    - If valid, generates new tokens with appropriate lifetimes based on user type.
    - For staff users: shorter token lifetimes (8 hours access, 7 days refresh)
    - For regular users: longer token lifetimes (90 days for both)
    """

    def post(self, request, *args, **kwargs):
        # Get the refresh token from the cookie
        refresh_token = request.COOKIES.get('refresh')

        if refresh_token:
            # If the refresh token is in the cookie, add it to the request data
            request.data['refresh'] = refresh_token

        # Call the parent class's post method
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # If the refresh was successful, get the new tokens
            access_token = response.data.get('access')
            new_refresh_token = response.data.get('refresh')

            # Determine if this is a staff user by checking the token
            try:
                from rest_framework_simplejwt.tokens import RefreshToken
                token = RefreshToken(refresh_token)
                user_id = token['user_id']
                user = User.objects.get(id=user_id)
                is_staff = user.is_staff
            except:
                is_staff = False

            # Set appropriate cookie lifetimes based on user type
            if is_staff:
                access_max_age = 8 * 60 * 60  # 8 hours for staff
                refresh_max_age = 7 * 24 * 60 * 60  # 7 days for staff
            else:
                access_max_age = settings.AUTH_COOKIE_ACCESS_MAX_AGE
                refresh_max_age = settings.AUTH_COOKIE_REFRESH_MAX_AGE

            # Set the new access token in a cookie
            response.set_cookie(
                'access',
                access_token,
                max_age=access_max_age,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=settings.AUTH_COOKIE_HTTP_ONLY,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )
            # Set the new refresh token in a cookie
            response.set_cookie(
                'refresh',
                new_refresh_token,
                max_age=refresh_max_age,
                path=settings.AUTH_COOKIE_PATH,
                secure=settings.AUTH_COOKIE_SECURE,
                httponly=settings.AUTH_COOKIE_HTTP_ONLY,
                samesite=settings.AUTH_COOKIE_SAMESITE,
            )

        return response


class CustomTokenVerifyView(TokenVerifyView):
    """
    Verifies the validity of the access token.
    - It checks for the access token in cookies and verifies its authenticity.
    - If the token is valid, the user remains authenticated.
    """

    def post(self, request, *args, **kwargs):
        access_token = request.COOKIES.get('access')

        if access_token:
            request.data['token'] = access_token

        return super().post(request, *args, **kwargs)


class CurrentUserView(APIView):
    """
    Fetches the details of the currently authenticated user.
    - Requires the user to be authenticated (i.e., have a valid JWT token).
    - Returns the serialized user data in the response.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = ConsolidatedUserSerializer(request.user)
        return Response(serializer.data)


class GoogleLoginView(APIView):
    def post(self, request, *args, **kwargs):
        google_token = request.data.get('access_token')

        if not google_token:
            return Response({'error': 'Google access token is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            strategy = load_strategy(request)
            backend = load_backend(strategy=strategy, name='google-oauth2', redirect_uri=None)
            user = backend.do_auth(google_token)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        if user and user.is_active:
            # Generate the refresh and access tokens for the authenticated user
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            # Create the response with refresh and access tokens in the body
            response = Response({
                'refresh_token': str(refresh),
                'access_token': access_token,
            }, status=status.HTTP_200_OK)

            # Set the access token in an HTTP-only, secure cookie
            response.set_cookie(
                key='access',
                value=access_token,
                httponly=True,
                secure=settings.AUTH_COOKIE_SECURE,  # Ensure this is True for production (HTTPS)
                samesite=settings.AUTH_COOKIE_SAMESITE,  # Prevent CSRF in modern browsers
                max_age=settings.AUTH_COOKIE_ACCESS_MAX_AGE,  # Adjust this based on your access token expiration
                path=settings.AUTH_COOKIE_PATH,  # Ensure it matches your app's settings
            )

            # Set the refresh token in a secure HTTP-only cookie
            response.set_cookie(
                key='refresh',
                value=str(refresh),
                httponly=True,
                secure=settings.AUTH_COOKIE_SECURE,  # Ensure this is True for production (HTTPS)
                samesite=settings.AUTH_COOKIE_SAMESITE,
                max_age=settings.AUTH_COOKIE_REFRESH_MAX_AGE,  # Adjust this based on your refresh token expiration
                path=settings.AUTH_COOKIE_PATH,
            )

            return response

        return Response({'error': 'Authentication failed'}, status=status.HTTP_401_UNAUTHORIZED)


class FacebookLoginView(APIView):
    def post(self, request, *args, **kwargs):
        # Get the Facebook access token from request
        facebook_token = request.data.get('access_token')

        # Check if the access token is present
        if not facebook_token:
            return Response({'error': 'Facebook access token is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Load strategy and backend
            strategy = load_strategy(request)
            backend = load_backend(strategy=strategy, name='facebook', redirect_uri=None)

            # Authenticate the user using the Facebook access token
            user = backend.do_auth(facebook_token)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # If user authentication is successful
        if user and user.is_active:
            # Generate JWT tokens for the authenticated user
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            # Setting tokens in cookies
            response = Response({
                'refresh_token': str(refresh),
                'access_token': access_token,
            }, status=status.HTTP_200_OK)

            # Set the access token in a cookie (secure, httponly, etc.)
            response.set_cookie(
                key='access',
                value=access_token,
                httponly=True,
                secure=True,
                samesite='Lax',
                max_age=3600,
            )

            # Set the refresh token in a cookie
            response.set_cookie(
                key='refresh',
                value=str(refresh),
                httponly=True,
                secure=True,
                samesite='Lax',
                max_age=14 * 24 * 3600,  # 14 days for refresh token
            )

            return response

        # If authentication fails
        return Response({'error': 'Authentication failed'}, status=status.HTTP_401_UNAUTHORIZED)
