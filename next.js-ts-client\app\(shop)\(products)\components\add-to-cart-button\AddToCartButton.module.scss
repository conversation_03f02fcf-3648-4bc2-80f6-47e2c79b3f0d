@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.add_to_cart_container {
  grid-area: add-to-cart;
  width: 100%;
}

.add_to_cart_btn {
  @include btn(#fff, $primary-blue);
  text-transform: uppercase;
  width: 100%;
  padding: .6rem 0rem;
  transition: all .2s ease-in-out;
  @include flexbox(center, center);
  gap: 0.5rem;

  &:hover:not(:disabled) {
    background-color: color.adjust($primary-blue, $lightness: -5%);
  }

  &:disabled {
    background-color: $primary-blue;
    color: #fff;
    opacity: 0.6;
    cursor: not-allowed;
    border: none;
  }
}

.loading_spinner {
  @include flexbox(center, center);
  gap: 0.5rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error_message {
  color: $primary-red;
  font-size: $font-size-1;
  margin-top: 0.5rem;
  text-align: center;
}