import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { AxiosError } from 'axios'
import APIClient from '../lib/api-client'
import cartStore from '../stores/cart-store'
import { CACHE_KEY_CART_ITEMS, SIMPLE_CART } from '../constants/constants'
import { ErrorResponse } from '../types/types'

interface CartItemSelectionRequest {
  is_selected: boolean
}

interface BulkSelectionRequest {
  item_ids?: number[]
  select_all?: boolean
  deselect_all?: boolean
}

interface SelectionResponse {
  success: boolean
  updated_count?: number
  message: string
  id?: number
  is_selected?: boolean
}

export const useCartItemSelection = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  return useMutation<
    SelectionResponse,
    AxiosError<ErrorResponse>,
    { itemId: number; isSelected: boolean }
  >({
    mutationFn: async ({ itemId, isSelected }) => {
      const apiClient = new APIClient<
        SelectionResponse,
        CartItemSelectionRequest
      >(`/cart/${cartId}/items/${itemId}/select/`)
      return apiClient.patch({ is_selected: isSelected })
    },
    // Optimistic updates: apply selection change locally before server responds
    onMutate: async (variables) => {
      const { itemId, isSelected } = variables
      if (!cartId)
        return {
          previousCart: null,
          previousSelected: cartStore.getState().selectedItemIds || [],
        }

      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({
        queryKey: [CACHE_KEY_CART_ITEMS, cartId],
      })

      const previousCart = queryClient.getQueryData<unknown>([
        CACHE_KEY_CART_ITEMS,
        cartId,
      ])

      // Optimistically update the cached cart items if shape matches { cart_items: Array }
      if (previousCart && typeof previousCart === 'object') {
        const prevObj = previousCart as Record<string, unknown>
        if (Array.isArray(prevObj.cart_items)) {
          const prev = previousCart as {
            cart_items: Array<Record<string, unknown>>
          }
          const newCart = {
            ...prev,
            cart_items: prev.cart_items.map((it) => {
              // runtime-safe check and shallow update
              if (it && typeof it === 'object') {
                const rec = it as Record<string, unknown>
                if (rec.id !== undefined && Number(rec.id) === itemId) {
                  return { ...rec, is_selected: isSelected }
                }
              }
              return it
            }),
          }
          queryClient.setQueryData([CACHE_KEY_CART_ITEMS, cartId], newCart)
        }
      }

      // Optimistically update local persisted selection array
      const currentSelection = cartStore.getState().selectedItemIds || []
      const has = currentSelection.includes(itemId)
      let newSelection = currentSelection
      if (isSelected && !has) newSelection = [...currentSelection, itemId]
      else if (!isSelected && has)
        newSelection = currentSelection.filter((id) => id !== itemId)
      cartStore.getState().setSelectedItems(newSelection)

      return { previousCart, previousSelected: currentSelection }
    },
    onError: (
      error: AxiosError<ErrorResponse>,
      variables: { itemId: number; isSelected: boolean },
      context: unknown
    ) => {
      console.error('Error updating item selection:', error)
      // rollback cache
      if (cartId && context && typeof context === 'object') {
        const ctx = context as {
          previousCart?: unknown
          previousSelected?: number[]
        }
        if (ctx.previousCart) {
          queryClient.setQueryData(
            [CACHE_KEY_CART_ITEMS, cartId],
            ctx.previousCart
          )
        }
        if (ctx.previousSelected) {
          cartStore.getState().setSelectedItems(ctx.previousSelected)
        }
      }
    },
    onSettled: () => {
      // Only invalidate simple cart cache to avoid triggering expensive useCart queries
      if (cartId) {
        queryClient.invalidateQueries({
          queryKey: [SIMPLE_CART, cartId],
        })
      }
    },
  })
}

export const useBulkCartSelection = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  const bulkSelectMutation = useMutation<
    SelectionResponse,
    AxiosError<ErrorResponse>,
    BulkSelectionRequest
  >({
    mutationFn: async (data) => {
      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(
        `/cart/${cartId}/items/bulk-select/`
      )
      return apiClient.post(data)
    },
    onSuccess: (data, variables) => {
      // Update local store based on operation
      if (variables.select_all) {
        // This will be handled by the parent component that knows all item IDs
      } else if (variables.item_ids) {
        cartStore.getState().setSelectedItems(variables.item_ids)
      }

      // Only invalidate simple cart cache to avoid triggering expensive useCart queries
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART, cartId],
      })
    },
    onError: (error) => {
      console.error('Error bulk selecting items:', error)
    },
  })

  const bulkDeselectMutation = useMutation<
    SelectionResponse,
    AxiosError<ErrorResponse>,
    BulkSelectionRequest
  >({
    mutationFn: async (data) => {
      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(
        `/cart/${cartId}/items/bulk-deselect/`
      )
      return apiClient.post(data)
    },
    onSuccess: (data, variables) => {
      // Update local store based on operation
      if (variables.deselect_all) {
        cartStore.getState().clearSelection()
      } else if (variables.item_ids) {
        // Remove specific items from selection
        const currentSelection = cartStore.getState().selectedItemIds || []
        const newSelection = currentSelection.filter(
          (id) => !variables.item_ids?.includes(id)
        )
        cartStore.getState().setSelectedItems(newSelection)
      }

      // Only invalidate simple cart cache to avoid triggering expensive useCart queries
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART, cartId],
      })
    },
    onError: (error) => {
      console.error('Error bulk deselecting items:', error)
    },
  })

  return {
    bulkSelect: bulkSelectMutation.mutate,
    bulkDeselect: bulkDeselectMutation.mutate,
    isSelectLoading: bulkSelectMutation.isPending,
    isDeselectLoading: bulkDeselectMutation.isPending,
    selectError: bulkSelectMutation.error,
    deselectError: bulkDeselectMutation.error,
  }
}

export const useSelectedCartSummary = () => {
  const { cartId } = cartStore()

  const fetchSummary = useCallback(() => {
    const apiClient = new APIClient<{
      selected_items_count: number
      total_items_count: number
      selected_total_price: number
      selected_total_weight: number
      all_items_selected: boolean
      selected_item_ids: number[]
    }>(`/cart/${cartId}/selected_summary/`)

    return apiClient.get()
  }, [cartId])

  return { fetchSummary }
}

// Hook to sync frontend selection state with backend
// export const useSyncCartSelection = () => {
//   // avoid subscribing to the store here so the returned function identity is stable
//   const getSelected = () => cartStore.getState().selectedItemIds || []
//   const setSelected = (ids: number[]) =>
//     cartStore.getState().setSelectedItems(ids)

//   const syncWithBackend = useCallback(
//     (cartItems: Array<{ id: number; is_selected?: boolean }>) => {
//       // Compute backend selected ids
//       const backendSelectedIds = cartItems
//         .filter((item) => item.is_selected)
//         .map((item) => item.id)

//       // Compare lengths first for a cheap check, then confirm all ids match (order-insensitive)
//       const current = getSelected()
//       if (
//         backendSelectedIds.length === current.length &&
//         backendSelectedIds.every((id) => current.includes(id))
//       ) {
//         // No change -> avoid calling setSelected to prevent triggering effects
//         return
//       }

//       // Only update when backend selection differs from current
//       setSelected(backendSelectedIds)
//     },
//     []
//   )

//   return { syncWithBackend }
// }
