@use '../../../../../../src/scss/variables' as *;
@use '../../../../../../src/scss/mixins' as *;
@use 'sass:map';

.product_list__container {
  @include flexbox(center, center, column);
  min-height: 60vh;
  padding: 1rem 0;
}

.mobile_controls {
  background: linear-gradient(135deg, $lighten-blue 0%, $sky-light-blue 100%);
  @include flexbox(space-between, center);
  width: 100%;
  padding: $padding-3 $padding-4;
  border-radius: $border-radius-3;
  margin-bottom: 1rem;
  box-shadow: $box-shadow-1;

  button {
    @include flexbox(flex-start, center);
    gap: 0.5rem;
    padding: $padding-2 $padding-4;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: $border-radius-2;
    cursor: pointer;
    color: $primary-dark-text-color;
    font-weight: map.get($font-weight, 'medium');
    font-size: $font-size-2;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: $primary-blue;
      transform: translateY(-2px);
      box-shadow: $box-shadow-blue-1;
    }

    &:active {
      transform: translateY(0);
    }

    span {
      background-color: $primary-blue;
      color: white;
      padding: 2px 8px;
      border-radius: $border-radius-1;
      font-size: $font-size-1;
      font-weight: map.get($font-weight, 'bold');
    }

    svg {
      font-size: $font-size-3;
      color: $primary-blue;
    }
  }
}

.filters {
  display: none;
  width: 100%;
  margin-bottom: 1rem;
  background: white;
  border-radius: $border-radius-3;
  box-shadow: $box-shadow-1;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &.show {
    display: block;
    animation: slideDown 0.3s ease-out;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product_list__wrapper {
  width: 100%;
  flex: 1;
}

.sorting {
  display: none;
  margin: 0.5rem 1rem 1rem 0;
  text-align: right;

  &.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.product_list {
  margin: 1rem 0 2rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
  list-style-type: none;
  padding: 0;
  justify-items: center;

  li {
    width: 100%;
    max-width: 280px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}

.empty_products {
  width: 100%;
  grid-column: 1 / -1;
  text-align: center;
  font-style: italic;
  font-size: $font-size-5;
  color: $primary-lighter-text-color;
  padding: 3rem 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: $border-radius-3;
  border: 2px dashed #dee2e6;
  margin: 2rem 0;
}

// Tablet styles
@media (width > $tablet) {
  .product_list {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 2rem;
    padding: 0 1rem;
  }

  .mobile_controls {
    padding: $padding-4 $padding-5;
  }
}

// Laptop and desktop styles
@media (width > $laptop) {
  .product_list__container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
    padding: 2rem 0;
  }

  .product_list__wrapper {
    .product_list {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 2rem;
      padding: 0;
      justify-items: start;
    }
  }

  .mobile_controls {
    display: none;
  }

  .filters {
    display: block !important;
    width: 100%;
    margin-bottom: 0;
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: $primary-blue;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: $primary-dark-blue;
    }
  }

  .sorting {
    display: block !important;
    text-align: right;
    margin: 0 0 1.5rem 0;
  }
}

// Large monitor styles
@media (width > $monitor) {
  .product_list__container {
    grid-template-columns: 320px 1fr;
    gap: 3rem;
  }

  .product_list__wrapper {
    .product_list {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 2.5rem;
    }
  }
}

// Loading and error states
.loading_container,
.error_container {
  @include flexbox(center, center, column);
  min-height: 40vh;
  padding: 2rem;
  text-align: center;
}

.error_container {
  .error_message {
    margin-top: 1rem;
    color: $error-text;
    font-size: $font-size-4;
  }
}

// Skeleton loading styles
.product_card_skeleton {
  background: white;
  border-radius: $border-radius-3;
  box-shadow: $box-shadow-1;
  overflow: hidden;
  height: 100%;
  min-height: 350px;

  .image_skeleton {
    width: 100%;
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  .info_skeleton {
    padding: 1rem;

    .title_skeleton {
      height: 20px;
      width: 80%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin-bottom: 0.5rem;
      border-radius: 4px;
    }

    .rating_skeleton {
      height: 16px;
      width: 60%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin-bottom: 1rem;
      border-radius: 4px;
    }

    .price_skeleton {
      @include flexbox(flex-start, center);
      gap: 0.5rem;

      .current_price_skeleton,
      .original_price_skeleton {
        height: 18px;
        border-radius: 4px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      .current_price_skeleton {
        width: 50px;
      }

      .original_price_skeleton {
        width: 40px;
        opacity: 0.7;
      }
    }
  }
}

.sort_select_skeleton {
  height: 40px;
  width: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: $border-radius-2;
  margin-left: auto;
}

.pagination_skeleton {
  @include flexbox(center, center);
  gap: 0.5rem;
  margin: 2rem 0;

  .pagination_button_skeleton,
  .pagination_number_skeleton {
    height: 40px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: $border-radius-2;
  }

  .pagination_button_skeleton {
    width: 40px;
  }

  .pagination_number_skeleton {
    width: 35px;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .filters.show,
  .sorting.show,
  .product_list li {
    animation: none;
    transition: none;
  }

  .mobile_controls button:hover,
  .product_list li:hover {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .mobile_controls {
    background: white;
    border: 2px solid black;
  }

  .filters {
    border: 2px solid black;
  }

  .empty_products {
    border: 3px solid black;
    background: white;
  }
}
