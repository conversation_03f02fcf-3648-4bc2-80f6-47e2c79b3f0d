import styles from './ProductListSkeleton.module.scss'

const ProductCardSkeleton = () => (
  <div className={styles.product_card_skeleton}>
    <div className={styles.image_skeleton}></div>
    <div className={styles.info_skeleton}>
      <div className={styles.title_skeleton}></div>
      <div className={styles.rating_skeleton}></div>
      <div className={styles.price_skeleton}>
        <div className={styles.current_price_skeleton}></div>
        <div className={styles.original_price_skeleton}></div>
      </div>
    </div>
  </div>
)

const FilterSkeleton = () => (
  <div className={styles.filter_skeleton}>
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      <div className={styles.price_range_skeleton}>
        <div className={styles.range_label_skeleton}></div>
        <div className={styles.range_slider_skeleton}></div>
        <div className={styles.range_label_skeleton}></div>
        <div className={styles.range_slider_skeleton}></div>
      </div>
    </div>
    
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      {[...Array(3)].map((_, i) => (
        <div key={i} className={styles.filter_option_skeleton}>
          <div className={styles.checkbox_skeleton}></div>
          <div className={styles.option_label_skeleton}></div>
        </div>
      ))}
    </div>
    
    <div className={styles.filter_section}>
      <div className={styles.filter_title_skeleton}></div>
      {[...Array(4)].map((_, i) => (
        <div key={i} className={styles.filter_option_skeleton}>
          <div className={styles.checkbox_skeleton}></div>
          <div className={styles.option_label_skeleton}></div>
        </div>
      ))}
    </div>
  </div>
)

const MobileControlsSkeleton = () => (
  <div className={styles.mobile_controls_skeleton}>
    <div className={styles.control_button_skeleton}></div>
    <div className={styles.control_button_skeleton}></div>
  </div>
)

const SortingSkeleton = () => (
  <div className={styles.sorting_skeleton}>
    <div className={styles.sort_select_skeleton}></div>
  </div>
)

interface ProductListSkeletonProps {
  showFilters?: boolean
  showSorting?: boolean
  productCount?: number
}

const ProductListSkeleton = ({ 
  showFilters = false, 
  showSorting = false, 
  productCount = 12 
}: ProductListSkeletonProps) => {
  return (
    <div className="container">
      <div className={styles.product_list_skeleton_container}>
        {/* Mobile controls skeleton */}
        <MobileControlsSkeleton />

        {/* Filters section skeleton */}
        <section className={`${styles.filters_skeleton} ${showFilters ? styles.show : ''}`}>
          <FilterSkeleton />
        </section>

        <section className={styles.product_list_wrapper_skeleton}>
          {/* Sorting section skeleton */}
          <div className={`${styles.sorting_wrapper} ${showSorting ? styles.show : ''}`}>
            <SortingSkeleton />
          </div>

          {/* Product list skeleton */}
          <ul className={styles.product_list_skeleton}>
            {[...Array(productCount)].map((_, i) => (
              <li key={i}>
                <ProductCardSkeleton />
              </li>
            ))}
          </ul>

          {/* Pagination skeleton */}
          <div className={styles.pagination_skeleton}>
            <div className={styles.pagination_button_skeleton}></div>
            {[...Array(5)].map((_, i) => (
              <div key={i} className={styles.pagination_number_skeleton}></div>
            ))}
            <div className={styles.pagination_button_skeleton}></div>
          </div>
        </section>
      </div>
    </div>
  )
}

export default ProductListSkeleton
