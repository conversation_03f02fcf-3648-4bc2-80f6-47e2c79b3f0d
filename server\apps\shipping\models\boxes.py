from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Box(models.Model):
    """Shipping box configurations for packing optimization"""
    
    title = models.CharField(max_length=100, help_text="Box title/name")
    internal_length = models.DecimalField(
        max_digits=8, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Internal length in centimeters"
    )
    internal_width = models.DecimalField(
        max_digits=8, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Internal width in centimeters"
    )
    internal_height = models.DecimalField(
        max_digits=8, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Internal height in centimeters"
    )
    max_weight = models.DecimalField(
        max_digits=8, decimal_places=2,
        validators=[MinValueValidator(Decimal('1.00'))],
        help_text="Maximum weight capacity in grams"
    )
    cost = models.DecimalField(
        max_digits=6, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Box cost in USD"
    )
    volume = models.DecimalField(
        max_digits=12, decimal_places=4,
        editable=False,
        help_text="Computed volume in cubic centimeters"
    )
    is_mailer = models.BooleanField(
        default=False,
        help_text="Is this a padded mailer envelope?"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Is this box available for use?"
    )
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Priority for box selection (higher = preferred)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Shipping Box"
        verbose_name_plural = "Shipping Boxes"
        ordering = ['priority', 'volume', 'cost']

    def __str__(self):
        return f"{self.title} ({self.internal_length}x{self.internal_width}x{self.internal_height}cm)"

    def calculate_volume(self):
        """Calculate internal volume in cubic centimeters"""
        # Handle case where dimensions might be None (e.g., before saving)
        if not all([self.internal_length, self.internal_width, self.internal_height]):
            return 0
        return self.internal_length * self.internal_width * self.internal_height

    def get_efficiency_ratio(self):
        """Calculate cost per cubic centimeter"""
        # Handle case where volume might be None (e.g., before saving)
        volume = self.volume or self.calculate_volume()
        # Also check if cost is available
        if volume and volume > 0 and self.cost is not None:
            return float(self.cost) / float(volume)
        return float('inf')

    def can_fit_weight(self, weight):
        """Check if box can handle the given weight"""
        return weight <= self.max_weight

    def can_fit_dimensions(self, length, width, height):
        """Check if item dimensions fit in box"""
        return (length <= self.internal_length and 
                width <= self.internal_width and 
                height <= self.internal_height)

    def save(self, *args, **kwargs):
        # Calculate volume before saving
        self.volume = self.calculate_volume()
        super().save(*args, **kwargs)

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate dimensions are positive
        if any(dim <= 0 for dim in [self.internal_length, self.internal_width, self.internal_height]):
            raise ValidationError("All dimensions must be greater than 0")
        
        # Validate max_weight is positive
        if self.max_weight <= 0:
            raise ValidationError("Maximum weight must be greater than 0")
        
        # Validate cost is non-negative
        if self.cost < 0:
            raise ValidationError("Cost cannot be negative")
