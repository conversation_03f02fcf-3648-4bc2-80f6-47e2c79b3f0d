'use client'

import Modal from '@/src/components/utils/modal/Modal'
import { useChangeAuthInfo } from '@/src/hooks/auth-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { changeAuthInfoSchema } from '@/src/schemas/schemas'
import authStore from '@/src/stores/auth-store'
import { ErrorResponse } from '@/src/types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import AuthLayout from '../../AuthLayout'
import { NewAuthInfoShape } from '../phone/page'

const ChangeEmail = () => {
  const router = useRouter()
  const { mutation } = useChangeAuthInfo()
  const { isLoggedIn } = authStore()
  const { data: customerData } = useCustomerDetails(isLoggedIn)

  // Initialize the form with default values from customerData
  const { register, handleSubmit, formState: { errors }, setValue } = useForm<NewAuthInfoShape>({
    resolver: zodResolver(changeAuthInfoSchema),
    defaultValues: {
      email: customerData?.email || '',
    }
  })

  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState<NewAuthInfoShape | null>(null)

  useEffect(() => {
    // Update the email field when customerData is loaded
    if (customerData?.email) {
      setValue('email', customerData.email)
    }
  }, [customerData, setValue])

  const onSubmit: SubmitHandler<NewAuthInfoShape> = (data) => {
    setFormData(data) // Save form data temporarily
    setShowModal(true) // Open the modal
  }

  const handleConfirm = () => {
    if (formData) {
      mutation.mutate(formData, {
        onSuccess: () => {
          router.push('/change-auth-info/verify')
        }
      })
    }
    setShowModal(false) // Close the modal
  }

  const handleCancel = () => {
    setShowModal(false) // Close the modal
  }

  return (
    <AuthLayout title="Change email" error={mutation.error as AxiosError<ErrorResponse> | null}>
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <div className='form_group'>
          <label className='form_label' htmlFor="email">Enter a new email:</label>
          <input
            className='form_input'
            type="email"
            id="email"
            {...register("email")}
          />
          {errors.email && <p>{errors.email.message} &#128543;</p>}
        </div>
        <section className='btn_container'>
          <button
            type="button"
            className='empty_btn'
            disabled={mutation.isPending}
            onClick={() => router.push('/account/profile')}>Cancel
          </button>
          <button type="submit" className='empty_btn' disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_blue} alt="Loading..." className='loading_svg' />
              'Updating...'
            ) : (
              'Update'
            )}
          </button>
        </section>
      </form>

      {/* Modal for confirmation */}
      <Modal
        title="Confirm Email Change"
        message="Are you sure you want to change your email?"
        show={showModal}
        onClose={handleCancel}
        onConfirm={handleConfirm}
        btn1="Yes"
        btn2="No"
      />
    </AuthLayout>
  )
}

export default ChangeEmail
