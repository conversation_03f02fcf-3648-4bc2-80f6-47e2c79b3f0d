# Test performance, database queries, and integration scenarios
import pytest
from django.test import override_settings
from django.db import connection
from django.test.utils import override_settings
from model_bakery import baker
from apps.products.models import (
    Category, Product, ProductVariant, ProductImage, Review,
    ProductType, Brand, Attribute, AttributeValue, ProductTypeAttribute
)
from apps.customers.models import Customer


@pytest.mark.django_db
class TestQueryOptimization:
    """Test database query optimization and N+1 problems"""
    
    def test_product_list_query_count(self, api_client):
        """Test that product list doesn't have N+1 query problems"""
        # Create test data
        category = baker.make(Category)
        brand = baker.make(Brand)
        product_type = baker.make(ProductType)
        
        products = []
        for i in range(5):
            product = baker.make(
                Product,
                category=category,
                brand=brand,
                product_type=product_type
            )
            variant = baker.make(ProductVariant, product=product, is_active=True)
            baker.make(ProductImage, product_variant=variant)
            products.append(product)
        
        # Test query count
        with self.assertNumQueries(expected_num=10):  # Adjust based on actual optimized queries
            response = api_client.get('/api/products/')
            assert response.status_code == 200
    
    def test_product_detail_query_count(self, api_client):
        """Test that product detail view is optimized"""
        category = baker.make(Category)
        brand = baker.make(Brand)
        product_type = baker.make(ProductType)
        
        product = baker.make(
            Product,
            category=category,
            brand=brand,
            product_type=product_type,
            slug="test-product"
        )
        
        # Create related data
        variant = baker.make(ProductVariant, product=product, is_active=True)
        baker.make(ProductImage, product_variant=variant, _quantity=3)
        
        customer = baker.make(Customer)
        baker.make(Review, product=product, customer=customer, _quantity=2)
        
        # Create attributes
        attribute = baker.make(Attribute)
        baker.make(ProductTypeAttribute, 
                  product_type=product_type, 
                  attribute=attribute, 
                  is_option_selector=True)
        
        # Test query count for detail view
        with self.assertNumQueries(expected_num=15):  # Adjust based on prefetch optimization
            response = api_client.get(f'/api/products/{product.slug}/')
            assert response.status_code == 200
    
    def test_category_hierarchy_query_optimization(self, api_client):
        """Test that category hierarchy queries are optimized"""
        # Create nested categories
        parent = baker.make(Category, title="Electronics")
        child1 = baker.make(Category, title="Laptops", parent=parent)
        child2 = baker.make(Category, title="Desktops", parent=parent)
        grandchild = baker.make(Category, title="Gaming Laptops", parent=child1)
        
        # Create products in different levels
        for category in [parent, child1, child2, grandchild]:
            product = baker.make(Product, category=category)
            baker.make(ProductVariant, product=product, is_active=True)
        
        # Test that listing products by parent category includes children
        with self.assertNumQueries(expected_num=8):  # Should use get_descendants efficiently
            response = api_client.get(f'/api/products/category/{parent.slug}/')
            assert response.status_code == 200
            # Should include products from all descendant categories
            assert len(response.data['results']) == 4


@pytest.mark.django_db
class TestConcurrencyAndRaceConditions:
    """Test concurrent access and race conditions"""
    
    def test_concurrent_review_creation_rating_update(self):
        """Test that concurrent review creation doesn't cause rating calculation issues"""
        product = baker.make(Product, average_rating=0.0)
        customers = baker.make(Customer, _quantity=3)
        
        # Simulate concurrent review creation
        reviews = []
        for i, customer in enumerate(customers):
            review = Review.objects.create(
                title=f"Review {i}",
                description=f"Description {i}",
                rating=i + 3,  # ratings: 3, 4, 5
                product=product,
                customer=customer
            )
            reviews.append(review)
        
        # Final rating should be correct
        product.refresh_from_db()
        expected_rating = (3 + 4 + 5) / 3
        assert abs(product.average_rating - expected_rating) < 0.01
    
    def test_concurrent_product_variant_order_assignment(self):
        """Test that OrderField handles concurrent variant creation"""
        product = baker.make(Product)
        
        # Create multiple variants that might compete for order values
        variants = []
        for i in range(5):
            variant = ProductVariant.objects.create(
                product=product,
                price=100 + i,
                sku=f'SKU-{i}',
                stock_qty=10,
                weight=100
            )
            variants.append(variant)
        
        # Check that all variants have unique orders
        orders = [v.order for v in variants]
        assert len(set(orders)) == len(orders)  # All unique
        assert sorted(orders) == list(range(5))  # Sequential from 0


@pytest.mark.django_db
class TestDataIntegrity:
    """Test data integrity and constraint enforcement"""
    
    def test_product_variant_sku_uniqueness_across_products(self):
        """Test that SKU uniqueness is enforced across all products"""
        product1 = baker.make(Product)
        product2 = baker.make(Product)
        
        # Create variant with specific SKU
        baker.make(ProductVariant, product=product1, sku="UNIQUE-SKU-123")
        
        # Should not be able to create another variant with same SKU
        with pytest.raises(Exception):  # IntegrityError
            ProductVariant.objects.create(
                product=product2,
                sku="UNIQUE-SKU-123",
                price=100,
                stock_qty=10,
                weight=100
            )
    
    def test_category_slug_uniqueness_within_parent(self):
        """Test category slug uniqueness constraint"""
        parent = baker.make(Category)
        
        # Create category with specific slug
        baker.make(Category, parent=parent, slug="test-slug")
        
        # Should not be able to create another category with same slug under same parent
        with pytest.raises(Exception):  # IntegrityError
            Category.objects.create(
                title="Different Title",
                slug="test-slug",
                parent=parent
            )
    
    def test_product_variant_attribute_value_uniqueness(self):
        """Test PVAV uniqueness constraint"""
        variant = baker.make(ProductVariant)
        attr_value = baker.make(AttributeValue)
        
        # Create first PVAV
        from apps.products.models import ProductVariantAttributeValue
        baker.make(ProductVariantAttributeValue, 
                  product_variant=variant, 
                  attribute_value=attr_value)
        
        # Should not be able to create duplicate
        with pytest.raises(Exception):  # IntegrityError
            ProductVariantAttributeValue.objects.create(
                product_variant=variant,
                attribute_value=attr_value
            )


@pytest.mark.django_db
class TestBulkOperations:
    """Test bulk operations and large dataset handling"""
    
    def test_bulk_product_creation_performance(self):
        """Test performance of bulk product creation"""
        category = baker.make(Category)
        brand = baker.make(Brand)
        product_type = baker.make(ProductType)
        
        # Create products in bulk
        products_data = []
        for i in range(100):
            products_data.append(Product(
                title=f"Product {i}",
                slug=f"product-{i}",
                category=category,
                brand=brand,
                product_type=product_type
            ))
        
        # Bulk create should be efficient
        with self.assertNumQueries(expected_num=1):
            Product.objects.bulk_create(products_data)
        
        assert Product.objects.count() == 100
    
    def test_bulk_review_average_rating_update(self):
        """Test average rating calculation with many reviews"""
        product = baker.make(Product, average_rating=0.0)
        customers = baker.make(Customer, _quantity=50)
        
        # Create many reviews
        reviews = []
        total_rating = 0
        for i, customer in enumerate(customers):
            rating = (i % 5) + 1  # Ratings 1-5
            total_rating += rating
            reviews.append(Review(
                title=f"Review {i}",
                description=f"Description {i}",
                rating=rating,
                product=product,
                customer=customer
            ))
        
        Review.objects.bulk_create(reviews)
        
        # Update average rating
        product.update_average_rating()
        
        expected_avg = total_rating / len(customers)
        assert abs(product.average_rating - expected_avg) < 0.01


@pytest.mark.django_db
class TestCacheAndOptimization:
    """Test caching and optimization strategies"""
    
    @override_settings(CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        }
    })
    def test_product_filter_options_caching(self, api_client):
        """Test that filter options could be cached for performance"""
        product_type = baker.make(ProductType)
        brand = baker.make(Brand)
        
        # Create BrandProductType relationship
        from apps.products.models import BrandProductType
        baker.make(BrandProductType, brand=brand, product_type=product_type)
        
        product = baker.make(Product, product_type=product_type, brand=brand)
        baker.make(ProductVariant, product=product, price=100, is_active=True)
        
        # First request
        response1 = api_client.get(f'/api/products/product-filter-options/?product_type_id={product_type.id}')
        assert response1.status_code == 200
        
        # Second request (could be cached)
        response2 = api_client.get(f'/api/products/product-filter-options/?product_type_id={product_type.id}')
        assert response2.status_code == 200
        assert response1.data == response2.data
    
    def test_category_tree_optimization(self):
        """Test that category tree operations are optimized"""
        # Create a complex category tree
        root = baker.make(Category, title="Root")
        
        # Create multiple levels
        level1_categories = []
        for i in range(5):
            cat = baker.make(Category, title=f"Level1-{i}", parent=root)
            level1_categories.append(cat)
        
        level2_categories = []
        for parent in level1_categories:
            for j in range(3):
                cat = baker.make(Category, title=f"Level2-{j}", parent=parent)
                level2_categories.append(cat)
        
        # Test that getting descendants is efficient
        with self.assertNumQueries(expected_num=1):  # MPTT should use single query
            descendants = root.get_descendants()
            assert descendants.count() == 20  # 5 + 15
    
    def assertNumQueries(self, expected_num):
        """Helper method to assert number of database queries"""
        return self.assertNumQueriesContext(expected_num)
    
    class assertNumQueriesContext:
        def __init__(self, expected_num):
            self.expected_num = expected_num
            
        def __enter__(self):
            self.initial_queries = len(connection.queries)
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            final_queries = len(connection.queries)
            actual_num = final_queries - self.initial_queries
            if actual_num != self.expected_num:
                raise AssertionError(
                    f"Expected {self.expected_num} queries, but got {actual_num}"
                )
