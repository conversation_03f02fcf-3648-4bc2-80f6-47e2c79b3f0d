import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import styles from './NameModal.module.scss'
import { getErrorMessage } from '../../utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'
import Underlay from '../../utils/underlay/Underlay'
import Alert from '../../utils/alert/Alert'

const inputSchema = z.object({
  first_name: z.string()
    .min(1, { message: 'First name is required' })
    .max(50, { message: 'First name must be less than 50 characters' })
    .regex(/^[a-zA-Z\s'-]+$/, { message: 'First name can only contain letters, spaces, hyphens, and apostrophes' }),
  last_name: z.string()
    .min(1, { message: 'Last name is required' })
    .max(50, { message: 'Last name must be less than 50 characters' })
    .regex(/^[a-zA-Z\s'-]+$/, { message: 'Last name can only contain letters, spaces, hyphens, and apostrophes' })
})

export type NameFormData = z.infer<typeof inputSchema>

interface Props {
  isPending: boolean
  error: Error | AxiosError | null
  onSubmit: (data: NameFormData) => void
  onClose: () => void
  initialData: { first_name: string; last_name: string }
  isEditing: boolean
  isOpen: boolean
  title: string
}

const NameModal = ({
  onSubmit,
  onClose,
  initialData,
  isEditing,
  isOpen,
  title,
  error,
  isPending
}: Props) => {
  const { register, handleSubmit, formState: { errors } } = useForm<NameFormData>({
    resolver: zodResolver(inputSchema),
    defaultValues: { 
      first_name: initialData.first_name,
      last_name: initialData.last_name
    },
  })

  const onSubmitForm: SubmitHandler<NameFormData> = (data) => {
    onSubmit(data)
  }

  return (
    <Underlay isOpen={isOpen}>
      <div className={styles.modal_content}>
        <h3>{title}</h3>
        {error && <Alert variant='error' message={getErrorMessage(error as AxiosError<ErrorResponse>)} />}
        <form onSubmit={handleSubmit(onSubmitForm)}>
          <div>
            <label htmlFor='first_name'>First Name:</label>
            <input
              id='first_name'
              type='text'
              {...register('first_name')}
            />
            {errors.first_name && <p>{errors.first_name.message}</p>}
          </div>
          <div>
            <label htmlFor='last_name'>Last Name:</label>
            <input
              id='last_name'
              type='text'
              {...register('last_name')}
            />
            {errors.last_name && <p>{errors.last_name.message}</p>}
          </div>
          <div className={styles.modal_buttons}>
            <button type="submit" className='empty_btn' disabled={isPending}>
              {isPending ? (
                'Updating...'
              ) : (
                isEditing ? 'Update' : 'Save'
              )}
            </button>
            <button type="button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </div>
    </Underlay>
  )
}

export default NameModal
