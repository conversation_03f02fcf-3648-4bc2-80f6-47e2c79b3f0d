@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.wishlist_btn {
  width: 40px;
  height: 40px;
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-2;
  background-color: #fff;
  @include flexbox(center, center);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    border-color: $primary-red;
    background-color: #fef7f7;
  }

  &.in_wishlist {
    color: $primary-red;
    border-color: $primary-red;
    background-color: #fef7f7;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  svg {
    font-size: $font-size-3;
  }
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid $primary-red;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}