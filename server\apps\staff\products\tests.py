import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from rest_framework.test import APIClient
from rest_framework import status
from model_bakery import baker

from .models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy, AttributeValueProxy,
    ProductVariantProxy, BrandProductTypeProxy, ProductAttributeValueProxy, ProductVariantAttributeValueProxy,
    ProductTypeAttributeProxy, ProductAudit, BulkProductOperation
)
from .services import ProductService, CategoryService, BrandProductTypeService, ProductTypeBrandService

User = get_user_model()


@pytest.mark.django_db
class TestProductStaffAPI:
    """Test product staff API endpoints"""
    
    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()
        
        # Create staff user with permissions
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')
        
        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)
        
        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products',
            codename__in=['view_product', 'add_product', 'change_product', 'delete_product']
        )
        self.product_manager_group.permissions.set(permissions)
        
        # Create test data
        self.category = baker.make(CategoryProxy, title='Test Category')
        self.brand = baker.make(BrandProxy, title='Test Brand')
        self.product_type = baker.make(ProductTypeProxy, title='Test Type')
        
        self.client.force_authenticate(user=self.staff_user)
    
    def test_product_list(self):
        """Test product listing"""
        # Create test products
        baker.make(ProductProxy, _quantity=3, category=self.category, brand=self.brand, product_type=self.product_type)
        
        response = self.client.get('/api/staff/products/')
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 3
    
    def test_product_create(self):
        """Test product creation"""
        product_data = {
            'title': 'Test Product',
            'slug': 'test-product',
            'brand': self.brand.id,
            'category': self.category.id,
            'product_type': self.product_type.id,
            'description': 'Test description',
            'is_active': True,
            'is_digital': False
        }
        
        response = self.client.post('/api/staff/products/', product_data)
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['title'] == 'Test Product'
        
        # Check audit log was created
        assert ProductAudit.objects.filter(
            staff_user=self.staff_user,
            action='CREATE'
        ).exists()
    
    def test_product_update(self):
        """Test product update"""
        product = baker.make(ProductProxy, category=self.category, brand=self.brand, product_type=self.product_type)
        
        update_data = {
            'title': 'Updated Product',
            'description': 'Updated description'
        }
        
        response = self.client.patch(f'/api/staff/products/{product.id}/', update_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == 'Updated Product'
        
        # Check audit log was created
        assert ProductAudit.objects.filter(
            product=product,
            staff_user=self.staff_user,
            action='UPDATE'
        ).exists()
    
    def test_bulk_operations(self):
        """Test bulk operations"""
        products = baker.make(ProductProxy, _quantity=3, category=self.category, brand=self.brand, product_type=self.product_type)
        product_ids = [p.id for p in products]
        
        bulk_data = {
            'operation_type': 'activate',
            'product_ids': product_ids
        }
        
        response = self.client.post('/api/staff/products/bulk_operations/', bulk_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['updated_count'] == 3
    
    def test_unauthorized_access(self):
        """Test unauthorized access"""
        self.client.force_authenticate(user=None)

        response = self.client.get('/api/staff/products/')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_create_product_with_variants_api(self):
        """Test creating product with variants via API endpoint"""
        # Create a price label attribute value for the variant
        price_label = baker.make(AttributeValueProxy)

        request_data = {
            'product': {
                'title': 'Western Digital 1TB 4TB 8TB WD Blue PC Internal Hard Drive',
                'slug': 'western-digital-1tb-4tb-8tb-wd-blue-pc-internal-hard-drive',
                'brand': self.brand.id,
                'category': self.category.id,
                'product_type': self.product_type.id,
                'description': 'This reliable 3.5" SATA HDD offers a massive 8TB capacity, perfect for storing games, media, and large files.',
                'is_active': True,
                'is_digital': False
            },
            'variants': [
                {
                    'price': '42.99',
                    'price_label': price_label.id,
                    'sku': 'MOUSE-REDS',
                    'stock_qty': 100,
                    'is_active': True,
                    'weight': 440,
                    'condition': 'New'
                }
            ]
        }

        response = self.client.post('/api/staff/products/create_with_variants/', request_data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['product']['title'] == 'Western Digital 1TB 4TB 8TB WD Blue PC Internal Hard Drive'
        assert len(response.data['variants']) == 1
        assert response.data['variants'][0]['sku'] == 'MOUSE-REDS'

        # Check that the product was created in the database
        product = ProductProxy.objects.get(slug='western-digital-1tb-4tb-8tb-wd-blue-pc-internal-hard-drive')
        assert product.title == 'Western Digital 1TB 4TB 8TB WD Blue PC Internal Hard Drive'
        assert product.product_variant.count() == 1

        # Check audit log was created
        assert ProductAudit.objects.filter(
            product=product,
            staff_user=self.staff_user,
            action='CREATE'
        ).exists()


@pytest.mark.django_db
class TestProductService:
    """Test product service methods"""
    
    def setup_method(self):
        """Setup test data"""
        self.staff_user = baker.make(User, is_staff=True)
        self.category = baker.make(CategoryProxy)
        self.brand = baker.make(BrandProxy)
        self.product_type = baker.make(ProductTypeProxy)
    
    def test_create_product_with_variants(self):
        """Test creating product with variants"""
        product_data = {
            'title': 'Test Product',
            'slug': 'test-product',
            'brand': self.brand,
            'category': self.category,
            'product_type': self.product_type,
            'is_active': True
        }
        
        variants_data = [
            {
                'price': 100.00,
                'sku': 'TEST-001',
                'stock_qty': 10,
                'condition': 'New'
            },
            {
                'price': 150.00,
                'sku': 'TEST-002',
                'stock_qty': 5,
                'condition': 'New'
            }
        ]
        
        product, variants = ProductService.create_product_with_variants(
            product_data=product_data,
            variants_data=variants_data,
            staff_user=self.staff_user
        )
        
        assert product.title == 'Test Product'
        assert len(variants) == 2
        assert variants[0].sku == 'TEST-001'
        assert variants[1].sku == 'TEST-002'
        
        # Check audit log
        assert ProductAudit.objects.filter(
            product=product,
            staff_user=self.staff_user,
            action='CREATE'
        ).exists()
    
    def test_bulk_update_products(self):
        """Test bulk product updates"""
        products = baker.make(ProductProxy, _quantity=3, category=self.category, brand=self.brand, product_type=self.product_type)
        
        products_data = [
            {'id': products[0].id, 'title': 'Updated Product 1'},
            {'id': products[1].id, 'title': 'Updated Product 2'},
            {'id': products[2].id, 'title': 'Updated Product 3'}
        ]
        
        result = ProductService.bulk_update_products(
            products_data=products_data,
            staff_user=self.staff_user
        )
        
        assert result['total_processed'] == 3
        assert result['total_failed'] == 0
        assert len(result['updated_products']) == 3
        
        # Check bulk operation record
        assert BulkProductOperation.objects.filter(
            staff_user=self.staff_user,
            operation_type='BULK_UPDATE'
        ).exists()


class TestCategoryService(TestCase):
    """Test category service methods"""
    
    def setUp(self):
        """Setup test data"""
        self.staff_user = baker.make(User, is_staff=True)
        self.parent_category = baker.make(CategoryProxy, title='Parent')
        self.child_category = baker.make(CategoryProxy, title='Child', parent=self.parent_category)
    
    def test_move_category(self):
        """Test moving category"""
        new_parent = baker.make(CategoryProxy, title='New Parent')
        
        moved_category = CategoryService.move_category(
            category_id=self.child_category.id,
            new_parent_id=new_parent.id,
            staff_user=self.staff_user
        )
        
        moved_category.refresh_from_db()
        assert moved_category.parent == new_parent
    
    def test_move_category_to_root(self):
        """Test moving category to root level"""
        moved_category = CategoryService.move_category(
            category_id=self.child_category.id,
            new_parent_id=None,
            staff_user=self.staff_user
        )
        
        moved_category.refresh_from_db()
        assert moved_category.parent is None


@pytest.mark.django_db
class TestBrandProductTypeAPI:
    """Test brand-product type association API endpoints"""

    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')

        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)

        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products',
            codename__in=['view_brand', 'add_brand', 'change_brand']
        )
        self.product_manager_group.permissions.set(permissions)

        # Create test data
        self.brand = baker.make(BrandProxy, title='Test Brand')
        self.brand2 = baker.make(BrandProxy, title='Test Brand 2')
        self.product_type1 = baker.make(ProductTypeProxy, title='Type 1')
        self.product_type2 = baker.make(ProductTypeProxy, title='Type 2')

        self.client.force_authenticate(user=self.staff_user)

    def test_brand_product_type_list(self):
        """Test brand-product type association listing"""
        # Create test associations
        baker.make(BrandProductTypeProxy, brand=self.brand, product_type=self.product_type1)
        baker.make(BrandProductTypeProxy, brand=self.brand, product_type=self.product_type2)

        response = self.client.get('/api/staff/brand-product-types/')
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2

    def test_bulk_brand_product_type_association(self):
        """Test bulk brand-product type association"""
        association_data = {
            'brand_id': self.brand.id,
            'product_type_ids': [self.product_type1.id, self.product_type2.id]
        }

        response = self.client.post('/api/staff/brand-product-types/bulk_associate/', association_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['created_count'] == 2

        # Check associations were created
        assert BrandProductTypeProxy.objects.filter(brand=self.brand).count() == 2

    def test_bulk_product_type_brand_association(self):
        """Test bulk product type-brand association"""
        association_data = {
            'brand_ids': [self.brand.id, self.brand2.id]
        }

        response = self.client.post(f'/api/staff/products/product-types/{self.product_type1.id}/associate_brands/', association_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['created_count'] == 2

        # Check associations were created
        assert BrandProductTypeProxy.objects.filter(product_type=self.product_type1).count() == 2

    def test_get_product_type_brands(self):
        """Test getting brands associated with a product type"""
        # Create some associations first
        BrandProductTypeProxy.objects.create(brand=self.brand, product_type=self.product_type1)
        BrandProductTypeProxy.objects.create(brand=self.brand2, product_type=self.product_type1)

        response = self.client.get(f'/api/staff/products/product-types/{self.product_type1.id}/brands/')
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2

    def test_remove_product_type_brands(self):
        """Test removing brand associations from a product type"""
        # Create some associations first
        BrandProductTypeProxy.objects.create(brand=self.brand, product_type=self.product_type1)
        BrandProductTypeProxy.objects.create(brand=self.brand2, product_type=self.product_type1)

        removal_data = {
            'brand_ids': [self.brand.id]
        }

        response = self.client.post(f'/api/staff/products/product-types/{self.product_type1.id}/remove_brands/', removal_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['removed_count'] == 1

        # Check association was removed
        assert BrandProductTypeProxy.objects.filter(product_type=self.product_type1).count() == 1


@pytest.mark.django_db
class TestProductAttributeValueAPI:
    """Test product attribute value association API endpoints"""

    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')

        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)

        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products'
        )
        self.product_manager_group.permissions.set(permissions)

        # Create test data
        self.category = baker.make(CategoryProxy, title='Test Category')
        self.brand = baker.make(BrandProxy, title='Test Brand')
        self.product_type = baker.make(ProductTypeProxy, title='Test Type')
        self.product = baker.make(ProductProxy, category=self.category, brand=self.brand, product_type=self.product_type)
        self.attribute = baker.make(AttributeProxy, title='Color')
        self.attr_value1 = baker.make(AttributeValueProxy, attribute=self.attribute, attribute_value='Red')
        self.attr_value2 = baker.make(AttributeValueProxy, attribute=self.attribute, attribute_value='Blue')

        self.client.force_authenticate(user=self.staff_user)

    def test_product_attribute_value_list(self):
        """Test product attribute value association listing"""
        # Create test associations
        baker.make(ProductAttributeValueProxy, product=self.product, attribute_value=self.attr_value1)
        baker.make(ProductAttributeValueProxy, product=self.product, attribute_value=self.attr_value2)

        response = self.client.get('/api/staff/product-attribute-values/')
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2

    def test_bulk_product_attribute_value_association(self):
        """Test bulk product attribute value association"""
        association_data = {
            'product_id': self.product.id,
            'attribute_value_ids': [self.attr_value1.id, self.attr_value2.id]
        }

        response = self.client.post('/api/staff/product-attribute-values/bulk_associate/', association_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['created_count'] == 2

        # Check associations were created
        assert ProductAttributeValueProxy.objects.filter(product=self.product).count() == 2


class TestBrandProductTypeService(TestCase):
    """Test brand-product type service methods"""

    def setUp(self):
        """Setup test data"""
        self.staff_user = baker.make(User, is_staff=True)
        self.brand = baker.make(BrandProxy, title='Test Brand')
        self.brand2 = baker.make(BrandProxy, title='Test Brand 2')
        self.product_type1 = baker.make(ProductTypeProxy, title='Type 1')
        self.product_type2 = baker.make(ProductTypeProxy, title='Type 2')

    def test_associate_product_types_bulk(self):
        """Test bulk product type association"""
        result = BrandProductTypeService.associate_product_types_bulk(
            brand_id=self.brand.id,
            product_type_ids=[self.product_type1.id, self.product_type2.id],
            staff_user=self.staff_user
        )

        assert result['total_created'] == 2
        assert result['total_existing'] == 0
        assert BrandProductTypeProxy.objects.filter(brand=self.brand).count() == 2

    def test_associate_brands_bulk(self):
        """Test bulk brand association with product type"""
        result = ProductTypeBrandService.associate_brands_bulk(
            product_type_id=self.product_type1.id,
            brand_ids=[self.brand.id, self.brand2.id],
            staff_user=self.staff_user
        )

        assert result['total_created'] == 2
        assert result['total_existing'] == 0
        assert BrandProductTypeProxy.objects.filter(product_type=self.product_type1).count() == 2

    def test_remove_brands_bulk(self):
        """Test bulk brand removal from product type"""
        # Create associations first
        BrandProductTypeProxy.objects.create(brand=self.brand, product_type=self.product_type1)
        BrandProductTypeProxy.objects.create(brand=self.brand2, product_type=self.product_type1)

        result = ProductTypeBrandService.remove_brands_bulk(
            product_type_id=self.product_type1.id,
            brand_ids=[self.brand.id],
            staff_user=self.staff_user
        )

        assert result['removed_count'] == 1
        assert BrandProductTypeProxy.objects.filter(product_type=self.product_type1).count() == 1

    def test_remove_product_types_bulk(self):
        """Test bulk product type removal"""
        # Create associations first
        baker.make(BrandProductTypeProxy, brand=self.brand, product_type=self.product_type1)
        baker.make(BrandProductTypeProxy, brand=self.brand, product_type=self.product_type2)

        result = BrandProductTypeService.remove_product_types_bulk(
            brand_id=self.brand.id,
            product_type_ids=[self.product_type1.id, self.product_type2.id],
            staff_user=self.staff_user
        )

        assert result['removed_count'] == 2
        assert BrandProductTypeProxy.objects.filter(brand=self.brand).count() == 0


@pytest.mark.django_db
class TestProductVariantAttributeValuesAPI:
    """Test product variant attribute values API endpoint"""

    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()

        # Create staff user with permissions
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')

        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)

        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products',
            codename__in=['view_product', 'view_productvariant', 'view_attribute', 'view_attributevalue']
        )
        self.product_manager_group.permissions.set(permissions)

        # Create test data
        self.category = baker.make(CategoryProxy, title='Test Category')
        self.brand = baker.make(BrandProxy, title='Test Brand')
        self.product_type = baker.make(ProductTypeProxy, title='Test Type')

        # Create product and variant
        self.product = baker.make(
            ProductProxy,
            title='Test Product',
            category=self.category,
            brand=self.brand,
            product_type=self.product_type
        )
        self.variant = baker.make(
            ProductVariantProxy,
            product=self.product,
            sku='TEST-SKU-001',
            price=99.99,
            stock_qty=10
        )

        # Create attributes and attribute values
        self.color_attribute = baker.make(AttributeProxy, title='Color')
        self.size_attribute = baker.make(AttributeProxy, title='Size')

        self.red_value = baker.make(AttributeValueProxy, attribute=self.color_attribute, attribute_value='Red')
        self.large_value = baker.make(AttributeValueProxy, attribute=self.size_attribute, attribute_value='Large')

        # Create associations
        self.color_association = baker.make(
            ProductVariantAttributeValueProxy,
            product_variant=self.variant,
            attribute_value=self.red_value,
            is_active=True,
            order=1
        )
        self.size_association = baker.make(
            ProductVariantAttributeValueProxy,
            product_variant=self.variant,
            attribute_value=self.large_value,
            is_active=True,
            order=2
        )

        self.client.force_authenticate(user=self.staff_user)

    def test_get_variant_attribute_values(self):
        """Test getting all attribute values for a product variant"""
        url = f'/staff/products/variants/{self.variant.id}/attribute_values/'
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Check response structure
        assert 'variant_sku' in data
        assert 'variant_id' in data
        assert 'product_title' in data
        assert 'attribute_values_count' in data
        assert 'attribute_values' in data

        # Check data values
        assert data['variant_sku'] == 'TEST-SKU-001'
        assert data['variant_id'] == self.variant.id
        assert data['product_title'] == 'Test Product'
        assert data['attribute_values_count'] == 2
        assert len(data['attribute_values']) == 2

        # Check attribute values structure
        attribute_values = data['attribute_values']
        assert all('id' in av for av in attribute_values)
        assert all('attribute_value' in av for av in attribute_values)
        assert all('attribute' in av for av in attribute_values)
        assert all('is_active' in av for av in attribute_values)
        assert all('order' in av for av in attribute_values)

    def test_get_variant_attribute_values_with_filters(self):
        """Test getting attribute values with filters"""
        # Test is_active filter
        url = f'/staff/products/variants/{self.variant.id}/attribute_values/?is_active=true'
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['attribute_values_count'] == 2

        # Test attribute filter
        url = f'/staff/products/variants/{self.variant.id}/attribute_values/?attribute={self.color_attribute.id}'
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['attribute_values_count'] == 1
        assert data['attribute_values'][0]['attribute']['title'] == 'Color'


@pytest.mark.django_db
class TestAssociationManagementAPI:
    """Test association management API endpoints"""

    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')

        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)

        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products'
        )
        self.product_manager_group.permissions.set(permissions)

        # Create test data
        self.product_type = baker.make(ProductTypeProxy, title='Electronics')
        self.attribute = baker.make(AttributeProxy, title='Color')

        # Create association
        self.association = baker.make(
            ProductTypeAttributeProxy,
            product_type=self.product_type,
            attribute=self.attribute,
            is_filterable=False,
            is_option_selector=False
        )

        self.client.force_authenticate(user=self.staff_user)

    def test_save_association(self):
        """Test creating new association"""
        new_attribute = baker.make(AttributeProxy, title='Size')

        association_data = {
            'product_type': self.product_type.id,
            'attribute': new_attribute.id,
            'is_filterable': True,
            'is_option_selector': False
        }

        response = self.client.post('/api/staff/products/associations/save_association/', association_data)
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['is_filterable'] is True
        assert response.data['is_option_selector'] is False

        # Check association was created
        assert ProductTypeAttributeProxy.objects.filter(
            product_type=self.product_type,
            attribute=new_attribute
        ).exists()

    def test_retrieve_association(self):
        """Test retrieving specific association using standard REST endpoint"""
        response = self.client.get(f'/api/staff/products/associations/{self.association.id}/')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == self.association.id
        assert response.data['product_type'] == self.product_type.id
        assert response.data['attribute'] == self.attribute.id

    def test_update_association(self):
        """Test updating existing association using standard REST endpoint"""
        update_data = {
            'is_filterable': True,
            'is_option_selector': True
        }

        response = self.client.patch(f'/api/staff/products/associations/{self.association.id}/', update_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_filterable'] is True
        assert response.data['is_option_selector'] is True

        # Check association was updated
        self.association.refresh_from_db()
        assert self.association.is_filterable is True
        assert self.association.is_option_selector is True

    def test_update_association_partial(self):
        """Test partial update of association"""
        update_data = {
            'is_filterable': True
            # is_option_selector not provided - should remain unchanged
        }

        response = self.client.patch(f'/api/staff/products/associations/{self.association.id}/', update_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_filterable'] is True
        assert response.data['is_option_selector'] is False  # Should remain unchanged

        # Check association was updated
        self.association.refresh_from_db()
        assert self.association.is_filterable is True
        assert self.association.is_option_selector is False

    def test_update_association_not_found(self):
        """Test update with non-existent association_id"""
        update_data = {
            'is_filterable': True
        }

        response = self.client.patch('/api/staff/products/associations/99999/', update_data)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_association(self):
        """Test deleting association using standard REST endpoint"""
        response = self.client.delete(f'/api/staff/products/associations/{self.association.id}/')
        assert response.status_code == status.HTTP_204_NO_CONTENT

        # Check association was deleted
        assert not ProductTypeAttributeProxy.objects.filter(id=self.association.id).exists()

    def test_delete_association_not_found(self):
        """Test deleting non-existent association"""
        response = self.client.delete('/api/staff/products/associations/99999/')
        assert response.status_code == status.HTTP_404_NOT_FOUND

    # Test backward compatibility with old endpoints
    def test_update_association_legacy(self):
        """Test updating association using legacy endpoint (deprecated)"""
        update_data = {
            'association_id': self.association.id,
            'is_filterable': True,
            'is_option_selector': True
        }

        response = self.client.patch('/api/staff/products/associations/update_association/', update_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_filterable'] is True
        assert response.data['is_option_selector'] is True

    def test_delete_association_legacy(self):
        """Test deleting association using legacy endpoint (deprecated)"""
        delete_data = {
            'association_id': self.association.id
        }

        response = self.client.delete('/api/staff/products/associations/delete_association/', delete_data)
        assert response.status_code == status.HTTP_204_NO_CONTENT

        # Check association was deleted
        assert not ProductTypeAttributeProxy.objects.filter(id=self.association.id).exists()


@pytest.mark.django_db
class TestProductTypeAttributeValuesAPI:
    """Test product type attribute values API endpoint"""

    def setup_method(self):
        """Setup test data"""
        self.client = APIClient()

        # Create staff user with permissions
        self.staff_user = baker.make(User, is_staff=True, email='<EMAIL>')

        # Create Product Manager group with permissions
        self.product_manager_group = Group.objects.create(name='Product Manager')
        self.staff_user.groups.add(self.product_manager_group)

        # Add basic permissions
        permissions = Permission.objects.filter(
            content_type__app_label='products',
            codename__in=['view_producttype', 'view_attribute', 'view_attributevalue']
        )
        self.product_manager_group.permissions.set(permissions)

        # Create test data
        self.product_type = baker.make(ProductTypeProxy, title='Electronics')
        self.attribute1 = baker.make(AttributeProxy, title='Color')
        self.attribute2 = baker.make(AttributeProxy, title='Size')

        # Create attribute values
        self.attr_value1 = baker.make(
            AttributeValueProxy,
            attribute=self.attribute1,
            attribute_value='Red',
            is_active=True,
            for_filtering=True
        )
        self.attr_value2 = baker.make(
            AttributeValueProxy,
            attribute=self.attribute1,
            attribute_value='Blue',
            is_active=True,
            for_filtering=False
        )
        self.attr_value3 = baker.make(
            AttributeValueProxy,
            attribute=self.attribute2,
            attribute_value='Large',
            is_active=True,
            for_filtering=True
        )

        # Associate attributes with product type
        from .models import ProductTypeAttributeProxy
        baker.make(ProductTypeAttributeProxy, product_type=self.product_type, attribute=self.attribute1)
        baker.make(ProductTypeAttributeProxy, product_type=self.product_type, attribute=self.attribute2)

        self.client.force_authenticate(user=self.staff_user)

    def test_get_attribute_values_for_product_type(self):
        """Test getting attribute values for a product type"""
        response = self.client.get(f'/api/staff/products/product-types/{self.product_type.id}/attribute_values/')

        assert response.status_code == status.HTTP_200_OK

        # Check response structure
        assert 'product_type_id' in response.data
        assert 'product_type_title' in response.data
        assert 'attributes' in response.data
        assert 'total_attributes' in response.data
        assert 'total_values' in response.data

        # Check data values
        assert response.data['product_type_id'] == self.product_type.id
        assert response.data['product_type_title'] == 'Electronics'
        assert response.data['total_attributes'] == 2
        assert response.data['total_values'] == 3

        # Check attributes structure
        attributes = response.data['attributes']
        assert len(attributes) == 2

        # Find Color attribute
        color_attr = next((attr for attr in attributes if attr['attribute_title'] == 'Color'), None)
        assert color_attr is not None
        assert color_attr['attribute_id'] == self.attribute1.id
        assert len(color_attr['values']) == 2

        # Find Size attribute
        size_attr = next((attr for attr in attributes if attr['attribute_title'] == 'Size'), None)
        assert size_attr is not None
        assert size_attr['attribute_id'] == self.attribute2.id
        assert len(size_attr['values']) == 1

        # Check value structure
        red_value = next((val for val in color_attr['values'] if val['attribute_value'] == 'Red'), None)
        assert red_value is not None
        assert red_value['is_active'] is True
        assert red_value['for_filtering'] is True

    def test_get_attribute_values_nonexistent_product_type(self):
        """Test getting attribute values for non-existent product type"""
        response = self.client.get('/api/staff/products/product-types/999/attribute_values/')

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_attribute_values_no_attributes(self):
        """Test getting attribute values for product type with no attributes"""
        empty_product_type = baker.make(ProductTypeProxy, title='Empty Type')

        response = self.client.get(f'/api/staff/products/product-types/{empty_product_type.id}/attribute_values/')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['total_attributes'] == 0
        assert response.data['total_values'] == 0
        assert len(response.data['attributes']) == 0
