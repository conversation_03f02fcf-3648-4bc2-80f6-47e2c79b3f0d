import { ProductShape } from '../types/product-types'
import { safeFetch } from './error-handlers'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api'

export class ProductService {
  private static async fetchWithErrorHandling<T>(url: string): Promise<T> {
    try {
      const response = await safeFetch(url, {
        next: { revalidate: 3600 }, // Revalidate every hour
      })

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Product not found')
        }
        throw new Error(`Failed to fetch data: ${response.status}`)
      }

      // Add error handling for potential streaming issues
      try {
        const data = await response.json()
        return data
      } catch (jsonError) {
        console.error('JSON parsing error (potential stream issue):', jsonError)
        throw new Error('Failed to parse response data')
      }
    } catch (error) {
      // Log potential stream controller errors
      if (error instanceof Error && error.message.includes('transformAlgorithm')) {
        console.error('🔴 Stream controller error in ProductService:', error)
      }

      if (error instanceof Error) {
        throw error
      }
      throw new Error('An unexpected error occurred')
    }
  }

  static async getProductBySlug(slug: string): Promise<ProductShape> {
    const url = `${API_BASE_URL}/products/${slug}/`
    return this.fetchWithErrorHandling<ProductShape>(url)
  }
}