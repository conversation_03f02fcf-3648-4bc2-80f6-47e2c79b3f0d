@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.underlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  /* Set the width to 100% of the viewport width */
  height: 100vh;
  /* Set the height to 100% of the viewport height */
  z-index: 20;
  /* Set a z-index value if needed */
  @include flexbox(center, center);
  /* Replaced direct flexbox properties with the flexbox mixin */
}