/**
 * TanStack Query Client Configuration for E-Commerce Application
 * 
 * This configuration is optimized for a customer-facing e-commerce application
 * with appropriate cache settings to balance performance and data freshness.
 * 
 * Key Configuration Principles:
 * - Reduce unnecessary API calls to improve performance and user experience
 * - Set appropriate staleTime based on how frequently data changes
 * - Use gcTime (garbage collection time) to keep data in cache for reasonable periods
 * - Implement smart retry logic for failed requests
 * - Disable refetchOnWindowFocus for better UX in e-commerce scenarios
 * 
 * Based on TanStack Query v5 best practices:
 * - https://tanstack.com/query/v5/docs/react/guides/important-defaults
 */

import { QueryClient } from '@tanstack/react-query'

/**
 * Create and configure the QueryClient with e-commerce optimized defaults
 * 
 * Global Configuration Rationale:
 * - staleTime: 5 minutes - Good balance for most e-commerce data
 * - gcTime: 10 minutes - Keeps data in cache longer than staleTime for better performance
 * - refetchOnWindowFocus: false - Prevents unnecessary refetches when user switches tabs
 * - retry: Smart retry logic that avoids retrying client errors (4xx) except timeouts/rate limits
 * - retryDelay: Exponential backoff to avoid overwhelming the server
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Data is considered fresh for 5 minutes by default
      // This prevents unnecessary refetches while ensuring data isn't too stale
      staleTime: 5 * 60 * 1000,
      
      // Keep cached data for 10 minutes after it becomes inactive
      // This allows for instant navigation back to previously viewed pages
      gcTime: 10 * 60 * 1000,
      
      // Smart retry configuration for better error handling
      retry: (failureCount, error: any) => {
        // Don't retry on client errors (4xx) except for specific cases
        // 408: Request Timeout - might be temporary network issue
        // 429: Too Many Requests - should retry with backoff
        if (
          error?.status >= 400 &&
          error?.status < 500 &&
          ![408, 429].includes(error.status)
        ) {
          return false // Client errors shouldn't be retried
        }
        // Retry up to 3 times for server errors and network issues
        return failureCount < 3
      },
      
      // Exponential backoff for retries to avoid overwhelming the server
      // Starts at 1s, then 2s, 4s, 8s, capped at 30s
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Disabled for better UX in e-commerce
      // Prevents unnecessary refetches when user switches browser tabs/windows
      refetchOnWindowFocus: false,
      
      // Always refetch when network connection is restored
      // This ensures data is fresh after coming back offline
      refetchOnReconnect: 'always',
      
      // Only execute queries when online
      // Prevents failed requests when user is offline
      networkMode: 'online',
    },
    
    mutations: {
      // Don't retry mutations by default as they can have side effects
      // Mutations should be handled explicitly by the caller
      retry: false,
      
      // Only execute mutations when online
      networkMode: 'online',
    },
  },
})

/**
 * Entity-Specific Query Defaults
 * 
 * These configurations override the global defaults for specific entity types
 * based on how frequently their data changes and their importance to the UX.
 */

// Products change infrequently - longer cache times for better performance
queryClient.setQueryDefaults(['products'], {
  staleTime: 10 * 60 * 1000, // 10 minutes - products don't change often
  gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache for quick navigation
})

// Categories are very stable - longest cache times
queryClient.setQueryDefaults(['categories'], {
  staleTime: 30 * 60 * 1000, // 30 minutes - categories rarely change
  gcTime: 60 * 60 * 1000, // 1 hour - keep for entire session
})

// Brands are very stable - similar to categories
queryClient.setQueryDefaults(['brands'], {
  staleTime: 30 * 60 * 1000, // 30 minutes - brands rarely change
  gcTime: 60 * 60 * 1000, // 1 hour - keep for entire session
})

// Cart data should always be fresh - critical for checkout process
queryClient.setQueryDefaults(['cart'], {
  staleTime: 0, // Always fetch fresh cart data
  gcTime: 5 * 60 * 1000, // 5 minutes - reasonable cache duration
})

// Cart items - need to be fresh but can be cached briefly
queryClient.setQueryDefaults(['cart_items'], {
  staleTime: 0, // Always fetch fresh cart items
  gcTime: 5 * 60 * 1000, // 5 minutes - reasonable cache duration
})

// Simple cart - optimized for quick cart previews
queryClient.setQueryDefaults(['simple_cart'], {
  staleTime: 1 * 60 * 1000, // 1 minute - balance between freshness and performance
  gcTime: 5 * 60 * 1000, // 5 minutes - keep for quick navigation
})

// Orders are dynamic but don't change as frequently as cart
queryClient.setQueryDefaults(['orders'], {
  staleTime: 2 * 60 * 1000, // 2 minutes - orders can change but not constantly
  gcTime: 10 * 60 * 1000, // 10 minutes - keep for order history navigation
})

// Order items - similar to orders but may need more freshness
queryClient.setQueryDefaults(['order_items'], {
  staleTime: 2 * 60 * 1000, // 2 minutes - order items can change
  gcTime: 10 * 60 * 1000, // 10 minutes - keep for order details navigation
})

// Admin orders - may need more frequent updates for admin users
queryClient.setQueryDefaults(['orders_admin'], {
  staleTime: 1 * 60 * 1000, // 1 minute - admin needs fresher data
  gcTime: 10 * 60 * 1000, // 10 minutes - keep for admin panel navigation
})

// User/customer data changes moderately - balance freshness and performance
queryClient.setQueryDefaults(['customer_details'], {
  staleTime: 5 * 60 * 1000, // 5 minutes - user data doesn't change constantly
  gcTime: 15 * 60 * 1000, // 15 minutes - keep for session duration
})

// Customer addresses - change infrequently
queryClient.setQueryDefaults(['customer_addresses'], {
  staleTime: 15 * 60 * 1000, // 15 minutes - addresses rarely change
  gcTime: 30 * 60 * 1000, // 30 minutes - keep for session duration
})

// Reviews don't change often - can be cached longer
queryClient.setQueryDefaults(['reviews'], {
  staleTime: 15 * 60 * 1000, // 15 minutes - reviews are added infrequently
  gcTime: 30 * 60 * 1000, // 30 minutes - keep for product page navigation
})

// Wishlist can change frequently but not as critical as cart
queryClient.setQueryDefaults(['wishlist_items'], {
  staleTime: 2 * 60 * 1000, // 2 minutes - wishlist can change but not constantly
  gcTime: 10 * 60 * 1000, // 10 minutes - keep for wishlist navigation
})

/**
 * Authentication Mutation Defaults
 * 
 * Global error handling for authentication mutations
 * This provides consistent error handling across all auth-related mutations
 */
queryClient.setMutationDefaults(['auth', 'login'], {
  mutationFn: async (variables: any) => {
    // This will be overridden by actual mutation functions
    throw new Error('Mutation function not implemented')
  },
  onError: (error: any) => {
    console.error('Authentication error:', error)
  },
})

/**
 * Query Utilities for Cache Management
 * 
 * These utilities provide a consistent interface for common cache operations
 * throughout the application. They help maintain cache consistency and
 * provide a clean API for interacting with the query cache.
 */
export const queryUtils = {
  /**
   * Invalidate all queries for a specific entity
   * This triggers a refetch of all queries matching the entity key
   * @param entity - The entity type to invalidate (e.g., 'products', 'cart')
   */
  invalidateEntity: (entity: string) => {
    return queryClient.invalidateQueries({ queryKey: [entity] })
  },

  /**
   * Remove all queries for a specific entity from cache
   * This completely removes the data, forcing a fresh fetch on next request
   * @param entity - The entity type to remove (e.g., 'products', 'cart')
   */
  removeEntity: (entity: string) => {
    return queryClient.removeQueries({ queryKey: [entity] })
  },

  /**
   * Get cached data for a specific query
   * Returns the cached data if it exists, otherwise returns undefined
   * @param queryKey - The query key to retrieve data for
   * @returns The cached data or undefined
   */
  getQueryData: <T>(queryKey: any[]) => {
    return queryClient.getQueryData<T>(queryKey)
  },

  /**
   * Set cached data for a specific query
   * Updates the cache with new data, useful for optimistic updates
   * @param queryKey - The query key to update
   * @param data - The new data to set in cache
   */
  setQueryData: <T>(queryKey: any[], data: T) => {
    return queryClient.setQueryData<T>(queryKey, data)
  },

  /**
   * Prefetch a query to populate the cache
   * Useful for preloading data that will be needed soon
   * @param queryKey - The query key to prefetch
   * @param queryFn - The function to fetch the data
   */
  prefetchQuery: (queryKey: any[], queryFn: () => Promise<any>) => {
    return queryClient.prefetchQuery({
      queryKey,
      queryFn,
    })
  },

  /**
   * Clear all cached data
   * This removes all queries from the cache, forcing fresh fetches
   * Useful for logging out or clearing user-specific data
   */
  clear: () => {
    return queryClient.clear()
  },
}

/**
 * Cache Utilities for Specific E-Commerce Operations
 * 
 * These utilities provide specialized cache management functions
 * for common e-commerce operations like updating products, cart,
 * wishlist, and user data. They ensure cache consistency across
 * different parts of the application.
 */
export const cacheUtils = {
  /**
   * Update product data in cache after mutations
   * Updates both the specific product detail and invalidates product lists
   * @param productId - The ID of the product to update
   * @param updater - Function to update the product data
   */
  updateProduct: (productId: number, updater: (old: any) => any) => {
    queryClient.setQueryData(['products', 'detail', productId], updater)
    // Also invalidate product lists to ensure consistency
    queryClient.invalidateQueries({ queryKey: ['products'] })
  },

  /**
   * Update cart data in cache
   * Ensures cart data is consistent across the application
   * @param updater - Function to update the cart data
   */
  updateCart: (updater: (old: any) => any) => {
    queryClient.setQueryData(['cart'], updater)
  },

  /**
   * Update simple cart data in cache
   * Used for quick cart previews and header cart displays
   * @param updater - Function to update the simple cart data
   */
  updateSimpleCart: (updater: (old: any) => any) => {
    queryClient.setQueryData(['simple_cart'], updater)
  },

  /**
   * Add item to wishlist cache
   * Optimistically updates the wishlist cache
   * @param item - The item to add to the wishlist
   */
  addToWishlist: (item: any) => {
    queryClient.setQueryData(['wishlist_items'], (old: any) => {
      if (!old) return [item]
      // Avoid duplicates
      const exists = old.some((existingItem: any) => existingItem.id === item.id)
      if (exists) return old
      return [...old, item]
    })
  },

  /**
   * Remove item from wishlist cache
   * Optimistically updates the wishlist cache
   * @param itemId - The ID of the item to remove from wishlist
   */
  removeFromWishlist: (itemId: number) => {
    queryClient.setQueryData(['wishlist_items'], (old: any[]) => {
      if (!old) return []
      return old.filter((item) => item.id !== itemId)
    })
  },

  /**
   * Update customer data in cache
   * Ensures customer data is consistent across the application
   * @param updater - Function to update the customer data
   */
  updateCustomer: (updater: (old: any) => any) => {
    queryClient.setQueryData(['customer_details'], updater)
  },

  /**
   * Update customer addresses in cache
   * Ensures address data is consistent across the application
   * @param updater - Function to update the customer addresses
   */
  updateCustomerAddresses: (updater: (old: any) => any) => {
    queryClient.setQueryData(['customer_addresses'], updater)
  },

  /**
   * Update order data in cache
   * Ensures order data is consistent across the application
   * @param updater - Function to update the order data
   */
  updateOrder: (updater: (old: any) => any) => {
    queryClient.setQueryData(['orders'], updater)
  },

  /**
   * Update order items in cache
   * Ensures order item data is consistent across the application
   * @param updater - Function to update the order items
   */
  updateOrderItems: (updater: (old: any) => any) => {
    queryClient.setQueryData(['order_items'], updater)
  },
}
