import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'

/**
 * Hook for fetching variant attribute values by variant
 */
export const useVariantAttributeValues = (variantId: number) => {
  return useQuery({
    queryKey: ['variant-attribute-values', variantId],
    queryFn: () => ProductService.getVariantAttributeValuesByVariant(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for creating variant attribute value association
 */
export const useCreateVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { product_variant: number; attribute_value: number; is_active?: boolean }) =>
      ProductService.createVariantAttributeValue(data),
    onSuccess: () => {
      showSuccess('Attribute Association Created', 'Attribute value has been associated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute association.')
    },
  })
}

/**
 * Hook for deleting variant attribute value association
 */
export const useDeleteVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteVariantAttributeValue(id),
    onSuccess: () => {
      showSuccess('Attribute Association Deleted', 'Attribute value association has been removed successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete attribute association.')
    },
  })
}

/**
 * Hook for updating variant attribute value association
 */
export const useUpdateVariantAttributeValueDetails = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { is_active?: boolean } }) =>
      ProductService.updateVariantAttributeValue(id, data),
    onSuccess: () => {
      showSuccess('Attribute Association Updated', 'Attribute value association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute association.')
    },
  })
}

/**
 * Hook for individual variant attribute value reorder
 */
export const useReorderVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ associationId, newOrder }: { associationId: number; newOrder: number }) =>
      ProductService.reorderVariantAttributeValue(associationId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute value has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute value.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering variant attribute values
 */
export const useReorderVariantAttributeValuesDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productVariantId, orderedIds }: { productVariantId: number; orderedIds: number[] }) =>
      ProductService.reorderVariantAttributeValuesDragDrop(productVariantId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute values have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute values.')
    },
  })
}

/**
 * Hook for bulk updating variant attribute values order
 */
export const useBulkUpdateVariantAttributeValuesOrder = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (orderUpdates: { id: number; order: number }[]) =>
      ProductService.bulkUpdateVariantAttributeValuesOrder(orderUpdates),
    onSuccess: () => {
      showSuccess('Updated', 'Attribute values order has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute values order.')
    },
  })
}

/**
 * Hook for bulk associating attribute values with a variant
 */
export const useBulkAssociateVariantAttributeValues = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { product_variant_id: number; attribute_value_ids: number[] }) =>
      ProductService.bulkAssociateVariantAttributeValues(data),
    onSuccess: () => {
      showSuccess('Attributes Associated', 'Attribute values have been associated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attribute values.')
    },
  })
}

/**
 * Hook for bulk updating status of variant attribute value associations
 */
export const useBulkUpdateVariantAttributeValueStatus = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { association_ids: number[]; is_active: boolean }) =>
      ProductService.bulkUpdateVariantAttributeValueStatus(data),
    onSuccess: () => {
      showSuccess('Status Updated', 'Attribute value statuses have been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute value statuses.')
    },
  })
}

/**
 * Hook for fetching attribute values by product type
 */
export const useAttributeValuesByProductType = (productTypeId: number) => {
  return useQuery({
    queryKey: ['product-type-attribute-values', productTypeId],
    queryFn: () => ProductService.getAttributeValuesByProductType(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}