// Main layout component that wraps all pages
// Includes header, footer, and main content area

// import { Header } from './Header'
// import { Footer } from './Footer'
import styles from './MainLayout.module.scss'

interface MainLayoutProps {
  children: React.ReactNode
  className?: string
}

export function MainLayout({ children, className = '' }: MainLayoutProps) {
  const layoutClasses = [styles.layout, className]
    .filter(Boolean)
    .join(' ')

  return (
    <div className={layoutClasses}>
      {/* <Header /> */}
      <main className={styles.main}>
        {children}
      </main>
      {/* <Footer /> */}
    </div>
  )
}
