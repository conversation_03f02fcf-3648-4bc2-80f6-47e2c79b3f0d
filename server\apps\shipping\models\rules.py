from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from .boxes import Box


class PackingRule(models.Model):
    """Configurable packing rules for automated box selection"""
    
    title = models.CharField(max_length=100, help_text="Rule title/name")
    description = models.TextField(
        blank=True,
        help_text="Description of what this rule does"
    )
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Rule priority (higher = applied first)"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Is this rule active?"
    )
    
    # Condition fields - all are optional (null = no constraint)
    min_weight = models.DecimalField(
        max_digits=8, decimal_places=2,
        null=True, blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Minimum total weight in grams"
    )
    max_weight = models.DecimalField(
        max_digits=8, decimal_places=2,
        null=True, blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Maximum total weight in grams"
    )
    min_volume = models.DecimalField(
        max_digits=12, decimal_places=4,
        null=True, blank=True,
        validators=[MinValueValidator(Decimal('0.0001'))],
        help_text="Minimum total volume in cubic centimeters"
    )
    max_volume = models.DecimalField(
        max_digits=12, decimal_places=4,
        null=True, blank=True,
        validators=[MinValueValidator(Decimal('0.0001'))],
        help_text="Maximum total volume in cubic centimeters"
    )
    min_items = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Minimum number of items"
    )
    max_items = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Maximum number of items"
    )
    product_types = models.ManyToManyField(
        'products.ProductType',
        blank=True,
        help_text="Apply rule only to these product types"
    )
    
    # Action fields
    preferred_box = models.ForeignKey(
        Box, on_delete=models.CASCADE,
        null=True, blank=True,
        help_text="Preferred box for items matching this rule"
    )
    force_mailer = models.BooleanField(
        default=False,
        help_text="Force use of mailer for matching items"
    )
    force_separate_packaging = models.BooleanField(
        default=False,
        help_text="Force items to be packaged separately"
    )
    additional_cost = models.DecimalField(
        max_digits=6, decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Additional packing cost in USD"
    )
    cost_multiplier = models.DecimalField(
        max_digits=4, decimal_places=2,
        default=Decimal('1.00'),
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Cost multiplier for matching items"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Packing Rule"
        verbose_name_plural = "Packing Rules"
        ordering = ['-priority', 'title']

    def __str__(self):
        return f"{self.title} (Priority: {self.priority})"

    def matches_conditions(self, total_weight, total_volume, item_count, product_types=None):
        """Check if given parameters match this rule's conditions"""
        
        # Check weight constraints
        if self.min_weight is not None and total_weight < self.min_weight:
            return False
        if self.max_weight is not None and total_weight > self.max_weight:
            return False
        
        # Check volume constraints
        if self.min_volume is not None and total_volume < self.min_volume:
            return False
        if self.max_volume is not None and total_volume > self.max_volume:
            return False
        
        # Check item count constraints
        if self.min_items is not None and item_count < self.min_items:
            return False
        if self.max_items is not None and item_count > self.max_items:
            return False
        
        # Check product type constraints
        if self.product_types.exists() and product_types:
            rule_product_types = set(self.product_types.values_list('id', flat=True))
            item_product_types = set(product_types)
            if not rule_product_types.intersection(item_product_types):
                return False
        
        return True

    def apply_rule(self, base_cost):
        """Apply rule modifications to base cost"""
        modified_cost = base_cost * self.cost_multiplier + self.additional_cost
        return modified_cost

    def get_action_summary(self):
        """Get human-readable summary of rule actions"""
        actions = []
        
        if self.preferred_box:
            actions.append(f"Use {self.preferred_box.title}")
        
        if self.force_mailer:
            actions.append("Force mailer")
        
        if self.force_separate_packaging:
            actions.append("Separate packaging")
        
        if self.additional_cost > 0:
            actions.append(f"Add ${self.additional_cost}")
        
        if self.cost_multiplier != 1:
            actions.append(f"Multiply cost by {self.cost_multiplier}")
        
        return "; ".join(actions) if actions else "No special actions"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate weight constraints
        if (self.min_weight is not None and self.max_weight is not None and 
            self.min_weight > self.max_weight):
            raise ValidationError("Minimum weight cannot be greater than maximum weight")
        
        # Validate volume constraints
        if (self.min_volume is not None and self.max_volume is not None and 
            self.min_volume > self.max_volume):
            raise ValidationError("Minimum volume cannot be greater than maximum volume")
        
        # Validate item count constraints
        if (self.min_items is not None and self.max_items is not None and 
            self.min_items > self.max_items):
            raise ValidationError("Minimum items cannot be greater than maximum items")
        
        # Validate that rule has at least one condition
        has_condition = any([
            self.min_weight is not None,
            self.max_weight is not None,
            self.min_volume is not None,
            self.max_volume is not None,
            self.min_items is not None,
            self.max_items is not None,
            self.product_types.exists()
        ])
        
        if not has_condition:
            raise ValidationError("Rule must have at least one condition")
