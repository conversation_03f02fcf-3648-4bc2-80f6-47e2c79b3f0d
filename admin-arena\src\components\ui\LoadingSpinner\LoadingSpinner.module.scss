@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.spinner {
  display: inline-block;
  position: relative;
}

.circle {
  border-radius: 50%;
  border-style: solid;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Size variants
.sm .circle {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.md .circle {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.lg .circle {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.xl .circle {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

// Color variants
.primary .circle {
  border-color: $primary-200 $primary-200 $primary-600 $primary-600;
}

.secondary .circle {
  border-color: $gray-200 $gray-200 $gray-600 $gray-600;
}

.white .circle {
  border-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.3) white white;
}

// Loading overlay
.container {
  position: relative;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  @include flex-column-center;
  z-index: 10;
  border-radius: inherit;
}

.overlayContent {
  @include flex-column-center;
  gap: $spacing-3;
}

.message {
  margin: 0;
  color: $gray-700;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

// Page loading
.pageLoading {
  @include flex-column-center;
  min-height: 200px;
  gap: $spacing-4;
}

.pageMessage {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
}

// Button loading
.buttonLoading {
  @include flex-center;
  gap: $spacing-2;
}

.buttonText {
  font-size: inherit;
  font-weight: inherit;
}
