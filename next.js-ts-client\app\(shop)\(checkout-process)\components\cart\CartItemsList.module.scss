@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.cart_item {
  // background-color: #e9e0e0;
  margin: 0;
  padding: 0.75rem;
  display: grid;
  grid-template-columns: 90px 1fr;
  gap: 0.75rem;
  box-shadow: $box-shadow-1;
  width: 100%;
  height: fit-content;
}

.cart_item__img {
  // @include flexbox(flex-start, center);
  // background-color: #e9b6b6;
  img {
    // @include flexbox(center, center);
    width: 100%;
    max-height: fit-content;
    // height: auto;
    object-fit: contain;
  }
}

.cart_item__info {
  @include flexbox(flex-start, flex-start, column);
  gap: 0.2rem;

  span {
    // background-color: #e6d8d8;
    font-size: $font-size-2;
    color: $primary-lighter-text-color;
    font-weight: bold;
  }

  .cart_item__title {
    font-size: 16px;
    font-weight: bold;

    a {
      color: $primary-dark;
    }

    &:hover {
      text-decoration: underline;
      color: $primary-blue;
    }
  }

  .cart_item__extra_data {
    @include flexbox(flex-start, center);
    gap: 0.5rem;
    font-size: 13.5px;
    color: $primary-lighter-text-color;

    p:first-child {
      font-weight: bold;
    }
  }
}

.cart_item__quantity {
  grid-column: 1 / 3;
  @include flexbox(flex-start, center, column);
  // background-color: #96eea2;
  // width: 100%;
  // margin: 0 auto;
  // column-gap: 1rem;

  div:first-child {
    // background-color: #978c8c;
    @include flexbox(flex-start, center);
    column-gap: 0.8rem;

    p {
      font-weight: bold;
      color: $primary-dark-text-color;
    }

    button {
      padding: 4px;
      border: 1.6px solid #fff;
      border-radius: 2px;
      transition: all 0.2s ease-out;
      // background-color: $sky-lighter-blue;

      i {
        @include flexbox(center, center);
      }

      &:hover {
        border: 1.6px solid $primary-blue;
        color: $primary-blue;
      }

      &:disabled:hover {
        border: 1.6px solid #fff;
        color: inherit;
        cursor: not-allowed;
      }
    }

    button:nth-child(5) {
      background-color: $error-red;
      border: 1.6px solid $error-red;
      transition: all 0.3s ease;

      i {
        font-weight: bold;
        color: #fff;
      }

      &:hover {
        background-color: darken($error-red, 10%);
        // border: 1.6px solid $error-red;

        i {
          color: #fff;
        }
      }
    }
  }

  p:last-child {
    margin: 10px 0;
    color: $primary-red;
    font-weight: bold;
    text-transform: none;
    // background-color: #c2a2a2;
  }
}

@media (width > $tablet) {
  .cart_item {
    grid-template-columns: 110px 1fr auto;
  }

  .cart_item__quantity {
    width: max-content;
    grid-column: 3 / 4;
    justify-content: center;
  }
}
