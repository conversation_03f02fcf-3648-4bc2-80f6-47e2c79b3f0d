@use '../../../../app/../src/scss/variables' as *;
@use '../../../../app/../src/scss/mixins' as *;

// Mobile-first base styles
.profileLoading {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  height: 2rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  width: 150px;
}

.section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.sectionTitle {
  height: 1.2rem;
  width: 120px;
  margin-bottom: 1rem;
  border-radius: 4px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.inputField {
  flex: 1;
  height: 2rem;
  border-radius: 4px;
}

.addressList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.addressCard {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.8rem;
}

.addressType {
  height: 1rem;
  width: 60px;
  margin-bottom: 0.5rem;
  border-radius: 4px;
}

.addressLine {
  height: 0.8rem;
  width: 100%;
  margin-bottom: 0.5rem;
  border-radius: 4px;

  &:last-child {
    width: 60%;
  }
}

.addressActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.button {
  height: 1.8rem;
  width: 100%;
  border-radius: 4px;
}

.saveButton {
  height: 2rem;
  width: 100px;
  border-radius: 6px;
  margin: 0 auto;
}

.pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .profileLoading {
    max-width: 800px;
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
    height: 2.5rem;
    margin-bottom: 2rem;
    width: 200px;
  }

  .section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .sectionTitle {
    height: 1.5rem;
    width: 150px;
  }

  .formGroup {
    flex-direction: row;
    gap: 1rem;
  }

  .inputField {
    height: 2.5rem;
  }

  .addressList {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }

  .addressCard {
    padding: 1rem;
  }

  .addressType {
    height: 1.2rem;
    width: 80px;
  }

  .addressLine {
    height: 1rem;
  }

  .addressActions {
    flex-direction: row;
  }

  .button {
    height: 2rem;
    width: 80px;
  }

  .saveButton {
    height: 2.5rem;
    width: 120px;
  }
}
