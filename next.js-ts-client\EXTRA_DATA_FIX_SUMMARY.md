# Extra Data Fix Summary

## Problem
The Next.js ProductInfo component was incorrectly populating `extra_data` with ALL product variant attribute values, even when those attributes were not selectable by the user. This differs from the correct behavior in the legacy React app.

## Root Cause
The component was setting `extra_data` whenever any attribute selection was made, without checking if the attributes were actually selectable by the user.

## Solution
Updated the ProductInfo component to only populate `extra_data` when:

1. There are selectable attributes (`option_selectors` or `selectable_attribute_values`)
2. The user has interacted with the selection interface
3. The user has made selections from truly selectable attributes

## Key Changes Made

### 1. Updated useEffect for extra_data synchronization
- Added check for `hasSelectableAttributes`
- Added check for `hasUserInteracted` 
- Filter selected attributes to only include selectable ones
- Only set `extra_data` if there are actual selectable attribute selections

### 2. Removed direct extra_data updates from handlers
- Removed `setExtraData()` calls from `handlePrimarySelection`
- Removed `setExtraData()` calls from `handleAttributeSelection`
- Let the useEffect handle all extra_data updates based on the logic

### 3. Added SelectableAttributeValues component support
- Import SelectableAttributeValues component
- Render it when there are no option_selectors but there are selectable_attribute_values
- This matches the behavior of the legacy React app

## Scenarios Handled

### Scenario 1: Product with only fixed variant attributes
- **Before**: extra_data populated with all variant attributes
- **After**: extra_data remains empty (correct)

### Scenario 2: Product with selectable attributes (option_selectors)
- **Before**: extra_data populated with all variant attributes
- **After**: extra_data only populated with user-selected selectable attributes (correct)

### Scenario 3: Product with selectable_attribute_values but no option_selectors
- **Before**: Not handled properly
- **After**: SelectableAttributeValues component renders and handles extra_data correctly

## Files Modified
- `next.js-ts-client/app/(shop)/(products)/components/product-info/ProductInfo.tsx`

## Testing Recommendations
1. Test products with only fixed variant attributes (Size/Color variants with no user selection)
2. Test products with option_selectors (user can choose Size, Color, etc.)
3. Test products with selectable_attribute_values but no option_selectors
4. Verify cart functionality works correctly with the new extra_data logic
