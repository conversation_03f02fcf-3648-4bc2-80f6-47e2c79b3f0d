# Generated by Django 5.2.4 on 2025-07-24 13:19

import cloudinary.models
import django.core.validators
import django.db.models.deletion
import mptt.fields
import utils.ordering
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Attribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='AttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_value', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_value', to='products.attribute')),
            ],
            options={
                'verbose_name_plural': 'Attribute Values',
            },
        ),
        migrations.CreateModel(
            name='AttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Attribute Value (Individual)',
                'verbose_name_plural': 'Attribute Values (Individual)',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attributevalue',),
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent', mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='products.category')),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'unique_together': {('slug', 'parent')},
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(db_index=True, max_length=100)),
                ('slug', models.SlugField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('is_digital', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('average_rating', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='products.brand')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='products.category')),
            ],
        ),
        migrations.CreateModel(
            name='ProductAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_value_av', to='products.attributevalue')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_value_pv', to='products.product')),
            ],
            options={
                'unique_together': {('attribute_value', 'product')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='attribute_value',
            field=models.ManyToManyField(blank=True, related_name='product_attr_value', through='products.ProductAttributeValue', to='products.attributevalue'),
        ),
        migrations.CreateModel(
            name='ProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='products.producttype')),
            ],
            options={
                'verbose_name': 'Product Type',
            },
        ),
        migrations.AddField(
            model_name='product',
            name='product_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_type', to='products.producttype'),
        ),
        migrations.CreateModel(
            name='BrandProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.brand')),
                ('product_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.producttype')),
            ],
            options={
                'verbose_name': 'Brand Product Type',
                'unique_together': {('brand', 'product_type')},
            },
        ),
        migrations.AddField(
            model_name='brand',
            name='product_types',
            field=models.ManyToManyField(related_name='brands', through='products.BrandProductType', to='products.producttype'),
        ),
        migrations.CreateModel(
            name='ProductTypeAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_filterable', models.BooleanField(default=False)),
                ('is_option_selector', models.BooleanField(default=False)),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_type_attribute_a', to='products.attribute')),
                ('product_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_type_attribute_pt', to='products.producttype')),
            ],
            options={
                'verbose_name': 'Product Type Attribute',
                'unique_together': {('product_type', 'attribute')},
            },
        ),
        migrations.AddField(
            model_name='producttype',
            name='attribute',
            field=models.ManyToManyField(related_name='product_type_attribute', through='products.ProductTypeAttribute', to='products.attribute'),
        ),
        migrations.CreateModel(
            name='ProductTypeAttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Product Type Attribute (Individual)',
                'verbose_name_plural': 'Product Type Attributes (Individual)',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttypeattribute',),
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(db_index=True, editable=False, verbose_name='order')),
                ('price', models.DecimalField(decimal_places=2, max_digits=6, validators=[django.core.validators.MinValueValidator(Decimal('1'))])),
                ('sku', models.CharField(max_length=100, unique=True)),
                ('stock_qty', models.PositiveIntegerField()),
                ('is_active', models.BooleanField(default=True)),
                ('weight', models.DecimalField(decimal_places=2, help_text='Please add the weight in grams.', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('length', models.DecimalField(decimal_places=2, default=Decimal('1.00'), help_text='Length in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('width', models.DecimalField(decimal_places=2, default=Decimal('1.00'), help_text='Width in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('height', models.DecimalField(decimal_places=2, default=Decimal('1.00'), help_text='Height in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('volume', models.DecimalField(decimal_places=4, default=Decimal('1.0000'), editable=False, help_text='Computed volume in cubic centimeters', max_digits=12)),
                ('condition', models.CharField(choices=[('New', 'New'), ('Used', 'Used')], default='New', max_length=4)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('price_label', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.attributevalue')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_variant', to='products.product')),
            ],
            options={
                'verbose_name': 'Product Variant',
                'ordering': ['order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alternative_text', models.CharField(max_length=100)),
                ('image', cloudinary.models.CloudinaryField(max_length=255, verbose_name='image')),
                ('order', utils.ordering.OrderField(blank=True)),
                ('product_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_image', to='products.productvariant')),
            ],
        ),
        migrations.CreateModel(
            name='Discount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('discount_percentage', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('is_active', models.BooleanField(default=False)),
                ('product_variants', models.ManyToManyField(blank=True, related_name='discounts', to='products.productvariant')),
            ],
        ),
        migrations.CreateModel(
            name='ProductVariantAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('order', utils.ordering.OrderField(help_text='Display order for this attribute value')),
                ('attribute_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_attribute_value_av', to='products.attributevalue')),
                ('product_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_attribute_value_pv', to='products.productvariant')),
            ],
            options={
                'verbose_name': 'Product Variant Attribute Value',
                'ordering': ['order', 'attribute_value__attribute__title'],
                'unique_together': {('attribute_value', 'product_variant')},
            },
        ),
        migrations.AddField(
            model_name='productvariant',
            name='attribute_value',
            field=models.ManyToManyField(blank=True, related_name='product_variant_attribute_value', through='products.ProductVariantAttributeValue', to='products.attributevalue'),
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('rating', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('posted_at', models.DateField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customers.customer')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='products.product')),
            ],
        ),
    ]
