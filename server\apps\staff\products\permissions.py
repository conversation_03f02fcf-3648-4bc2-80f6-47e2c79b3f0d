from rest_framework.permissions import BasePermission
from apps.staff.authorization.permissions import CanAccessStaffAPI


class CanManageProducts(BasePermission):
    """
    Permission class for product management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check basic staff API access
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific product permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_product')
            elif action == 'create':
                return request.user.has_perm('products.add_product')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_product')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_product')
            elif action in ['bulk_update', 'bulk_create', 'bulk_delete']:
                return request.user.has_perm('products.bulk_update_products')
            elif action == 'change_status':
                return request.user.has_perm('products.change_product_status')
        
        # Default to view permission for other actions
        return request.user.has_perm('products.view_product')


class CanManageCategories(BasePermission):
    """
    Permission class for category management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_category')
            elif action == 'create':
                return request.user.has_perm('products.add_category')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_category')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_category')
            elif action == 'move':
                return request.user.has_perm('products.move_category')
        
        return request.user.has_perm('products.view_category')


class CanManageBrands(BasePermission):
    """
    Permission class for brand management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_brand')
            elif action == 'create':
                return request.user.has_perm('products.add_brand')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_brand')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_brand')
        
        return request.user.has_perm('products.view_brand')


class CanManageProductTypes(BasePermission):
    """
    Permission class for product type management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_producttype')
            elif action == 'create':
                return request.user.has_perm('products.add_producttype')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_producttype')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_producttype')
            elif action == 'manage_attributes':
                return request.user.has_perm('products.manage_type_attributes')
        
        return request.user.has_perm('products.view_producttype')


class CanManageAttributes(BasePermission):
    """
    Permission class for attribute management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_attribute')
            elif action == 'create':
                return request.user.has_perm('products.add_attribute')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_attribute')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_attribute')
            elif action in ['bulk_associate', 'manage_associations']:
                return request.user.has_perm('products.manage_attribute_associations')
        
        return request.user.has_perm('products.view_attribute')


class CanManageReviews(BasePermission):
    """
    Permission class for review management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_review')
            elif action == 'moderate':
                return request.user.has_perm('products.moderate_review')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_review')
        
        return request.user.has_perm('products.view_review')


class CanManageDiscounts(BasePermission):
    """
    Permission class for discount management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('products.view_discount')
            elif action == 'create':
                return request.user.has_perm('products.add_discount')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('products.change_discount')
            elif action == 'destroy':
                return request.user.has_perm('products.delete_discount')
            elif action == 'apply_to_variants':
                return request.user.has_perm('products.apply_discount')
        
        return request.user.has_perm('products.view_discount')


class IsProductManager(BasePermission):
    """
    Permission class for Product Manager role
    Full access to all product operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check if user is in Product Manager group or has superuser status
        return (
            request.user.is_superuser or
            request.user.groups.filter(name='Product Manager').exists()
        )


class IsInventoryManager(BasePermission):
    """
    Permission class for Inventory Manager role
    Limited to stock and variant management
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        return (
            request.user.is_superuser or
            request.user.groups.filter(name__in=['Product Manager', 'Inventory Manager']).exists()
        )
    
    def has_object_permission(self, request, view, obj):
        # Inventory managers can only modify stock-related fields
        if hasattr(view, 'action') and view.action in ['update', 'partial_update']:
            # This would be handled in the serializer to restrict fields
            return True
        return self.has_permission(request, view)


class IsContentManager(BasePermission):
    """
    Permission class for Content Manager role
    Limited to content editing and category management
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        return (
            request.user.is_superuser or
            request.user.groups.filter(name__in=['Product Manager', 'Content Manager']).exists()
        )
