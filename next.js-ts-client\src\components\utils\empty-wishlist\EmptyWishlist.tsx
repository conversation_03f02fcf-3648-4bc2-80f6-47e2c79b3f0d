import Link from 'next/link'
import { FaRegHeart } from 'react-icons/fa'
import styles from './EmptyWishlist.module.scss'

interface Props {
  message?: string
  showIcon?: boolean
  actionText?: string
  actionHref?: string
}

const EmptyWishlist = ({
  message,
  showIcon = true,
  actionText = "Go Shopping",
  actionHref = "/"
}: Props) => {
  return (
    <div className={styles.empty_wishlist} role="region" aria-label="Empty wishlist">
      {showIcon && (
        <div className={styles.icon_section} aria-hidden="true">
          <FaRegHeart />
        </div>
      )}

      <div className={styles.content_section}>
        <h2 className={styles.heading}>
          {message ? message : "Your wishlist is empty"}
        </h2>
        <p className={styles.description}>
          Save items you love for later shopping and never lose track of your favorites.
        </p>
      </div>

      <div className={styles.action_section}>
        <Link
          href={actionHref}
          className={styles.action_button}
          aria-label={`${actionText} - Browse products to add to your wishlist`}
        >
          {actionText}
        </Link>
      </div>
    </div>
  )
}
export default EmptyWishlist