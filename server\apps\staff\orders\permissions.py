from rest_framework.permissions import BasePermission
from apps.staff.common.constants import STAFF_GROUPS


class CanManageOrders(BasePermission):
    """
    Permission for order management operations.
    Checks if staff user has appropriate order management permissions.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        # Check if user has order management permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'],
            STAFF_GROUPS['ORDER_FULFILLMENT']
        }

        return bool(user_groups.intersection(allowed_groups))

    def has_object_permission(self, request, view, obj):
        """Check object-level permissions for order access"""
        if not self.has_permission(request, view):
            return False

        if request.user.is_superuser:
            return True

        # Check if staff member can process this specific order
        if hasattr(request.user, 'staff_profile'):
            from .models import OrderProxy
            order_proxy = OrderProxy.objects.get(pk=obj.pk)
            return order_proxy.can_be_processed_by_staff(request.user.staff_profile)

        return False


class CanChangeOrderStatus(BasePermission):
    """
    Permission for changing order status.
    More restrictive than general order management.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        # Check specific permissions based on action
        if request.method in ['PATCH', 'PUT']:
            return request.user.has_perm('order.change_order')

        return True

    def has_object_permission(self, request, view, obj):
        """Check if user can change status of specific order"""
        if not self.has_permission(request, view):
            return False

        if request.user.is_superuser:
            return True

        user_groups = set(request.user.groups.values_list('name', flat=True))

        # Order Management Executive can change any status
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return True

        # Order Fulfillment Specialist can only change specific statuses
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            # Can only change from Pending to Processing, or Processing to Dispatched
            current_status = obj.order_status
            if request.method in ['PATCH', 'PUT']:
                new_status = request.data.get('order_status')
                allowed_transitions = {
                    'Pending': ['Processing'],
                    'Processing': ['Dispatched']
                }
                return new_status in allowed_transitions.get(current_status, [])

        # Order Management Group Member has limited status change permissions
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            # Can only change from Pending to Processing
            current_status = obj.order_status
            if request.method in ['PATCH', 'PUT']:
                new_status = request.data.get('order_status')
                return current_status == 'Pending' and new_status == 'Processing'

        return False


class CanAssignOrders(BasePermission):
    """
    Permission for assigning orders to staff members.
    Only certain roles can assign orders.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        user_groups = set(request.user.groups.values_list('name', flat=True))

        # Only Order Management Executive and Department Heads can assign orders
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['DEPARTMENT_HEAD']
        }

        return bool(user_groups.intersection(allowed_groups))


class CanViewOrderReports(BasePermission):
    """
    Permission for viewing order reports and analytics.
    Includes business analysts and management roles.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        user_groups = set(request.user.groups.values_list('name', flat=True))

        # Roles that can view order reports
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['BUSINESS_ANALYST'],
            STAFF_GROUPS['FINANCE_MANAGER'],
            STAFF_GROUPS['DEPARTMENT_HEAD']
        }

        return bool(user_groups.intersection(allowed_groups))


class CanAddOrderNotes(BasePermission):
    """
    Permission for adding notes to orders.
    Most order-related staff can add notes.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        user_groups = set(request.user.groups.values_list('name', flat=True))

        # Most order-related roles can add notes
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'],
            STAFF_GROUPS['ORDER_FULFILLMENT'],
            STAFF_GROUPS['CUSTOMER_SERVICE']
        }

        return bool(user_groups.intersection(allowed_groups))


class CanViewOrderReports(BasePermission):
    """
    Permission for viewing order reports and analytics.
    Available to order management roles and analysts.
    """

    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False

        if request.user.is_superuser:
            return True

        user_groups = set(request.user.groups.values_list('name', flat=True))

        # Roles that can view order reports
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'],
            STAFF_GROUPS['DEPARTMENT_HEAD'],
            STAFF_GROUPS['FINANCE_MANAGER']
        }

        return bool(user_groups.intersection(allowed_groups))
