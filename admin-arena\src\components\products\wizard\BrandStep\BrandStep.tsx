// Brand selection step in product wizard
// Allows selecting or creating brands with logo upload

import React, { useState } from 'react'
import { FiPlus, FiImage, FiCheck } from 'react-icons/fi'
import { useProductBrands, useCreateBrand } from '../../../../hooks/use-products'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Input } from '../../../ui/Input'
import { Modal } from '../../../ui/Modal'
import { LoadingSpinner } from '../../../ui/LoadingSpinner'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import { Brand } from '../../../../types/api-types'
import styles from './BrandStep.module.scss'

interface BrandStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

export const BrandStep: React.FC<BrandStepProps> = ({ data, onUpdate }) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [newBrandName, setNewBrandName] = useState('')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const { data: brands, isLoading, refetch } = useProductBrands()
  const createBrandMutation = useCreateBrand()

  const handleBrandSelect = (brand: Brand) => {
    onUpdate({
      brand: {
        id: brand.id,
        name: brand.title,
        logo: brand.logo,
      }
    })
  }

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setLogoFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleCreateBrand = async () => {
    if (!newBrandName.trim()) return

    try {
      const formData = new FormData()
      formData.append('title', newBrandName)
      if (logoFile) {
        formData.append('logo', logoFile)
      }

      await createBrandMutation.mutateAsync(formData as any)

      setIsCreateModalOpen(false)
      setNewBrandName('')
      setLogoFile(null)
      setLogoPreview(null)
      refetch()
    } catch (error) {
      console.error('Failed to create brand:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <LoadingSpinner size="lg" />
        <p>Loading brands...</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Select Brand</h3>
          <p>Choose the brand for your product. Brands help customers identify and trust your products.</p>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <FiPlus />
          New Brand
        </Button>
      </div>

      {data.brand && (
        <Card className={styles.selectedCard}>
          <div className={styles.selectedBrand}>
            <div className={styles.selectedLogo}>
              {data.brand.logo ? (
                <img src={data.brand.logo} alt={data.brand.name} />
              ) : (
                <FiImage />
              )}
            </div>
            <div className={styles.selectedInfo}>
              <h4>Selected Brand</h4>
              <p>{data.brand.name}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onUpdate({ brand: undefined })}
            >
              Change
            </Button>
          </div>
        </Card>
      )}

      <div className={styles.brandsGrid}>
        {brands && brands.length > 0 ? (
          brands.map((brand) => {
            const isSelected = data.brand?.id === brand.id

            return (
              <Card
                key={brand.id}
                className={`${styles.brandCard} ${isSelected ? styles.selected : ''}`}
                hover={!isSelected}
              >
                <div className={styles.brandContent}>
                  <div className={styles.brandLogo}>
                    {brand.logo ? (
                      <img src={brand.logo} alt={brand.name} />
                    ) : (
                      <FiImage />
                    )}
                  </div>

                  <div className={styles.brandInfo}>
                    <h4 className={styles.brandName}>{brand.title}</h4>
                    <p className={styles.brandStats}>
                      {brand.products_count || 0} products
                    </p>
                  </div>

                  {isSelected && (
                    <div className={styles.selectedBadge}>
                      <FiCheck />
                    </div>
                  )}

                  <Button
                    variant={isSelected ? "primary" : "outline"}
                    size="sm"
                    onClick={() => handleBrandSelect(brand)}
                    className={styles.selectButton}
                  >
                    {isSelected ? 'Selected' : 'Select Brand'}
                  </Button>
                </div>
              </Card>
            )
          })
        ) : (
          <div className={styles.emptyState}>
            <FiImage />
            <h4>No brands found</h4>
            <p>Create your first brand to organize your products</p>
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <FiPlus />
              Create Brand
            </Button>
          </div>
        )}
      </div>

      {/* Create Brand Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Brand"
        size="md"
      >
        <div className={styles.createModal}>
          <div className={styles.formGroup}>
            <label htmlFor="brandName">Brand Name</label>
            <Input
              id="brandName"
              type="text"
              value={newBrandName}
              onChange={(e) => setNewBrandName(e.target.value)}
              placeholder="Enter brand name"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="brandLogo">Brand Logo (Optional)</label>
            <div className={styles.logoUpload}>
              <input
                id="brandLogo"
                type="file"
                accept="image/*"
                onChange={handleLogoChange}
                className={styles.fileInput}
              />
              <label htmlFor="brandLogo" className={styles.uploadArea}>
                {logoPreview ? (
                  <img src={logoPreview} alt="Logo preview" className={styles.logoPreview} />
                ) : (
                  <div className={styles.uploadPlaceholder}>
                    <FiImage />
                    <span>Click to upload logo</span>
                  </div>
                )}
              </label>
            </div>
          </div>

          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateBrand}
              disabled={!newBrandName.trim() || createBrandMutation.isPending}
            >
              {createBrandMutation.isPending ? 'Creating...' : 'Create Brand'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
