# Celery Tasks for Async Cart Operations
# High-performance async task implementations for cart operations

from celery import shared_task, group, chain, chord
from celery.exceptions import Retry
from django.db import transaction
from django.utils import timezone
from django.core.cache import cache
from decimal import Decimal
import logging
import time
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def update_cart_totals_async(self, cart_id: str, force_recalculate: bool = False):
    """
    Async task to update cart totals with optimized database operations
    """
    try:
        from apps.cart.models import Cart, CartItem
        from apps.cart.services.cache import CartCacheService
        
        cache_service = CartCacheService()
        
        # Check if we have cached totals and don't need to recalculate
        if not force_recalculate:
            cached_data = cache_service.get_cached_cart_data(cart_id)
            if cached_data and cached_data.get('last_updated'):
                last_updated = timezone.datetime.fromisoformat(cached_data['last_updated'])
                if (timezone.now() - last_updated).seconds < 300:  # 5 minutes
                    logger.info(f"Cart {cart_id} totals are recent, skipping update")
                    return cached_data
        
        # Get cart with optimized query
        try:
            cart = Cart.objects.select_related('customer').prefetch_related(
                'cart_items__product_variant__product',
                'cart_items__product_variant__discounts'
            ).get(id=cart_id)
        except Cart.DoesNotExist:
            logger.warning(f"Cart {cart_id} not found for total update")
            return None
        
        # Calculate totals using database aggregation where possible
        with transaction.atomic():
            # Update cart weight using optimized method
            total_weight = cart.get_cart_weight()
            
            # Calculate total price with discounts
            total_price = Decimal('0.00')
            item_count = 0
            
            for item in cart.cart_items.all():
                # Use prefetched discount data
                active_discounts = [d for d in item.product_variant.discounts.all() 
                                  if d.is_active and d.is_valid_for_product(item.product_variant)]
                
                if active_discounts:
                    price = active_discounts[0].apply_discount(item.product_variant.price)
                else:
                    price = item.product_variant.price
                
                total_price += price * item.quantity
                item_count += item.quantity
            
            # Update cart fields
            cart.total_weight = total_weight
            cart.total_price = total_price
            cart.item_count = item_count
            cart.last_updated = timezone.now()
            cart.save(update_fields=['total_weight', 'total_price', 'item_count', 'last_updated'])
        
        # Cache the updated data
        cache_service.cache_cart_data(cart)
        
        result = {
            'cart_id': str(cart_id),
            'total_weight': float(total_weight),
            'total_price': float(total_price),
            'item_count': item_count,
            'updated_at': cart.last_updated.isoformat()
        }
        
        logger.info(f"Updated cart {cart_id} totals: {result}")
        return result
        
    except Exception as exc:
        logger.error(f"Error updating cart {cart_id} totals: {exc}")
        self.retry(exc=exc, countdown=60 * (self.request.retries + 1))


@shared_task(bind=True, max_retries=3, default_retry_delay=120)
def calculate_shipping_async(self, cart_id: str, force_recalculate: bool = False, 
                           destination_data: Dict = None):
    """
    Async task to calculate shipping rates for a cart
    """
    try:
        from apps.cart.models import Cart
        from apps.shipping.services.cart import CartShippingService
        from apps.cart.services.cache import ShippingCacheService
        
        shipping_cache = ShippingCacheService()
        
        # Get cart with items
        try:
            cart = Cart.objects.prefetch_related(
                'cart_items__product_variant__product'
            ).get(id=cart_id)
        except Cart.DoesNotExist:
            logger.warning(f"Cart {cart_id} not found for shipping calculation")
            return None
        
        # Skip if cart is empty
        if not cart.cart_items.exists():
            logger.info(f"Cart {cart_id} is empty, skipping shipping calculation")
            return None
        
        # Use destination from parameter or cart's saved destination
        destination = None
        if destination_data:
            from apps.shipping.models import Destination
            destination = Destination(**destination_data)
        elif hasattr(cart, 'shipping_destination'):
            destination = cart.shipping_destination
        
        if not destination:
            logger.info(f"No destination for cart {cart_id}, skipping shipping calculation")
            return None
        
        # Check cache first
        if not force_recalculate:
            cached_result = shipping_cache.get_cached_shipping_calculation(
                cart.cart_items.all(), destination
            )
            if cached_result:
                logger.info(f"Using cached shipping calculation for cart {cart_id}")
                return cached_result
        
        # Calculate shipping
        shipping_service = CartShippingService()
        start_time = time.time()
        
        shipping_result = shipping_service.calculate_shipping_for_cart(cart, destination)
        
        calculation_time = time.time() - start_time
        
        # Update cart with shipping information
        with transaction.atomic():
            cart.shipping_cost = shipping_result.get('total_cost', 0)
            cart.estimated_delivery_days = shipping_result.get('estimated_days', 0)
            cart.last_shipping_calculation = timezone.now()
            cart.save(update_fields=[
                'shipping_cost', 'estimated_delivery_days', 'last_shipping_calculation'
            ])
        
        # Cache the result
        shipping_cache.cache_shipping_calculation(
            cart.cart_items.all(), destination, shipping_result
        )
        
        result = {
            'cart_id': str(cart_id),
            'shipping_cost': float(shipping_result.get('total_cost', 0)),
            'estimated_days': shipping_result.get('estimated_days', 0),
            'calculation_time': calculation_time,
            'carriers_checked': len(shipping_result.get('carrier_rates', [])),
            'calculated_at': timezone.now().isoformat()
        }
        
        logger.info(f"Calculated shipping for cart {cart_id}: {result}")
        
        # Trigger packing calculation if needed
        if shipping_result.get('requires_packing', True):
            calculate_packing_async.delay(cart_id, force_recalculate=force_recalculate)
        
        return result
        
    except Exception as exc:
        logger.error(f"Error calculating shipping for cart {cart_id}: {exc}")
        self.retry(exc=exc, countdown=120 * (self.request.retries + 1))


@shared_task(bind=True, max_retries=2, default_retry_delay=180)
def calculate_packing_async(self, cart_id: str, force_recalculate: bool = False):
    """
    Async task to calculate optimal packing for cart items
    """
    try:
        from apps.cart.models import Cart
        from apps.shipping.services.packing import PackingService
        from apps.cart.services.cache import PackingCacheService
        
        packing_cache = PackingCacheService()
        
        # Get cart with items
        try:
            cart = Cart.objects.prefetch_related(
                'cart_items__product_variant__product__product_type'
            ).get(id=cart_id)
        except Cart.DoesNotExist:
            logger.warning(f"Cart {cart_id} not found for packing calculation")
            return None
        
        cart_items = cart.cart_items.all()
        if not cart_items:
            logger.info(f"Cart {cart_id} is empty, skipping packing calculation")
            return None
        
        # Check cache first
        if not force_recalculate:
            cached_result = packing_cache.get_cached_packing_result(cart_items)
            if cached_result:
                logger.info(f"Using cached packing result for cart {cart_id}")
                return self._serialize_packing_result(cached_result)
        
        # Calculate packing
        packing_service = PackingService()
        start_time = time.time()
        
        packing_result = packing_service.calculate_packing_for_cart_items(cart_items)
        
        calculation_time = time.time() - start_time
        
        # Update cart with packing information
        with transaction.atomic():
            cart.packing_cost = packing_result.total_cost
            cart.total_volume = packing_result.total_volume
            cart.box_count = len(packing_result.boxes)
            cart.packing_efficiency = packing_result.efficiency_score
            cart.last_packing_calculation = timezone.now()
            cart.save(update_fields=[
                'packing_cost', 'total_volume', 'box_count', 
                'packing_efficiency', 'last_packing_calculation'
            ])
        
        # Cache the result
        packing_cache.cache_packing_result(cart_items, packing_result)
        
        result = {
            'cart_id': str(cart_id),
            'packing_cost': float(packing_result.total_cost),
            'total_volume': float(packing_result.total_volume),
            'box_count': len(packing_result.boxes),
            'efficiency_score': packing_result.efficiency_score,
            'calculation_time': calculation_time,
            'method_used': packing_result.method_used,
            'calculated_at': timezone.now().isoformat()
        }
        
        logger.info(f"Calculated packing for cart {cart_id}: {result}")
        return result
        
    except Exception as exc:
        logger.error(f"Error calculating packing for cart {cart_id}: {exc}")
        self.retry(exc=exc, countdown=180 * (self.request.retries + 1))
    
    def _serialize_packing_result(self, packing_result):
        """Serialize packing result for task return"""
        return {
            'total_cost': float(packing_result.total_cost),
            'total_volume': float(packing_result.total_volume),
            'box_count': len(packing_result.boxes),
            'efficiency_score': packing_result.efficiency_score,
            'method_used': packing_result.method_used,
            'success': packing_result.success
        }


@shared_task(bind=True, max_retries=5, default_retry_delay=30)
def fetch_carrier_rates_async(self, carrier_code: str, package_data: Dict, 
                             destination_data: Dict, cart_id: str = None):
    """
    Async task to fetch rates from individual carriers
    """
    try:
        from apps.shipping.services.carriers import CarrierServiceFactory
        from apps.cart.services.cache import ShippingCacheService
        
        shipping_cache = ShippingCacheService()
        
        # Check cache first
        package_signature = shipping_cache.generate_cache_key('package', package_data)
        destination_signature = shipping_cache.generate_cache_key('destination', destination_data)
        
        cached_rate = shipping_cache.get_cached_carrier_rate(
            carrier_code, package_signature, destination_signature
        )
        
        if cached_rate:
            logger.info(f"Using cached rate for {carrier_code}")
            return cached_rate
        
        # Get carrier service
        carrier_service = CarrierServiceFactory.get_service(carrier_code)
        
        start_time = time.time()
        
        # Fetch rates with timeout
        rate_data = carrier_service.get_rates(
            package_data=package_data,
            destination_data=destination_data,
            timeout=25  # 25 second timeout
        )
        
        response_time = time.time() - start_time
        
        # Add metadata
        rate_data.update({
            'carrier_code': carrier_code,
            'response_time_ms': int(response_time * 1000),
            'fetched_at': timezone.now().isoformat(),
            'cart_id': cart_id
        })
        
        # Cache the result
        shipping_cache.cache_carrier_rate(
            carrier_code, package_signature, destination_signature, rate_data
        )
        
        logger.info(f"Fetched {carrier_code} rates in {response_time:.2f}s")
        return rate_data
        
    except Exception as exc:
        logger.error(f"Error fetching {carrier_code} rates: {exc}")
        
        # Don't retry on certain errors
        if 'authentication' in str(exc).lower() or 'invalid' in str(exc).lower():
            logger.error(f"Permanent error for {carrier_code}, not retrying")
            return {
                'carrier_code': carrier_code,
                'error': str(exc),
                'permanent_error': True
            }
        
        self.retry(exc=exc, countdown=30 * (self.request.retries + 1))


@shared_task
def bulk_cart_operations(operations: List[Dict], cart_id: str):
    """
    Process multiple cart operations in a single transaction
    """
    try:
        from apps.cart.models import Cart, CartItem
        from apps.products.models import ProductVariant
        
        results = []
        
        with transaction.atomic():
            cart, created = Cart.objects.get_or_create(id=cart_id)
            
            for operation in operations:
                try:
                    result = _process_cart_operation(cart, operation)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'operation': operation,
                        'success': False,
                        'error': str(e)
                    })
        
        # Trigger async updates after all operations
        update_cart_totals_async.delay(cart_id, force_recalculate=True)
        
        return {
            'cart_id': str(cart_id),
            'operations_processed': len(operations),
            'successful_operations': len([r for r in results if r.get('success')]),
            'results': results
        }
        
    except Exception as exc:
        logger.error(f"Error in bulk cart operations for {cart_id}: {exc}")
        raise


def _process_cart_operation(cart, operation):
    """Process a single cart operation"""
    from apps.cart.models import CartItem
    from apps.products.models import ProductVariant
    
    op_type = operation['type']
    variant_id = operation['product_variant_id']
    
    if op_type == 'add':
        quantity = operation['quantity']
        variant = ProductVariant.objects.get(id=variant_id)
        
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product_variant=variant,
            defaults={
                'quantity': quantity,
                'product_id': variant.product_id
            }
        )
        
        if not created:
            cart_item.quantity += quantity
            cart_item.save()
        
        return {
            'operation': operation,
            'success': True,
            'cart_item_id': cart_item.id,
            'new_quantity': cart_item.quantity
        }
    
    elif op_type == 'update':
        quantity = operation['quantity']
        cart_item = CartItem.objects.get(cart=cart, product_variant_id=variant_id)
        cart_item.quantity = quantity
        cart_item.save()
        
        return {
            'operation': operation,
            'success': True,
            'cart_item_id': cart_item.id,
            'new_quantity': cart_item.quantity
        }
    
    elif op_type == 'remove':
        deleted_count, _ = CartItem.objects.filter(
            cart=cart, product_variant_id=variant_id
        ).delete()
        
        return {
            'operation': operation,
            'success': True,
            'removed': deleted_count > 0
        }


@shared_task
def comprehensive_cart_update(cart_id: str, destination_data: Dict = None):
    """
    Comprehensive cart update that chains all necessary calculations
    """
    # Create a chain of tasks
    workflow = chain(
        update_cart_totals_async.s(cart_id, force_recalculate=True),
        calculate_shipping_async.s(cart_id, force_recalculate=True, destination_data=destination_data),
        calculate_packing_async.s(cart_id, force_recalculate=True)
    )
    
    return workflow.apply_async()


@shared_task
def parallel_carrier_rate_fetch(package_data: Dict, destination_data: Dict, 
                               cart_id: str, carriers: List[str] = None):
    """
    Fetch rates from multiple carriers in parallel
    """
    if not carriers:
        carriers = ['ups', 'fedex', 'usps', 'dhl']
    
    # Create parallel tasks for each carrier
    job = group(
        fetch_carrier_rates_async.s(
            carrier_code, package_data, destination_data, cart_id
        ) for carrier_code in carriers
    )
    
    return job.apply_async()


@shared_task
def cart_analytics_update(cart_id: str, event_type: str, event_data: Dict):
    """
    Update cart analytics and business metrics
    """
    try:
        from apps.analytics.models import CartEvent
        from apps.cart.business_metrics import BusinessMetricsCollector
        
        # Record cart event
        CartEvent.objects.create(
            cart_id=cart_id,
            event_type=event_type,
            event_data=event_data,
            timestamp=timezone.now()
        )
        
        # Update business metrics
        metrics_collector = BusinessMetricsCollector()
        metrics_collector.process_cart_event(cart_id, event_type, event_data)
        
        logger.info(f"Updated analytics for cart {cart_id}, event: {event_type}")
        
    except Exception as exc:
        logger.error(f"Error updating cart analytics: {exc}")


@shared_task
def cleanup_expired_carts():
    """
    Cleanup expired carts and cache entries
    """
    try:
        from apps.cart.models import Cart
        from apps.cart.services.cache import CartCacheService
        from datetime import timedelta
        
        # Delete carts older than 30 days with no activity
        cutoff_date = timezone.now() - timedelta(days=30)
        expired_carts = Cart.objects.filter(
            last_updated__lt=cutoff_date,
            cart_items__isnull=True
        )
        
        deleted_count = expired_carts.count()
        expired_carts.delete()
        
        # Clear expired cache entries
        cache_service = CartCacheService()
        cache_service.clear_expired_cache()
        
        logger.info(f"Cleaned up {deleted_count} expired carts")
        return {'deleted_carts': deleted_count}
        
    except Exception as exc:
        logger.error(f"Error cleaning up expired carts: {exc}")
        raise
