import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import authStore from '../stores/auth-store'
import APIClient from '../lib/api-client'
import { ErrorResponse, InitRegUserShape, VerificationCredentials } from '../types/types'
import { passwordSchemaFormInputs } from '@/app/(auth)/register/set-password/page'
import { VerifyContactShape } from '@/app/(auth)/register/update-info/VerifyAuthContact'
import { loginSchema } from '../schemas/schemas'
import z from 'zod'
import { LoginUserShape } from '@/app/(auth)/login/page'
import { CUSTOMER_DETAILS } from '../constants/constants'
import { ChangePasswordShape } from '@/app/(auth)/change-password/page'
import { ResetRequestShape } from '@/app/(auth)/reset-password/PasswordResetReq'
import { VerifyDetailShape } from '@/app/(auth)/reset-password/PasswordResetVerify'
import { NewAuthInfoShape } from '@/app/(auth)/change-auth-info/phone/page'


type RegResponseShape = {
  message: string
  username: string
}

type UpdateAuthInfoResponseShape = {
  message: string
  username: string
}

export interface UpdateAuthInfoInitShape {
  email?: string
  phone_number?: string
}

type ResetPasswordResponseShape = {
  message: string
  email_or_phone: string
}

// Register hooks
export const useRegister = () => {
  const { setUsername } = authStore()

  // In AuthClient Response has defined as: Request = Response 
  // If request data is different do not forget to specify the types here. 
  const apiClient = new APIClient<RegResponseShape, InitRegUserShape>(`/auth/register/initiate/`)

  const mutation = useMutation<RegResponseShape, AxiosError<ErrorResponse>, InitRegUserShape>({
    mutationFn: (data: InitRegUserShape) => apiClient.post(data),  // Here `data` is of type `RegisterUserShape`
    onSuccess: (data) => {
      console.log(data)
      setUsername(data.username)
    }
  })

  return { mutation }
}

export const useSendVerifyRegCredentials = () => {
  const apiClient = new APIClient('/auth/register/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerificationCredentials) => apiClient.post(data)
  })

  return { mutation }
}

export const useSetPassword = () => {

  const apiClient = new APIClient(`/auth/register/set-password/`)

  const mutation = useMutation({
    mutationFn: (data: passwordSchemaFormInputs) => apiClient.post(data)
  })

  return { mutation }
}

// Update customer information hooks
export const useSendUpdateAuthInfo = () => {
  const apiClient = new APIClient<UpdateAuthInfoResponseShape, UpdateAuthInfoInitShape>('/auth/profile/contact/update/')
  // const { setAltUsername } = authStore()

  const authInfoMutation = useMutation<UpdateAuthInfoResponseShape, AxiosError<ErrorResponse>, UpdateAuthInfoInitShape>({
    mutationFn: (data) => apiClient.patch(data),
    // onSuccess: (data) => {
    //   console.log(data)
    //   setAltUsername(data.username)
    // }
  })
  
  return { authInfoMutation }
}

export const useSendVerifyCode = () => {
  const apiClient = new APIClient('/auth/profile/contact/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerifyContactShape) => apiClient.post(data)
  })

  return { mutation }
}



export const useLogin = () => {
  const { setIsLoggedIn } = authStore()
  const apiClient = new APIClient(`/auth/login/`)

  const mutation = useMutation({
    mutationFn: (data: LoginUserShape) => apiClient.post(data),
    onSuccess: () => { // data is the response data
      // const { access } = data.data
      setIsLoggedIn(true)
    }
    // onSettled: (data, error, variables) => {
    //   console.log(data)
    //   console.log(error)
    //   console.log(variables) // variables are user input data
    // }
  })

  return { mutation }
}

export const useLogout = () => {
  const { setIsLoggedIn } = authStore()
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/auth/logout/`)

  const mutation = useMutation({
    mutationFn: () => apiClient.post(),
    onSuccess: () => {
      console.log('Logout was success')
      setIsLoggedIn(false)
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS],
      })
      // queryClient.refetchQueries({
      //   queryKey: [CUSTOMER_DETAILS]
      // })
      queryClient.removeQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}

// Change Existing Password
export const useChangePassword = () => {

  const apiClient = new APIClient(`/auth/password/change/`)

  const mutation = useMutation({
    mutationFn: (data: ChangePasswordShape) => apiClient.post(data)
  })

  return { mutation }
}

// Reset Password 
export const useInitResetPassword = () => {

  const apiClient = new APIClient<ResetPasswordResponseShape, ResetRequestShape>(`/auth/password/reset/request/`)

  const mutation = useMutation<ResetPasswordResponseShape, AxiosError<ErrorResponse>, ResetRequestShape>({
    mutationFn: (newPassword: ResetRequestShape) => apiClient.post(newPassword),
  })

  return { mutation }
}

export const useResetPasswordConfirm = () => {

  const apiClient = new APIClient(`/auth/password/reset/confirm/`)

  const mutation = useMutation({
    mutationFn: (data: VerifyDetailShape) => apiClient.post(data)
  })

  return { mutation }
}

// Change Auth Info
export const useChangeAuthInfo = () => {
  const apiClient = new APIClient(`auth/profile/contact/update/`)

  const mutation = useMutation({
    mutationFn: (data: NewAuthInfoShape) => apiClient.patch(data),
    onSuccess: () => { // data is the response data
      // const { access } = data.data
      // login()
    }
    // onSettled: (data, error, variables) => {
    //   console.log(data)
    //   console.log(error)
    //   console.log(variables) // variables are user input data
    // }
  })

  return { mutation }
}

export const useSendAuthInfoVerifyCode = () => {
  const apiClient = new APIClient('/auth/profile/contact/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerifyContactShape) => apiClient.post(data)
  })

  return { mutation }
}








