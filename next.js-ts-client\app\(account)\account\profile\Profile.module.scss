@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;

// Mobile-first base styles
.profile {
  width: 100%;
  @include flexbox(flex-start, center, column);
  padding: 0 1rem;

  .user_details {
    width: 100%;
    max-width: 100%;
  }
}

.profile__details {
  @include flexbox(flex-start, flex-start, column);
  gap: 1rem;
  padding: 0.5rem;

  div {
    h5 {
      font-weight: bold;
      color: $primary-dark-text-color;
      margin-bottom: 0.5rem;
      font-size: 16px;
    }

    p {
      color: $primary-lighter-text-color;
      font-size: 14px;
    }
  }

  button {
    align-self: stretch;
    margin-top: 1rem;
  }
}

.profile__editable {
  padding: 0.5rem;

  form {
    @include flexbox(flex-start, flex-start, column);
    gap: 1rem;

    div {
      width: 100%;

      label {
        font-size: 14px;
      }

      input {
        width: 100%;
        padding: 0.7rem;
      }
    }

    button {
      width: 100%;
      margin-top: 1rem;
    }
  }
}

.edit_btn {
  @include btn($primary-blue, #fff);
  padding: 0.3rem 1.2rem;
  border: 1px solid $lighten-blue;
  letter-spacing: 0.7px;
  transition: all 0.3s ease;
  height: fit-content;
  font-size: 14px;

  &:hover {
    border: 1px solid $primary-dark-blue;
    color: $primary-dark-blue;
  }
}

.edit_btn__empty {
  @include btn($lighten-blue, #fff);
  padding: 0.3rem 1.2rem;
  border: 1px solid $lighten-blue;
  letter-spacing: 0.7px;
  transition: all 0.3s ease;

  &:hover {
    border: 1px solid $primary-blue;
    color: $primary-blue;
  }
}

.reset_details {
  row-gap: 1.5rem;
  padding: 0.5rem;

  .reset_details__section {
    @include flexbox(flex-start, flex-start, column);
    gap: 1rem;
    padding: 1rem;
    background-color: #f9f9f9;
    border-radius: 8px;

    div {
      width: 100%;

      h5 {
        font-weight: bold;
        color: $primary-dark-text-color;
        margin-bottom: 0.5rem;
        font-size: 16px;
      }

      p {
        color: $primary-lighter-text-color;
        font-size: 14px;
      }
    }

    .no_data {
      font-style: italic;
    }

    button {
      width: 100%;
    }
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .profile {
    padding: 0;

    .user_details {
      max-width: 600px;
    }

    h2 {
      padding: 0.5rem 0;
      font-size: 23px;
    }
  }

  .profile__details {
    @include flexbox(space-between, flex-start, row);
    gap: 2rem;
    padding: 1rem;

    div {
      h5 {
        font-size: 17px;
      }

      p {
        font-size: 16px;
      }
    }

    button {
      align-self: center;
      margin-top: 0;
      max-width: fit-content;
    }
  }

  .profile__editable {
    padding: 1rem;

    form {
      @include flexbox(space-between, flex-start, row);
      gap: 2rem;

      div {
        width: auto;

        label {
          font-size: 16px;
        }

        input {
          width: auto;
          padding: 0.5rem;
        }
      }

      button {
        width: auto;
        margin-top: 0;
        max-width: fit-content;
        align-self: center;
      }
    }
  }

  .reset_details {
    row-gap: 1rem;
    padding: 1rem;

    .reset_details__section {
      @include flexbox(space-between, center, row);
      gap: 2rem;
      padding: 0;
      background-color: transparent;
      border-radius: 0;

      div {
        width: auto;

        h5 {
          font-size: 17px;
        }

        p {
          font-size: 16px;
        }
      }

      button {
        width: auto;
      }
    }
  }
}
