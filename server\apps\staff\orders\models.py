from django.db import models
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
from apps.order.models import Order, OrderItem
from apps.staff.authorization.models import StaffProfile
from apps.staff.common.constants import STAFF_GROUPS

User = get_user_model()


class OrderProxy(Order):
    """
    Proxy model for staff-specific order operations.
    Provides additional methods and behavior for staff order management.
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Order"
        verbose_name_plural = "Staff Orders"

    def can_be_processed_by_staff(self, staff_profile):
        """Check if order can be processed by given staff member"""
        user_groups = set(staff_profile.user.groups.values_list('name', flat=True))

        # Order Management Executive can process all orders
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return True

        # Order Fulfillment Specialist can process pending and processing orders
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            return self.order_status in ['Pending', 'Processing']

        # Order Management Group Member has limited access
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            return self.order_status in ['Pending', 'Processing', 'Dispatched']

        return False

    def get_status_history(self):
        """Get order status change history"""
        return self.status_history.all().order_by('-changed_at')

    def get_current_assignment(self):
        """Get current active assignment"""
        return self.assignments.filter(is_active=True).first()


class OrderStatusHistory(models.Model):
    """
    Track order status changes by staff members.
    Provides audit trail for all order status modifications.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='status_history'
    )
    previous_status = models.CharField(max_length=20)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(
        StaffProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name='order_status_changes'
    )
    changed_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, help_text="Optional notes about the status change")

    class Meta:
        ordering = ['-changed_at']
        verbose_name = "Order Status History"
        verbose_name_plural = "Order Status Histories"

    def __str__(self):
        return f"Order {self.order.id}: {self.previous_status} → {self.new_status}"


class OrderAssignment(models.Model):
    """
    Assign orders to specific staff members for processing.
    Supports workflow management and responsibility tracking.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='assignments'
    )
    assigned_to = models.ForeignKey(
        StaffProfile,
        on_delete=models.CASCADE,
        related_name='assigned_orders'
    )
    assigned_by = models.ForeignKey(
        StaffProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name='orders_assigned_by_me'
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, help_text="Assignment notes or instructions")

    class Meta:
        ordering = ['-assigned_at']
        verbose_name = "Order Assignment"
        verbose_name_plural = "Order Assignments"

    def __str__(self):
        return f"Order {self.order.id} assigned to {self.assigned_to.user.email}"

    def clean(self):
        """Validate assignment"""
        if self.assigned_to == self.assigned_by:
            raise ValidationError("Staff member cannot assign order to themselves")

    def deactivate(self):
        """Deactivate this assignment"""
        self.is_active = False
        self.save()


class OrderNote(models.Model):
    """
    Staff notes and comments on orders.
    Supports internal communication and documentation.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='staff_notes'
    )
    created_by = models.ForeignKey(
        StaffProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name='order_notes'
    )
    note = models.TextField(help_text="Internal staff note about the order")
    is_internal = models.BooleanField(
        default=True,
        help_text="If False, note may be visible to customer"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Order Note"
        verbose_name_plural = "Order Notes"

    def __str__(self):
        return f"Note on Order {self.order.id} by {self.created_by.user.email if self.created_by else 'Unknown'}"


class BulkOrderOperation(models.Model):
    """
    Track bulk operations on orders with progress monitoring
    """
    OPERATION_CHOICES = [
        ('BULK_STATUS_UPDATE', 'Bulk Status Update'),
        ('BULK_ASSIGNMENT', 'Bulk Assignment'),
        ('BULK_NOTE_ADD', 'Bulk Note Addition'),
        ('BULK_LABEL_PRINT', 'Bulk Label Printing'),
        ('BULK_INVOICE_GENERATE', 'Bulk Invoice Generation'),
        ('BULK_WAREHOUSE_DOC', 'Bulk Warehouse Documentation'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    operation_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    staff_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bulk_order_operations'
    )
    operation_type = models.CharField(max_length=30, choices=OPERATION_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')

    # Progress tracking
    total_items = models.PositiveIntegerField(default=0)
    processed_items = models.PositiveIntegerField(default=0)
    failed_items = models.PositiveIntegerField(default=0)

    # Operation data and results
    operation_data = models.JSONField(default=dict, help_text="Input data for the operation")
    results = models.JSONField(default=dict, help_text="Results and output data")
    error_message = models.TextField(blank=True)

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-started_at']
        verbose_name = "Bulk Order Operation"
        verbose_name_plural = "Bulk Order Operations"

    def __str__(self):
        return f"{self.get_operation_type_display()} by {self.staff_user.email}"

    @property
    def progress_percentage(self):
        """Calculate progress percentage"""
        if self.total_items == 0:
            return 0
        return round((self.processed_items / self.total_items) * 100, 2)

    @property
    def duration(self):
        """Calculate operation duration"""
        if not self.completed_at:
            return None
        return self.completed_at - self.started_at

    def mark_completed(self):
        """Mark operation as completed"""
        self.status = 'COMPLETED'
        self.completed_at = timezone.now()
        self.save()

    def mark_failed(self, error_message):
        """Mark operation as failed"""
        self.status = 'FAILED'
        self.error_message = error_message
        self.completed_at = timezone.now()
        self.save()

    def increment_processed(self):
        """Increment processed items count"""
        self.processed_items += 1
        self.save()

    def increment_failed(self):
        """Increment failed items count"""
        self.failed_items += 1
        self.save()


class OrderDocument(models.Model):
    """
    Store generated documents for orders (labels, invoices, warehouse docs)
    """
    DOCUMENT_TYPES = [
        ('SHIPPING_LABEL', 'Shipping Label'),
        ('CUSTOMER_INVOICE', 'Customer Invoice'),
        ('WAREHOUSE_PICKUP', 'Warehouse Pickup Document'),
        ('PACKING_SLIP', 'Packing Slip'),
        ('DELIVERY_NOTE', 'Delivery Note'),
    ]

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='documents'
    )
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES)
    generated_by = models.ForeignKey(
        StaffProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name='generated_documents'
    )
    bulk_operation = models.ForeignKey(
        BulkOrderOperation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_documents'
    )

    # Document content and metadata
    document_data = models.JSONField(default=dict, help_text="Document content and metadata")
    file_path = models.CharField(max_length=500, blank=True, help_text="Path to generated file")

    generated_at = models.DateTimeField(auto_now_add=True)
    is_printed = models.BooleanField(default=False)
    printed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-generated_at']
        verbose_name = "Order Document"
        verbose_name_plural = "Order Documents"

    def __str__(self):
        return f"{self.get_document_type_display()} for Order {self.order.id}"

    def mark_printed(self):
        """Mark document as printed"""
        self.is_printed = True
        self.printed_at = timezone.now()
        self.save()
