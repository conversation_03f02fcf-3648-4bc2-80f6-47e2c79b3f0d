// Attribute Values management route
// Protected route for managing attribute values

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { AttributeValuesPage } from '../../pages/products/AttributeValuesPage'

export const Route = createFileRoute('/products/attribute-values')({
  component: AttributeValuesRoute,
})

function AttributeValuesRoute() {
  return (
    <AuthGuard permission="staff.view_attributevalue">
      <AttributeValuesPage />
    </AuthGuard>
  )
}
