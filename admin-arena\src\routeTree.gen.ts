/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as UnauthorizedRouteImport } from './routes/unauthorized'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as ProductsRouteImport } from './routes/products'
import { Route as OrdersRouteImport } from './routes/orders'
import { Route as MainRouteImport } from './routes/main'
import { Route as LoginRouteImport } from './routes/login'
import { Route as CustomersRouteImport } from './routes/customers'
import { Route as AnalyticsRouteImport } from './routes/analytics'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ProductsIndexRouteImport } from './routes/products/index'
import { Route as ProductsWizardRouteImport } from './routes/products/wizard'
import { Route as ProductsTypesRouteImport } from './routes/products/types'
import { Route as ProductsProductTypeAttributesRouteImport } from './routes/products/product-type-attributes'
import { Route as ProductsNewRouteImport } from './routes/products/new'
import { Route as ProductsCategoriesRouteImport } from './routes/products/categories'
import { Route as ProductsBrandsRouteImport } from './routes/products/brands'
import { Route as ProductsBrandProductTypesRouteImport } from './routes/products/brand-product-types'
import { Route as ProductsAttributesRouteImport } from './routes/products/attributes'
import { Route as ProductsAttributeValuesRouteImport } from './routes/products/attribute-values'
import { Route as ProductsProductIdEditRouteImport } from './routes/products/$productId/edit'

const UnauthorizedRoute = UnauthorizedRouteImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsRoute = ProductsRouteImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersRoute = OrdersRouteImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => rootRouteImport,
} as any)
const MainRoute = MainRouteImport.update({
  id: '/main',
  path: '/main',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomersRoute = CustomersRouteImport.update({
  id: '/customers',
  path: '/customers',
  getParentRoute: () => rootRouteImport,
} as any)
const AnalyticsRoute = AnalyticsRouteImport.update({
  id: '/analytics',
  path: '/analytics',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsIndexRoute = ProductsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsWizardRoute = ProductsWizardRouteImport.update({
  id: '/wizard',
  path: '/wizard',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsTypesRoute = ProductsTypesRouteImport.update({
  id: '/types',
  path: '/types',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsProductTypeAttributesRoute =
  ProductsProductTypeAttributesRouteImport.update({
    id: '/product-type-attributes',
    path: '/product-type-attributes',
    getParentRoute: () => ProductsRoute,
  } as any)
const ProductsNewRoute = ProductsNewRouteImport.update({
  id: '/new',
  path: '/new',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsCategoriesRoute = ProductsCategoriesRouteImport.update({
  id: '/categories',
  path: '/categories',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsBrandsRoute = ProductsBrandsRouteImport.update({
  id: '/brands',
  path: '/brands',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsBrandProductTypesRoute =
  ProductsBrandProductTypesRouteImport.update({
    id: '/brand-product-types',
    path: '/brand-product-types',
    getParentRoute: () => ProductsRoute,
  } as any)
const ProductsAttributesRoute = ProductsAttributesRouteImport.update({
  id: '/attributes',
  path: '/attributes',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsAttributeValuesRoute = ProductsAttributeValuesRouteImport.update({
  id: '/attribute-values',
  path: '/attribute-values',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsProductIdEditRoute = ProductsProductIdEditRouteImport.update({
  id: '/$productId/edit',
  path: '/$productId/edit',
  getParentRoute: () => ProductsRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/analytics': typeof AnalyticsRoute
  '/customers': typeof CustomersRoute
  '/login': typeof LoginRoute
  '/main': typeof MainRoute
  '/orders': typeof OrdersRoute
  '/products': typeof ProductsRouteWithChildren
  '/settings': typeof SettingsRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/products/attribute-values': typeof ProductsAttributeValuesRoute
  '/products/attributes': typeof ProductsAttributesRoute
  '/products/brand-product-types': typeof ProductsBrandProductTypesRoute
  '/products/brands': typeof ProductsBrandsRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/new': typeof ProductsNewRoute
  '/products/product-type-attributes': typeof ProductsProductTypeAttributesRoute
  '/products/types': typeof ProductsTypesRoute
  '/products/wizard': typeof ProductsWizardRoute
  '/products/': typeof ProductsIndexRoute
  '/products/$productId/edit': typeof ProductsProductIdEditRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/analytics': typeof AnalyticsRoute
  '/customers': typeof CustomersRoute
  '/login': typeof LoginRoute
  '/main': typeof MainRoute
  '/orders': typeof OrdersRoute
  '/settings': typeof SettingsRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/products/attribute-values': typeof ProductsAttributeValuesRoute
  '/products/attributes': typeof ProductsAttributesRoute
  '/products/brand-product-types': typeof ProductsBrandProductTypesRoute
  '/products/brands': typeof ProductsBrandsRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/new': typeof ProductsNewRoute
  '/products/product-type-attributes': typeof ProductsProductTypeAttributesRoute
  '/products/types': typeof ProductsTypesRoute
  '/products/wizard': typeof ProductsWizardRoute
  '/products': typeof ProductsIndexRoute
  '/products/$productId/edit': typeof ProductsProductIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/analytics': typeof AnalyticsRoute
  '/customers': typeof CustomersRoute
  '/login': typeof LoginRoute
  '/main': typeof MainRoute
  '/orders': typeof OrdersRoute
  '/products': typeof ProductsRouteWithChildren
  '/settings': typeof SettingsRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/products/attribute-values': typeof ProductsAttributeValuesRoute
  '/products/attributes': typeof ProductsAttributesRoute
  '/products/brand-product-types': typeof ProductsBrandProductTypesRoute
  '/products/brands': typeof ProductsBrandsRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/new': typeof ProductsNewRoute
  '/products/product-type-attributes': typeof ProductsProductTypeAttributesRoute
  '/products/types': typeof ProductsTypesRoute
  '/products/wizard': typeof ProductsWizardRoute
  '/products/': typeof ProductsIndexRoute
  '/products/$productId/edit': typeof ProductsProductIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/analytics'
    | '/customers'
    | '/login'
    | '/main'
    | '/orders'
    | '/products'
    | '/settings'
    | '/unauthorized'
    | '/products/attribute-values'
    | '/products/attributes'
    | '/products/brand-product-types'
    | '/products/brands'
    | '/products/categories'
    | '/products/new'
    | '/products/product-type-attributes'
    | '/products/types'
    | '/products/wizard'
    | '/products/'
    | '/products/$productId/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/analytics'
    | '/customers'
    | '/login'
    | '/main'
    | '/orders'
    | '/settings'
    | '/unauthorized'
    | '/products/attribute-values'
    | '/products/attributes'
    | '/products/brand-product-types'
    | '/products/brands'
    | '/products/categories'
    | '/products/new'
    | '/products/product-type-attributes'
    | '/products/types'
    | '/products/wizard'
    | '/products'
    | '/products/$productId/edit'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/analytics'
    | '/customers'
    | '/login'
    | '/main'
    | '/orders'
    | '/products'
    | '/settings'
    | '/unauthorized'
    | '/products/attribute-values'
    | '/products/attributes'
    | '/products/brand-product-types'
    | '/products/brands'
    | '/products/categories'
    | '/products/new'
    | '/products/product-type-attributes'
    | '/products/types'
    | '/products/wizard'
    | '/products/'
    | '/products/$productId/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  AnalyticsRoute: typeof AnalyticsRoute
  CustomersRoute: typeof CustomersRoute
  LoginRoute: typeof LoginRoute
  MainRoute: typeof MainRoute
  OrdersRoute: typeof OrdersRoute
  ProductsRoute: typeof ProductsRouteWithChildren
  SettingsRoute: typeof SettingsRoute
  UnauthorizedRoute: typeof UnauthorizedRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/unauthorized': {
      id: '/unauthorized'
      path: '/unauthorized'
      fullPath: '/unauthorized'
      preLoaderRoute: typeof UnauthorizedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products': {
      id: '/products'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders': {
      id: '/orders'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof OrdersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/main': {
      id: '/main'
      path: '/main'
      fullPath: '/main'
      preLoaderRoute: typeof MainRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/customers': {
      id: '/customers'
      path: '/customers'
      fullPath: '/customers'
      preLoaderRoute: typeof CustomersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/analytics': {
      id: '/analytics'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof AnalyticsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products/': {
      id: '/products/'
      path: '/'
      fullPath: '/products/'
      preLoaderRoute: typeof ProductsIndexRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/wizard': {
      id: '/products/wizard'
      path: '/wizard'
      fullPath: '/products/wizard'
      preLoaderRoute: typeof ProductsWizardRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/types': {
      id: '/products/types'
      path: '/types'
      fullPath: '/products/types'
      preLoaderRoute: typeof ProductsTypesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/product-type-attributes': {
      id: '/products/product-type-attributes'
      path: '/product-type-attributes'
      fullPath: '/products/product-type-attributes'
      preLoaderRoute: typeof ProductsProductTypeAttributesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/new': {
      id: '/products/new'
      path: '/new'
      fullPath: '/products/new'
      preLoaderRoute: typeof ProductsNewRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/categories': {
      id: '/products/categories'
      path: '/categories'
      fullPath: '/products/categories'
      preLoaderRoute: typeof ProductsCategoriesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/brands': {
      id: '/products/brands'
      path: '/brands'
      fullPath: '/products/brands'
      preLoaderRoute: typeof ProductsBrandsRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/brand-product-types': {
      id: '/products/brand-product-types'
      path: '/brand-product-types'
      fullPath: '/products/brand-product-types'
      preLoaderRoute: typeof ProductsBrandProductTypesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/attributes': {
      id: '/products/attributes'
      path: '/attributes'
      fullPath: '/products/attributes'
      preLoaderRoute: typeof ProductsAttributesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/attribute-values': {
      id: '/products/attribute-values'
      path: '/attribute-values'
      fullPath: '/products/attribute-values'
      preLoaderRoute: typeof ProductsAttributeValuesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/$productId/edit': {
      id: '/products/$productId/edit'
      path: '/$productId/edit'
      fullPath: '/products/$productId/edit'
      preLoaderRoute: typeof ProductsProductIdEditRouteImport
      parentRoute: typeof ProductsRoute
    }
  }
}

interface ProductsRouteChildren {
  ProductsAttributeValuesRoute: typeof ProductsAttributeValuesRoute
  ProductsAttributesRoute: typeof ProductsAttributesRoute
  ProductsBrandProductTypesRoute: typeof ProductsBrandProductTypesRoute
  ProductsBrandsRoute: typeof ProductsBrandsRoute
  ProductsCategoriesRoute: typeof ProductsCategoriesRoute
  ProductsNewRoute: typeof ProductsNewRoute
  ProductsProductTypeAttributesRoute: typeof ProductsProductTypeAttributesRoute
  ProductsTypesRoute: typeof ProductsTypesRoute
  ProductsWizardRoute: typeof ProductsWizardRoute
  ProductsIndexRoute: typeof ProductsIndexRoute
  ProductsProductIdEditRoute: typeof ProductsProductIdEditRoute
}

const ProductsRouteChildren: ProductsRouteChildren = {
  ProductsAttributeValuesRoute: ProductsAttributeValuesRoute,
  ProductsAttributesRoute: ProductsAttributesRoute,
  ProductsBrandProductTypesRoute: ProductsBrandProductTypesRoute,
  ProductsBrandsRoute: ProductsBrandsRoute,
  ProductsCategoriesRoute: ProductsCategoriesRoute,
  ProductsNewRoute: ProductsNewRoute,
  ProductsProductTypeAttributesRoute: ProductsProductTypeAttributesRoute,
  ProductsTypesRoute: ProductsTypesRoute,
  ProductsWizardRoute: ProductsWizardRoute,
  ProductsIndexRoute: ProductsIndexRoute,
  ProductsProductIdEditRoute: ProductsProductIdEditRoute,
}

const ProductsRouteWithChildren = ProductsRoute._addFileChildren(
  ProductsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  AnalyticsRoute: AnalyticsRoute,
  CustomersRoute: CustomersRoute,
  LoginRoute: LoginRoute,
  MainRoute: MainRoute,
  OrdersRoute: OrdersRoute,
  ProductsRoute: ProductsRouteWithChildren,
  SettingsRoute: SettingsRoute,
  UnauthorizedRoute: UnauthorizedRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
