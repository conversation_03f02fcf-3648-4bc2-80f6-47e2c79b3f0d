@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.modal_content {
  background-color: #fff;
  padding: 2rem;
  border-radius: $border-radius-2;
  max-width: 500px;
  width: 100%;

  h3 {
    font-size: $font-size-4;
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
  }

  form {
    // @include flexbox(flex-start, flex-start, column);

    div {
      margin-bottom: 1rem;

      label {
        font-weight: bold;
        color: $primary-dark-text-color;
        display: block;
        margin-bottom: 0.5rem;
      }

      input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid $primary-lighter-text-color;
        border-radius: 3px;

        &:focus {
          outline: 2px solid $lighten-blue;
          border: none;
        }

        &:read-only {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }
      }

      // Style for react-country-state-city selectors
      :global(.react-country-state-city) {
        width: 100%;
        
        .css-1s2u09g-control {
          border: 1px solid $primary-lighter-text-color;
          border-radius: 3px;
          min-height: 38px;
        }
        
        .css-1pahdxg-control {
          border-color: $lighten-blue;
          box-shadow: 0 0 0 1px $lighten-blue;
        }
      }

      select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid $primary-lighter-text-color;
        border-radius: 3px;
        background-color: #fff;
        font-size: 1rem;

        &:focus {
          outline: 2px solid $lighten-blue;
          border: none;
        }
      }

      p {
        color: $error-red;
        font-size: 14px;
        margin-top: 0.5rem;
        text-align: center;
      }
    }
  }
}

.modal_buttons {
  @include flexbox(space-between, center);
  margin-top: 1rem;

  button {
    @include btn($primary-blue, #fff);
    padding: 0.5rem 1rem;
    border: 1px solid $lighten-blue;
    transition: all 0.3s ease;

    &:hover {
      border: 1px solid $primary-dark-blue;
      color: $primary-dark-blue;
    }
  }
}
