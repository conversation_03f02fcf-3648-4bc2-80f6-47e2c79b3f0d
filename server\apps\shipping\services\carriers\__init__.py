from .base import BaseCarrier
from .posten_bring import PostenBringCarrier


def get_carrier_instance(carrier_config):
    """Factory function to get carrier instance based on configuration"""
    
    carrier_map = {
        'posten_bring': PostenBringCarrier,
        'posten': PostenBringCarrier,  # Alias
        'bring': PostenBringCarrier,   # Alias
    }
    
    carrier_class = carrier_map.get(carrier_config.code.lower())
    
    if not carrier_class:
        raise ValueError(f"Unknown carrier code: {carrier_config.code}")
    
    return carrier_class(carrier_config)


__all__ = ['BaseCarrier', 'PostenBringCarrier', 'get_carrier_instance']
