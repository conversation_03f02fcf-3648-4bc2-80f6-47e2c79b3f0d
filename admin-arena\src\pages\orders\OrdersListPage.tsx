// Orders list page with filtering and bulk operations
// Main interface for order management

import React, { useState, useEffect } from 'react'
import { FiDownload, FiSearch, FiRefreshCw } from 'react-icons/fi'
import { DateTime } from 'luxon'
import { useOrders, useBulkUpdateOrderStatus, useExportOrders } from '../../hooks/use-orders'
import { usePageState } from '../../stores/ui-store'
import { DataTable } from '../../components/ui/DataTable'
import { Button } from '../../components/ui/Button'
import { Input } from '../../components/ui/Input'
import type { OrderFilters } from '../../types/api-types'
import styles from './OrdersListPage.module.scss'

const ORDER_STATUS_OPTIONS = [
  { value: '', label: 'All Statuses' },
  { value: 'PENDING', label: 'Pending' },
  { value: 'PROCESSING', label: 'Processing' },
  { value: 'SHIPPED', label: 'Shipped' },
  { value: 'DELIVERED', label: 'Delivered' },
  { value: 'CANCELLED', label: 'Cancelled' },
]

export const OrdersListPage: React.FC = () => {
  const { setPageTitle, setBreadcrumbs } = usePageState()
  const [filters, setFilters] = useState<OrderFilters>({
    page: 1,
    page_size: 20,
    ordering: '-created_at'
  })
  const [selectedOrders, setSelectedOrders] = useState<Set<number>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')

  const { data: ordersData, isLoading, refetch } = useOrders(filters)
  const bulkUpdateMutation = useBulkUpdateOrderStatus()
  const exportMutation = useExportOrders()

  useEffect(() => {
    setPageTitle('Orders')
    setBreadcrumbs([
      { label: 'Dashboard', path: '/' },
      { label: 'Orders' }
    ])
  }, [setPageTitle, setBreadcrumbs])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setFilters(prev => ({
      ...prev,
      search: value || undefined,
      page: 1
    }))
  }

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: status || undefined,
      page: 1
    }))
  }

  const handleSort = (key: string) => {
    const isCurrentSort = filters.ordering?.replace('-', '') === key
    const isDesc = filters.ordering?.startsWith('-')

    setFilters(prev => ({
      ...prev,
      ordering: isCurrentSort && !isDesc ? `-${key}` : key
    }))
  }

  const handleRowSelect = (rowId: string | number) => {
    const orderId = typeof rowId === 'string' ? parseInt(rowId, 10) : rowId
    setSelectedOrders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(orderId)) {
        newSet.delete(orderId)
      } else {
        newSet.add(orderId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (selectedOrders.size === ordersData?.results.length) {
      setSelectedOrders(new Set())
    } else {
      setSelectedOrders(new Set(ordersData?.results.map(order => order.id) || []))
    }
  }

  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedOrders.size === 0) return

    try {
      await bulkUpdateMutation.mutateAsync({
        orderIds: Array.from(selectedOrders),
        status
      })
      setSelectedOrders(new Set())
    } catch (error) {
      console.error('Bulk update failed:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportMutation.mutateAsync(filters)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      PENDING: styles.statusPending,
      PROCESSING: styles.statusProcessing,
      SHIPPED: styles.statusShipped,
      DELIVERED: styles.statusDelivered,
      CANCELLED: styles.statusCancelled,
    }

    return (
      <span className={`${styles.statusBadge} ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status}
      </span>
    )
  }

  console.log(ordersData)

  const columns = [
    {
      key: 'id',
      header: 'Order #',
      sortable: true,
      render: (value: string) => (
        <span className={styles.orderNumber}>{value}</span>
      )
    },
    {
      key: 'customer_name',
      header: 'Customer',
      // sortable: true,
      // align: 'right' as const,
      // render: (amount: number) => formatCurrency(amount)
    },
    // {
    //   key: 'customer',
    //   header: 'Customer',
    //   render: (customer: { first_name?: string; last_name?: string; email?: string } | null | undefined) => {
    //     if (!customer) {
    //       return <span className={styles.customerInfo}>No customer data</span>
    //     }

    //     return (
    //       <div className={styles.customerInfo}>
    //         <span className={styles.customerName}>
    //           {customer.first_name || 'N/A'} {customer.last_name || ''}
    //         </span>
    //         <span className={styles.customerEmail}>{customer.email || 'No email'}</span>
    //       </div>
    //     )
    //   }
    // },
    {
      key: 'payment_status',
      header: 'Payment Status',
      sortable: true,
      render: (status: string) => getStatusBadge(status)
    },
    {
      key: 'total',
      header: 'Total',
      sortable: true,
      // align: 'right' as const,
      render: (amount: number) => formatCurrency(amount)
    },
    {
      key: 'order_status',
      header: 'Order Status',
      sortable: true,
    },
    {
      key: 'placed_at',
      header: 'Created',
      sortable: true,
      render: (value: string) =>
        <span>
          {DateTime.fromISO(value).toFormat('dd LLL yyyy h:mm a')}
        </span>
    }
  ]

  const bulkActions = (
    <div className={styles.bulkActions}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleBulkStatusUpdate('PROCESSING')}
        disabled={bulkUpdateMutation.isPending}
      >
        Mark Processing
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleBulkStatusUpdate('SHIPPED')}
        disabled={bulkUpdateMutation.isPending}
      >
        Mark Shipped
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleBulkStatusUpdate('CANCELLED')}
        disabled={bulkUpdateMutation.isPending}
      >
        Cancel Orders
      </Button>
    </div>
  )

  return (
    <div className={styles.ordersPage}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <h1 className={styles.title}>Orders</h1>
          <p className={styles.subtitle}>
            Manage and track all customer orders
          </p>
        </div>

        <div className={styles.headerActions}>
          <Button
            variant="outline"
            leftIcon={<FiDownload />}
            onClick={handleExport}
            loading={exportMutation.isPending}
          >
            Export
          </Button>
          <Button
            variant="outline"
            leftIcon={<FiRefreshCw />}
            onClick={() => refetch()}
          >
            Refresh
          </Button>
        </div>
      </div>

      <div className={styles.filters}>
        <div className={styles.searchFilter}>
          <Input
            placeholder="Search orders..."
            leftIcon={<FiSearch />}
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        <div className={styles.statusFilter}>
          <select
            value={filters.status || ''}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className={styles.statusSelect}
          >
            {ORDER_STATUS_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <DataTable
        data={ordersData?.results || []}
        columns={columns}
        loading={isLoading}
        sortBy={filters.ordering?.replace('-', '')}
        sortDirection={filters.ordering?.startsWith('-') ? 'desc' : 'asc'}
        onSort={handleSort}
        selectedRows={selectedOrders}
        onRowSelect={handleRowSelect}
        onSelectAll={handleSelectAll}
        bulkActions={bulkActions}
        emptyMessage="No orders found"
        stickyHeader
        maxHeight="calc(100vh - 300px)"
      />
    </div>
  )
}

export default OrdersListPage
