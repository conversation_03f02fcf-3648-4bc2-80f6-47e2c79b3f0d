$ python manage.py runserver
Watching for file changes with StatReloader
INFO Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).
September 07, 2025 - 09:29:58
Django version 5.2.5, using settings 'pc_hardware.settings.dev'
Starting development server at http://127.0.0.1:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Internal Server Error: /admin/shipping/box/add/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'efficiency_ratio_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 290, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 330, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\options.py", line 685, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: Box has no field named 'efficiency_ratio_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 81, in super
    return mark_safe(self.render(self.context))
                     ~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 210, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 173, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\helpers.py", line 275, in contents
    f, attr, value = lookup_field(field, obj, model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 299, in lookup_field
    value = attr(obj)
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\admin.py", line 54, in efficiency_ratio_display
    ratio = obj.get_efficiency_ratio()
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\models\boxes.py", line 70, in get_efficiency_ratio
    volume = self.volume or self.calculate_volume()
                            ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\models\boxes.py", line 65, in calculate_volume
    return self.internal_length * self.internal_width * self.internal_height
           ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~
TypeError: unsupported operand type(s) for *: 'NoneType' and 'NoneType'
ERROR Internal Server Error: /admin/shipping/box/add/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'efficiency_ratio_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 290, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 330, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\options.py", line 685, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: Box has no field named 'efficiency_ratio_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 81, in super
    return mark_safe(self.render(self.context))
                     ~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\loader_tags.py", line 210, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 173, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\helpers.py", line 275, in contents
    f, attr, value = lookup_field(field, obj, model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\contrib\admin\utils.py", line 299, in lookup_field
    value = attr(obj)
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\admin.py", line 54, in efficiency_ratio_display
    ratio = obj.get_efficiency_ratio()
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\models\boxes.py", line 70, in get_efficiency_ratio
    volume = self.volume or self.calculate_volume()
                            ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\shipping\models\boxes.py", line 65, in calculate_volume
    return self.internal_length * self.internal_width * self.internal_height
           ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~
TypeError: unsupported operand type(s) for *: 'NoneType' and 'NoneType'