@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.empty_wishlist {
  @include flexbox(center, center, column);
  margin: 3rem auto 0 auto;
  text-align: center;
  max-width: 500px;
  padding: $padding-5;

  .icon_section {
    margin-bottom: $padding-4;

    svg {
      font-size: 64px;
      color: $primary-lighter-text-color;
    }

    @include mobile {
      svg {
        font-size: 48px;
      }
    }
  }

  .content_section {
    margin-bottom: $padding-5;

    .heading {
      font-size: $font-size-5;
      color: $primary-dark-text-color;
      font-weight: map-get($font-weight, 'bold');
      margin-bottom: $padding-3;
      line-height: 1.3;

      @include mobile {
        font-size: $font-size-4;
      }
    }

    .description {
      font-size: $font-size-3;
      color: $primary-lighter-text-color;
      line-height: 1.5;
      margin: 0;

      @include mobile {
        font-size: $font-size-3;
      }
    }
  }

  .action_section {
    .action_button {
      @include btn(white, $primary-blue);
      text-decoration: none;
      padding: $padding-3 $padding-5;
      border-radius: $border-radius-2;
      font-size: $font-size-3;
      font-weight: map-get($font-weight, 'medium');
      transition: background-color 0.3s ease, transform 0.2s ease;
      display: inline-block;

      &:hover {
        background-color: $lighten-blue;
        transform: translateY(-1px);
        text-decoration: none;
      }

      &:focus {
        outline: 2px solid $primary-blue;
        outline-offset: 2px;
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}