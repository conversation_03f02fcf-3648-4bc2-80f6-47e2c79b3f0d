import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'
import { queryKeys } from '../../services/query-keys'


/**
 * Hook for fetching brands associated with a product type
 */
export const useProductTypeBrands = (productTypeId: number) => {
  return useQuery({
    queryKey: queryKeys.products.productTypeBrands(productTypeId),
    queryFn: () => ProductService.getProductTypeBrands(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}


/**
 * Hook for fetching all brand-product type associations
 */
export const useBrandProductTypeAssociations = () => {
  return useQuery({
    queryKey: queryKeys.products.brandProductTypes(),
    queryFn: ProductService.getBrandProductTypeAssociations,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}


/**
 * Hook for associating multiple brands with a product type
 */
export const useAssociateBrandsWithProductType = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, brandIds }: { productTypeId: number; brandIds: number[] }) =>
      ProductService.associateBrandsWithProductType(productTypeId, brandIds),
    onSuccess: (_, variables) => {
      showSuccess('Brands Associated', 'Brands have been associated with the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeBrands(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate brands with product type.')
    },
  })
}


/**
 * Hook for bulk managing brands for a product type (replace all associations)
 */
export const useManageProductTypeBrands = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: async ({
      productTypeId,
      currentBrandIds,
      newBrandIds
    }: {
      productTypeId: number
      currentBrandIds: number[]
      newBrandIds: number[]
    }) => {
      // First remove all current brands
      if (currentBrandIds.length > 0) {
        await ProductService.removeBrandsFromProductType(productTypeId, currentBrandIds)
      }

      // Then add the new brands
      if (newBrandIds.length > 0) {
        await ProductService.associateBrandsWithProductType(productTypeId, newBrandIds)
      }
    },
    onSuccess: (_, variables) => {
      showSuccess('Brands Updated', 'Product type brand associations have been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeBrands(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type brand associations.')
    },
  })
}

