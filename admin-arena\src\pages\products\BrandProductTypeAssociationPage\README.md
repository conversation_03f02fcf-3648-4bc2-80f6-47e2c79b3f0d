# Brand-Product Type Association API Integration

This document explains the new API endpoints and React hooks for managing brand-product type associations.

## New API Endpoints

### 1. Associate Multiple Brands with a Product Type
```
POST http://127.0.0.1:8000/api/staff/products/product-types/{id}/associate_brands/
```

**Request Body:**
```json
{
  "brand_ids": [1, 2, 3]
}
```

**Description:** Associates multiple brands with a specific product type.

### 2. Remove Multiple Brands from a Product Type
```
POST http://127.0.0.1:8000/api/staff/products/product-types/{id}/remove_brands/
```

**Request Body:**
```json
{
  "brand_ids": [1, 2]
}
```

**Description:** Removes multiple brands from a specific product type.

### 3. Get Brands by Product Type
```
GET http://127.0.0.1:8000/api/staff/products/product-types/{id}/brands/
```

**Response:**
```json
[
  {
    "id": 2,
    "brand": 1,
    "brand_title": "Dell",
    "product_type": 1,
    "product_type_title": "hard-drives"
  }
]
```

**Description:** Retrieves all brands associated with a specific product type.

## New React Hooks

### 1. useProductTypeBrands
Fetches brands associated with a specific product type.

```typescript
import { useProductTypeBrands } from '../../../hooks/products-hooks/use-product-type-brand-associations'

const { data: brands, isLoading } = useProductTypeBrands(productTypeId)
```

### 2. useAssociateBrandsWithProductType
Associates multiple brands with a product type.

```typescript
import { useAssociateBrandsWithProductType } from '../../../hooks/products-hooks/use-product-type-brand-associations'

const associateMutation = useAssociateBrandsWithProductType()

// Usage
await associateMutation.mutateAsync({
  productTypeId: 1,
  brandIds: [1, 2, 3]
})
```

### 3. useRemoveBrandsFromProductType
Removes multiple brands from a product type.

```typescript
import { useRemoveBrandsFromProductType } from '../../../hooks/products-hooks/use-product-type-brand-associations'

const removeMutation = useRemoveBrandsFromProductType()

// Usage
await removeMutation.mutateAsync({
  productTypeId: 1,
  brandIds: [1, 2]
})
```

### 4. useManageProductTypeBrands
Bulk management hook that replaces all current brand associations with new ones.

```typescript
import { useManageProductTypeBrands } from '../../../hooks/products-hooks/use-product-type-brand-associations'

const manageMutation = useManageProductTypeBrands()

// Usage
await manageMutation.mutateAsync({
  productTypeId: 1,
  currentBrandIds: [1, 2, 3], // Current associations to remove
  newBrandIds: [4, 5, 6]      // New associations to add
})
```

## Service Methods

The following methods have been added to `ProductService`:

### associateBrandsWithProductType
```typescript
ProductService.associateBrandsWithProductType(productTypeId: number, brandIds: number[]): Promise<void>
```

### removeBrandsFromProductType
```typescript
ProductService.removeBrandsFromProductType(productTypeId: number, brandIds: number[]): Promise<void>
```

### getProductTypeBrands
```typescript
ProductService.getProductTypeBrands(productTypeId: number): Promise<BrandProductType[]>
```

## Updated Components

### BrandProductTypeAssociationPage
The main association page has been updated to use the new API endpoints:

- **Create associations:** Uses `useAssociateBrandsWithProductType`
- **Edit associations:** Uses `useManageProductTypeBrands` for bulk replacement
- **Display current brands:** Shows current associations in edit modal

### ProductTypeBrandManager (Demo Component)
A demonstration component showing all three operations:

- **Associate tab:** Add new brands to a product type
- **Remove tab:** Remove existing brands from a product type  
- **Manage tab:** Replace all current associations with new ones

## Query Key Updates

New query key added to support caching:

```typescript
productTypeBrands: (productTypeId: number) => [...queryKeys.products.all, 'product-type-brands', productTypeId]
```

## Type Definitions

New TypeScript interfaces:

```typescript
export interface ProductTypeBrandAssociate {
  brand_ids: number[]
}

export interface ProductTypeBrandRemove {
  brand_ids: number[]
}
```

## Usage Examples

### Basic Association
```typescript
const associateMutation = useAssociateBrandsWithProductType()

const handleAssociate = async () => {
  await associateMutation.mutateAsync({
    productTypeId: 1,
    brandIds: [1, 2, 3]
  })
}
```

### Bulk Management
```typescript
const manageMutation = useManageProductTypeBrands()

const handleBulkUpdate = async () => {
  const currentBrands = [1, 2, 3]
  const newBrands = [4, 5, 6]
  
  await manageMutation.mutateAsync({
    productTypeId: 1,
    currentBrandIds: currentBrands,
    newBrandIds: newBrands
  })
}
```

## Error Handling

All hooks include built-in error handling with toast notifications:
- Success messages for successful operations
- Error messages for failed operations
- Automatic query invalidation on success

## Cache Management

The hooks automatically invalidate related queries on successful mutations:
- `productTypeBrands(productTypeId)` - Specific product type brands
- `brandProductTypes()` - All brand-product type associations
- `types()` - Product types list
- `brands()` - Brands list
