from django.db import models
from apps.customers.models import Customer
from apps.products.models import Product


class Wishlist(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name="wishlists")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name="wishlisted_by")
    added_at = models.DateTimeField(auto_now_add=True)

    # priority = models.IntegerField(default=0)
    # note = models.TextField(blank=True, null=True)
    # is_favorite = models.BooleanField(default=False)

    class Meta:
        unique_together = ("customer", "product")
        ordering = ["-added_at"]

    def __str__(self):
        return f"{self.customer.first_name}'s Wishlist"
