from django.contrib import admin
from .models import Customer, Address


class AddressInline(admin.StackedInline):
    model = Address
    extra = 0


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    inlines = [
        AddressInline
    ]
    list_display = ['id', 'first_name', 'last_name', 'user', 'get_phone_number', 'birth_date']
    readonly_fields = ['user', 'birth_date_change_count']
    list_select_related = ['user']

    # ordering = ['first_name', 'last_name']

    @admin.display(description='Phone Number')
    def get_phone_number(self, obj):
        return obj.user.phone_number if obj.user else None


@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    list_display = ['id', 'full_name', 'address_line_1', 'address_line_2',
                    'postal_code', 'city_or_village', 'customer']
    readonly_fields = ['customer']
    list_filter = ['customer']
