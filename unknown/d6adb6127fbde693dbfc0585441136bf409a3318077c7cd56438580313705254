// Pure cookie-based authentication service for staff
// All tokens are managed server-side via HTTP-only cookies
// Frontend never handles tokens directly

import { apiClient } from './api-client'
import { AuthStorage } from '../utils/auth-storage'
import type {
  LoginCredentials,
  LoginResponse,
  CurrentUserResponse,
  StaffUser
} from '../types/api-types'

export class AuthService {
  /**
   * Authenticate staff user with username and password
   * Server sets HTTP-only cookies automatically
   * Uses unified authentication from core app
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Use unified login endpoint from core app - server sets HTTP-only cookies
      const response = await apiClient.post<{
        refresh: string
        access: string
        user_type: string
        message: string
      }>('/api/auth/login/', credentials)

      // The unified endpoint returns tokens and user_type, not a success flag
      // Tokens are stored in HTTP-only cookies automatically by the server
      // We need to get the user details from the staff authorization endpoint

      // Get user details from staff authorization endpoint
      const userResponse = await apiClient.get<{
        success: boolean
        user: StaffUser
      }>('/api/staff/auth/user/')

      if (!userResponse.data.success) {
        throw new Error('Failed to fetch user information after login')
      }

      // Return user data (tokens are in HTTP-only cookies)
      return {
        access: response.data.access, // For compatibility, but not used in cookie-based auth
        refresh: response.data.refresh, // For compatibility, but not used in cookie-based auth
        user: userResponse.data.user
      }
    } catch (error: unknown) {
      // Handle specific login errors
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 401) {
          throw new Error('Invalid email or password')
        } else if (axiosError.response?.status === 403) {
          throw new Error('Account is not authorized for admin access')
        } else if (axiosError.response?.status === 429) {
          throw new Error('Too many login attempts. Please try again later')
        }
      }
      throw new Error('Login failed. Please try again')
    }
  }

  /**
   * Get current authenticated user with permissions and groups
   * Authentication is handled via HTTP-only cookies
   * Uses staff-specific authorization endpoint for user details
   */
  static async getCurrentUser(): Promise<CurrentUserResponse> {
    try {
      // Use staff-specific user endpoint - cookies sent automatically
      const response = await apiClient.get<{
        success: boolean
        user: StaffUser
      }>('/api/staff/auth/user/')

      if (!response.data.success) {
        throw new Error('Failed to fetch user information')
      }

      return {
        user: response.data.user,
        permissions: response.data.user.permissions || [],
        groups: response.data.user.groups || []
      }
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 401) {
          throw new Error('Authentication required')
        }
      }
      throw new Error('Failed to fetch user information')
    }
  }

  /**
   * Check if user has specific permission
   * Uses staff-specific authorization endpoint
   */
  static async checkPermission(permission: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ has_permission: boolean }>('/api/staff/auth/check-permission/', {
        permission
      })
      return response.data.has_permission
    } catch (error: unknown) {
      console.error('Permission check failed:', error)
      return false
    }
  }

  /**
   * Get all user permissions
   * Uses staff-specific authorization endpoint
   */
  static async getUserPermissions(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ permissions: string[] }>('/api/staff/auth/permissions/')
      return response.data.permissions
    } catch (error: unknown) {
      console.error('Failed to get user permissions:', error)
      return []
    }
  }

  /**
   * Get all user groups
   * Uses staff-specific authorization endpoint
   */
  static async getUserGroups(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ groups: string[] }>('/api/staff/auth/groups/')
      return response.data.groups
    } catch (error: unknown) {
      console.error('Failed to get user groups:', error)
      return []
    }
  }

  /**
   * Refresh access token - handled automatically by server via HTTP-only cookies
   * This method is kept for compatibility but tokens are refreshed server-side
   * Uses unified token refresh from core app
   */
  static async refreshToken(): Promise<string> {
    try {
      // Server refreshes HTTP-only cookies automatically using unified endpoint
      const response = await apiClient.post<{
        success: boolean
        message: string
        error?: string
      }>('/api/auth/token/refresh/')

      if (!response.data.success) {
        throw new Error(response.data.error || 'Token refresh failed')
      }

      // Server has updated HTTP-only cookies automatically
      return 'refreshed' // Placeholder since we can't access the actual token
    } catch (error: unknown) {
      // If refresh fails, clear any legacy cookies
      AuthStorage.clearTokens()

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 401) {
          throw new Error('Session expired. Please login again')
        }
      }
      throw new Error('Failed to refresh authentication')
    }
  }

  /**
   * Logout user - server clears HTTP-only cookies automatically
   * Uses unified logout from core app
   */
  static async logout(): Promise<void> {
    try {
      // Server handles token blacklisting and cookie clearing using unified endpoint
      await apiClient.post('/api/auth/logout/')
    } catch (error: unknown) {
      // Even if server logout fails, clear any legacy cookies
      console.warn('Server logout failed, clearing legacy cookies:', error)
    } finally {
      // Clear any legacy cookies that might exist
      AuthStorage.clearTokens()
    }
  }

  /**
   * Check if user is currently authenticated
   * In pure cookie-based auth, this is determined by API responses
   */
  static isAuthenticated(): boolean {
    // Cannot determine from frontend with HTTP-only cookies
    // Authentication status is determined by API responses
    return true // Will be overridden by actual API calls
  }

  /**
   * Update user profile
   * Uses staff-specific authorization endpoint
   */
  static async updateProfile(data: Partial<StaffUser>): Promise<StaffUser> {
    try {
      const response = await apiClient.patch<StaffUser>('/api/staff/auth/profile/', data)
      return response.data
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 400) {
          throw new Error('Invalid profile data')
        }
      }
      throw new Error('Failed to update profile')
    }
  }

  /**
   * Change user password
   * Uses staff-specific authorization endpoint
   */
  static async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/change-password/', data)
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 400) {
          throw new Error('Invalid password data')
        }
      }
      throw new Error('Failed to change password')
    }
  }

  /**
   * Request password reset
   * Uses unified authentication endpoint from core app
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/api/auth/password/reset/request/', { email })
    } catch {
      throw new Error('Failed to request password reset')
    }
  }

  /**
   * Verify password reset token
   * Uses unified authentication endpoint from core app
   */
  static async verifyPasswordResetToken(token: string): Promise<boolean> {
    try {
      await apiClient.post('/api/auth/password/reset/verify/', { token })
      return true
    } catch {
      return false
    }
  }

  /**
   * Confirm password reset
   * Uses unified authentication endpoint from core app
   */
  static async confirmPasswordReset(data: {
    token: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/auth/password/reset/confirm/', data)
    } catch {
      throw new Error('Failed to confirm password reset')
    }
  }
}
