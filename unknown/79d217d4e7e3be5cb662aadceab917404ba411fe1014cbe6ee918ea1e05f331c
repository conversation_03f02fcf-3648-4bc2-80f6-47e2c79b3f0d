from django.contrib.auth.password_validation import validate_password
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from phonenumber_field.phonenumber import to_python
# from django.core.exceptions import ValidationError as DjangoValidationError
from .models import BlockedEmail, BlockedPhoneNumber
from phonenumber_field.serializerfields import PhoneNumberField
from django.contrib.auth import authenticate
import phonenumbers

from rest_framework import serializers
from rest_framework.serializers import ModelSerializer
from django.contrib.auth import get_user_model

User = get_user_model()


# class ConsolidatedUserSerializer(serializers.ModelSerializer):
#     email = serializers.EmailField(required=False)
#     phone_number = PhoneNumberField(required=False)
#
#     class Meta:
#         model = User
#         fields = ['id', 'email', 'phone_number', 'is_phone_verified', 'is_email_verified']
#         extra_kwargs = {
#             'email': {'required': False, 'allow_null': True},
#             'phone_number': {'required': False, 'allow_null': True},
#         }
#
#     def validate(self, attrs):
#         if not attrs.get('email') and not attrs.get('phone_number'):
#             raise serializers.ValidationError("Either email or phone number must be provided.")
#         return attrs
#
#     def create(self, validated_data):
#         return User.objects.create_user(**validated_data)


class ConsolidatedUserSerializer(ModelSerializer):
    phone_number = PhoneNumberField(required=False)
    email = serializers.EmailField(required=False)

    class Meta:
        model = User
        fields = ['id', 'email', 'phone_number', 'is_phone_verified', 'is_email_verified']

    def validate(self, attrs):
        email = attrs.get('email')
        phone_number = attrs.get('phone_number')

        if not email and not phone_number:
            raise serializers.ValidationError("Either email or phone number must be provided.")

        # Ensure only one of the fields is updated at a time
        if email and phone_number:
            raise serializers.ValidationError("You can only update either email or phone number, not both.")

        if email:
            if BlockedEmail.objects.filter(email=email).exists():
                raise serializers.ValidationError("This email cannot be used for registration.")
            if User.objects.filter(email=email).exists():
                raise serializers.ValidationError("This email is already in use.")

        if phone_number:
            if BlockedPhoneNumber.objects.filter(phone_number=phone_number).exists():
                raise serializers.ValidationError("This phone number cannot be used for registration.")
            if User.objects.filter(phone_number=phone_number).exists():
                raise serializers.ValidationError("This phone number is already in use.")

        return attrs

    def create(self, validated_data):
        return User.objects.create_user(**validated_data)


class ContactUpdateSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    phone_number = PhoneNumberField(required=False)
    
    def validate(self, attrs):
        email = attrs.get('email')
        phone_number = attrs.get('phone_number')
        user = self.context['request'].user
        
        if not email and not phone_number:
            raise serializers.ValidationError("Either email or phone number must be provided.")
            
        if email and phone_number:
            raise serializers.ValidationError("You can only update either email or phone number at a time.")
            
        # Check if the new contact info is different from current
        if email and email == user.email:
            raise serializers.ValidationError("New email must be different from current email.")
            
        if phone_number and phone_number == user.phone_number:
            raise serializers.ValidationError("New phone number must be different from current phone number.")
            
        # Check if the new contact info is already in use
        if email and User.objects.filter(email=email).exclude(id=user.id).exists():
            raise serializers.ValidationError("This email is already in use.")
            
        if phone_number and User.objects.filter(phone_number=phone_number).exclude(id=user.id).exists():
            raise serializers.ValidationError("This phone number is already in use.")
            
        return attrs


class RegVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    phone_number = PhoneNumberField(required=False)
    code = serializers.CharField(max_length=6, min_length=6)

    def validate(self, attrs):
        if not attrs.get('email') and not attrs.get('phone_number'):
            raise serializers.ValidationError("Either email or phone number must be provided.")
        return attrs

class UpdateUserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ['email', 'phone_number']


class SetPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        password = attrs.get('password')
        confirm_password = attrs.get('confirm_password')

        if password != confirm_password:
            raise serializers.ValidationError("Passwords do not match.")
        return attrs


# class UserCreateSerializer(serializers.ModelSerializer):
#     phone_number = PhoneNumberField(required=False)
#     email = serializers.EmailField(required=False)
#
#     class Meta:
#         model = User
#         fields = ['id', 'email', 'phone_number']
#
#     def validate(self, attrs):
#         email = attrs.get('email')
#         phone_number = attrs.get('phone_number')
#
#         if not email and not phone_number:
#             raise serializers.ValidationError("Either email or phone number must be provided.")
#
#         if email:
#             if BlockedEmail.objects.filter(email=email).exists():
#                 raise serializers.ValidationError("This email cannot be used for registration.")
#             if User.objects.filter(email=email).exists():
#                 raise serializers.ValidationError("This email cannot be used for registration.")
#
#         if phone_number:
#             if BlockedPhoneNumber.objects.filter(phone_number=phone_number).exists():
#                 raise serializers.ValidationError("This phone number cannot be used for registration.")
#             if User.objects.filter(phone_number=phone_number).exists():
#                 raise serializers.ValidationError("This phone number cannot be used for registration.")
#
#         return attrs


# class CustomLoginSerializer(serializers.Serializer):
#     username = serializers.CharField()
#     password = serializers.CharField(style={'input_type': 'password'}, trim_whitespace=False)

#     def validate(self, attrs):
#         username = attrs.get('username')
#         password = attrs.get('password')

#         if username and password:
#             user = authenticate(request=self.context.get('request'), username=username, password=password)
#             if not user:
#                 msg = 'Unable to log in with provided credentials.'
#                 raise serializers.ValidationError(msg, code='authorization')
#         else:
#             msg = 'Must include "username" and "password".'
#             raise serializers.ValidationError(msg, code='authorization')

#         attrs['user'] = user
#         return attrs



class CustomLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(style={'input_type': 'password'}, trim_whitespace=False)

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')

        if username and password:
            # Initialize user as None and try to identify by email or phone number
            user = None

            # Check if username is an email
            try:
                validate_email(username)
                user = User.objects.filter(email=username).first()
            except ValidationError:
                # Not an email, so try validating as a phone number
                try:
                    phone_number = phonenumbers.parse(username, None)  # Parse without country for testing
                    if phonenumbers.is_valid_number(phone_number):
                        # Format the phone number as stored in the database (e.g., E164 format)
                        formatted_phone = phonenumbers.format_number(phone_number, phonenumbers.PhoneNumberFormat.E164)
                        user = User.objects.filter(phone_number=formatted_phone).first()
                except phonenumbers.NumberParseException:
                    # If it's neither a valid email nor a valid phone number
                    raise serializers.ValidationError("Invalid email or phone number format.", code='authorization')

            # Check if the user exists and if the password is correct
            if user is None or not user.check_password(password):
                msg = 'Unable to log in with provided credentials.'
                raise serializers.ValidationError(msg, code='authorization')
        else:
            msg = 'Must include "username" and "password".'
            raise serializers.ValidationError(msg, code='authorization')

        # Attach the validated user to attrs for access in the view
        attrs['user'] = user
        return attrs


class VerificationSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=6, min_length=6)

    def validate_code(self, value):
        if not value.isdigit():
            raise serializers.ValidationError("Verification code must be numeric.")
        return value


    # def validate(self, attrs):
    #     if not attrs.get('email') and not attrs.get('phone_number'):
    #         raise serializers.ValidationError("Either email or phone number must be provided.")
    #     return attrs


# class UserSerializer(BaseUserSerializer):
#     phone_number = PhoneNumberField(required=False)
#
#     class Meta(BaseUserSerializer.Meta):
#         fields = ['id', 'email', 'phone_number', 'is_phone_verified', 'is_email_verified']


# class PhoneNumberSerializer(serializers.Serializer):
#     phone_number = PhoneNumberField()


class PasswordResetRequestSerializer(serializers.Serializer):
    email_or_phone = serializers.CharField()

    def validate_email_or_phone(self, value):
        """
        Custom validation to check whether the value is either a valid email or a valid phone number.
        """
        # Try validating it as an email
        try:
            validate_email(value)
            return value  # If it's a valid email, return the value
        except ValidationError:
            pass

        # Try validating it as a phone number
        try:
            phone_number = phonenumbers.parse(value, None)
            if phonenumbers.is_valid_number(phone_number):
                return phonenumbers.format_number(phone_number, phonenumbers.PhoneNumberFormat.E164)
            else:
                raise serializers.ValidationError("Invalid phone number")
        except phonenumbers.NumberParseException:
            raise serializers.ValidationError("Invalid email or phone number")

    def validate(self, attrs):
        email_or_phone = attrs.get('email_or_phone')
        validated_contact = self.validate_email_or_phone(email_or_phone)
        attrs['validated_contact'] = validated_contact  # Store validated contact info in attrs
        return attrs


class PasswordResetConfirmSerializer(serializers.Serializer):
    email_or_phone = serializers.CharField()  # No need to validate here, this was done in previous step
    code = serializers.CharField()
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        validate_password(attrs['new_password'])
        return attrs


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        validate_password(attrs['new_password'])
        return attrs


class ChangePrimaryAuthMethodSerializer(serializers.Serializer):
    new_primary_method = serializers.ChoiceField(choices=['email', 'phone'])
