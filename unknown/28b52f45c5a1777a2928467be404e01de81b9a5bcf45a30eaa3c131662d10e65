from django.urls import path
from rest_framework_nested import routers
from . import views

router = routers.DefaultRouter()
router.register('payment-options', views.PaymentOptionViewSet, basename='payment-options')

urlpatterns = [
    # Stripe
    # path('create-payment-intent/', views.StripeCheckoutView.as_view(), name='create-payment-intent'),
    path('payment-intent-confirm/', views.ConfirmPaymentView.as_view(), name='payment-intent-confirm'),
    path('payment-intent-secret/', views.PaymentIntentClientSecretView.as_view(), name='payment-intent-secret'),
    path('stripe/webhook/', views.StripeWebhookView.as_view(), name='stripe-webhook'),

    # Braintree
    # path('client-token/', views.GenerateClientTokenView.as_view()),
    # path('process-payment/', views.ProcessPaymentView.as_view()),
    # path('braintree/webhook/', views.BraintreeWebhookView.as_view()),

    # PayPal
    path('create-paypal-order/', views.CreatePayPalOrderView.as_view(), name='create-paypal-order'),
    path('capture-paypal-order/', views.CapturePayPalOrderView.as_view(), name='capture-paypal-order'),
    path('paypal/webhook/', views.PayPalWebhookView.as_view(), name='paypal-webhook'),
]

urlpatterns += router.urls
