from rest_framework import serializers
import stripe


def create_stripe_payment_intent(order):
    try:
        # Check if customer already exists in Stripe
        stripe_customer = stripe.Customer.list(email=order.customer.user.email).data

        # If customer doesn't exist, create a new one
        if not stripe_customer:
            stripe_customer = stripe.Customer.create(
                email=order.customer.user.email,
                name=f"{order.customer.first_name} {order.customer.last_name}",
                phone=order.customer.user.phone_number,
            )
        else:
            stripe_customer = stripe_customer[0]

        # Create a PaymentIntent
        payment_intent = stripe.PaymentIntent.create(
            amount=int(order.total * 100),  # Stripe requires the amount in cents
            currency='usd',
            customer=stripe_customer.id,
            metadata={
                'order_id': order.id
            },
            automatic_payment_methods={'enabled': True},
            # idempotency_key=f"order_{order.id}_{order.total}",  # Is this really necessary?
        )

        return payment_intent

    except stripe.error.CardError as e:
        raise serializers.ValidationError({'stripe_error': 'There was an issue with the card.'})
    except stripe.error.InvalidRequestError as e:
        raise serializers.ValidationError({'stripe_error': 'Invalid request to Stripe API.'})
    except stripe.error.APIConnectionError as e:
        raise serializers.ValidationError({'stripe_error': 'Network communication with Stripe failed.'})
    except stripe.error.StripeError as e:
        raise serializers.ValidationError({'stripe_error': 'An error occurred while processing your payment.'})

# def create_order(cart, customer, selected_address, payment_method):
#     cart_serializer = CartSerializer(cart)
#     subtotal = cart_serializer.data['total_price']
#     shipping_cost = cart_serializer.data['shipping_cost']
#     total_weight = cart_serializer.data['cart_weight']
#     total = cart_serializer.data['grand_total']
#
#     with transaction.atomic():
#         order = Order.objects.create(
#             customer=customer,
#             selected_address=selected_address,
#             payment_method=payment_method,
#             shipping_cost=shipping_cost,
#             subtotal=subtotal,
#             total_weight=total_weight,
#             total=total
#         )
#         # Handle order items and stock updates...
#         return order
