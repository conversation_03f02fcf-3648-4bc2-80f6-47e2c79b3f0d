from django.conf import settings
from django.core.mail import send_mail
from django.dispatch import receiver
from .signals import low_stock_alert


@receiver(low_stock_alert)
def send_low_stock_alert(sender, product_variant, **kwargs):
    subject = f'Low Stock Alert: {product_variant.product.title} - {product_variant.price_label}'
    message = (f'The stock quantity for {product_variant.product.title} '
               f'({product_variant.price_label}) has dropped below {product_variant.stock_qty + 1}.'
               f'Current stock: {product_variant.stock_qty}')
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [settings.STORE_OWNER_EMAIL]

    send_mail(subject, message, from_email, recipient_list)
