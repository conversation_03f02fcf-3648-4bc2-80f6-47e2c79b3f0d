version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile-dev
    volumes:
      - ./apps:/app/apps        # Sync app code for development
      - ./media:/app/media      # Persist media files
      - ./static:/app/static    # Persist static files
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=pc_hardware.settings.dev
      - DATABASE_URL=**************************************/mydatabase
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:16.6-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=mydatabase
      - POSTGRES_USER=myuser
      - POSTGRES_PASSWORD=mypassword
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U myuser -d mydatabase" ]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432"  # Expose PostgreSQL port to the host

  redis:
    image: redis:7.4.2-alpine
    volumes:
      - redis_data:/data
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "6379:6379"  # Expose Redis port to the host

volumes:
  postgres_data:
  redis_data: