from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from .models import PaymentOption, PayPalOrder


class PaymentMethodSerializer(ModelSerializer):
    class Meta:
        model = PaymentOption
        fields = ['id', 'name', 'slug']


class PaymentOptionSerializer(ModelSerializer):
    class Meta:
        model = PaymentOption
        fields = ['id', 'name']


class PayPalOrderSerializer(ModelSerializer):
    class Meta:
        model = PayPalOrder
        fields = ['paypal_order_id', 'status', 'created_at']


class CreatePayPalOrderSerializer(serializers.Serializer):
    order_id = serializers.IntegerField()
