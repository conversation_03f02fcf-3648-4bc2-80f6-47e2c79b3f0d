// Zustand store (filterStore.ts)
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface FilterStoreShape {
  productTypeId: number
  currentCategory: string
  categoryProductTypeMap: Record<string, number>
  selectedFilters: Record<string, string | number | (string | number)[] | [number, number]>
  setProductTypeId: (proType: number) => void
  setCurrentCategory: (category: string) => void
  setCategoryProductType: (category: string, productTypeId: number) => void
  updateFilter: (filterName: string, filterValue: string | number | (string | number)[]) => void
  resetFilters: () => void
  shouldFetchFilters: (category: string) => boolean
}

const filterStore = create<FilterStoreShape>()(
  persist(
    (set, get) => ({
  productTypeId: 0,
  currentCategory: '',
  categoryProductTypeMap: {},
  selectedFilters: {},

  setProductTypeId: (proType) => set({ productTypeId: proType }),

  setCurrentCategory: (category) => {
    const state = get()
    if (state.currentCategory !== category) {
      // Category changed, reset filters and update current category
      const cachedProductTypeId = state.categoryProductTypeMap[category]
      set({
        currentCategory: category,
        selectedFilters: {},
        // Only set productTypeId if we have cached data, otherwise keep current value
        ...(cachedProductTypeId && { productTypeId: cachedProductTypeId })
      })
    }
  },

  setCategoryProductType: (category, productTypeId) => {
    const state = get()
    // Only update if the productTypeId is different to avoid unnecessary re-renders
    if (state.categoryProductTypeMap[category] !== productTypeId || state.productTypeId !== productTypeId) {
      set({
        categoryProductTypeMap: {
          ...state.categoryProductTypeMap,
          [category]: productTypeId
        },
        productTypeId: productTypeId
      })
    }
  },

  updateFilter: (filterName, filterValue) =>
    set((state) => ({
      selectedFilters: { ...state.selectedFilters, [filterName]: filterValue },
    })),

  resetFilters: () => set({ selectedFilters: {} }),

  shouldFetchFilters: (category) => {
    const state = get()
    // Only fetch filters if we don't have cached productTypeId for this category
    return !state.categoryProductTypeMap[category]
  },
    }),
    {
      name: 'filter-store', // unique name for localStorage key
      partialize: (state) => ({
        categoryProductTypeMap: state.categoryProductTypeMap
      }), // only persist the category-product type mapping
    }
  )
)

export default filterStore
