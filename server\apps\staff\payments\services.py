from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import PaymentOptionProxy, PayPalOrderProxy, PaymentTransactionAudit, PaymentDispute
from apps.order.models import Order


class PaymentAnalyticsService:
    """Service for payment analytics and reporting"""
    
    @staticmethod
    def get_payment_analytics(days=30):
        """Get comprehensive payment analytics"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Base order queryset for the time period
        orders = Order.objects.filter(placed_at__gte=cutoff_date)
        
        # Basic transaction counts
        total_transactions = orders.count()
        successful_transactions = orders.filter(payment_status='Paid').count()
        failed_transactions = orders.exclude(payment_status='Paid').count()
        
        # Revenue calculations
        paid_orders = orders.filter(payment_status='Paid')
        total_revenue = paid_orders.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
        average_transaction_value = paid_orders.aggregate(avg=Avg('total'))['avg'] or Decimal('0.00')
        
        # Success rate
        success_rate = (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0
        
        # Payment method distribution
        payment_method_distribution = PaymentAnalyticsService._get_payment_method_distribution(orders)
        
        # Daily revenue
        daily_revenue = PaymentAnalyticsService._get_daily_revenue(paid_orders, days)
        
        # Dispute and refund rates
        dispute_rate = PaymentAnalyticsService._get_dispute_rate(orders)
        refund_rate = PaymentAnalyticsService._get_refund_rate(orders)
        
        # Top failure reasons
        top_failure_reasons = PaymentAnalyticsService._get_failure_reasons(orders)
        
        return {
            'total_transactions': total_transactions,
            'successful_transactions': successful_transactions,
            'failed_transactions': failed_transactions,
            'total_revenue': round(total_revenue, 2),
            'average_transaction_value': round(average_transaction_value, 2),
            'success_rate': round(success_rate, 2),
            'payment_method_distribution': payment_method_distribution,
            'daily_revenue': daily_revenue,
            'dispute_rate': round(dispute_rate, 2),
            'refund_rate': round(refund_rate, 2),
            'top_failure_reasons': top_failure_reasons
        }
    
    @staticmethod
    def _get_payment_method_distribution(orders):
        """Get distribution of payment methods"""
        distribution = orders.values(
            'payment_method__name'
        ).annotate(
            count=Count('id'),
            revenue=Sum('total', filter=Q(payment_status='Paid'))
        ).order_by('-count')
        
        return {
            item['payment_method__name'] or 'Unknown': {
                'count': item['count'],
                'revenue': float(item['revenue'] or 0)
            }
            for item in distribution
        }
    
    @staticmethod
    def _get_daily_revenue(paid_orders, days):
        """Get daily revenue for the period"""
        daily_revenue = {}
        
        for order in paid_orders:
            date_key = order.placed_at.date().isoformat()
            daily_revenue[date_key] = daily_revenue.get(date_key, 0) + float(order.total)
        
        return daily_revenue
    
    @staticmethod
    def _get_dispute_rate(orders):
        """Calculate dispute rate"""
        total_orders = orders.count()
        if total_orders == 0:
            return 0.0
        
        disputed_orders = PaymentDispute.objects.filter(
            order__in=orders
        ).count()
        
        return (disputed_orders / total_orders) * 100
    
    @staticmethod
    def _get_refund_rate(orders):
        """Calculate refund rate (placeholder)"""
        # This would integrate with actual refund tracking
        # For now, return a placeholder value
        return 2.5
    
    @staticmethod
    def _get_failure_reasons(orders):
        """Get top failure reasons (placeholder)"""
        # This would integrate with payment gateway error logs
        return [
            {'reason': 'Insufficient funds', 'count': 45},
            {'reason': 'Card declined', 'count': 32},
            {'reason': 'Expired card', 'count': 28},
            {'reason': 'Invalid CVV', 'count': 15},
            {'reason': 'Network error', 'count': 8},
        ]


class PaymentMonitoringService:
    """Service for payment monitoring and alerts"""
    
    @staticmethod
    def get_monitoring_data():
        """Get real-time payment monitoring data"""
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        last_24h = now - timedelta(hours=24)
        
        # Recent transactions
        recent_transactions = PaymentMonitoringService._get_recent_transactions()
        
        # Failed transactions
        failed_transactions = PaymentMonitoringService._get_failed_transactions(last_24h)
        
        # Suspicious transactions
        suspicious_transactions = PaymentMonitoringService._get_suspicious_transactions()
        
        # High value transactions
        high_value_transactions = PaymentMonitoringService._get_high_value_transactions(last_24h)
        
        # Payment gateway status
        gateway_status = PaymentMonitoringService._get_gateway_status()
        
        # Alerts
        alerts = PaymentMonitoringService._get_alerts()
        
        return {
            'recent_transactions': recent_transactions,
            'failed_transactions': failed_transactions,
            'suspicious_transactions': suspicious_transactions,
            'high_value_transactions': high_value_transactions,
            'payment_gateway_status': gateway_status,
            'alerts': alerts
        }
    
    @staticmethod
    def _get_recent_transactions(limit=20):
        """Get recent transactions"""
        orders = Order.objects.select_related(
            'customer__user', 'payment_method'
        ).order_by('-placed_at')[:limit]
        
        return [
            {
                'id': order.id,
                'customer_email': order.customer.user.email if order.customer else None,
                'amount': float(order.total),
                'payment_method': order.payment_method.name if order.payment_method else None,
                'status': order.payment_status,
                'placed_at': order.placed_at.isoformat(),
            }
            for order in orders
        ]
    
    @staticmethod
    def _get_failed_transactions(since):
        """Get failed transactions since given time"""
        failed_orders = Order.objects.filter(
            placed_at__gte=since,
            payment_status__in=['Failed', 'Cancelled', 'Pending']
        ).select_related('customer__user', 'payment_method')[:50]
        
        return [
            {
                'id': order.id,
                'customer_email': order.customer.user.email if order.customer else None,
                'amount': float(order.total),
                'payment_method': order.payment_method.name if order.payment_method else None,
                'status': order.payment_status,
                'placed_at': order.placed_at.isoformat(),
                'failure_reason': 'Unknown',  # Would integrate with gateway data
            }
            for order in failed_orders
        ]
    
    @staticmethod
    def _get_suspicious_transactions():
        """Get potentially suspicious transactions"""
        # This would implement fraud detection logic
        # For now, return placeholder data
        return [
            {
                'id': 12345,
                'customer_email': '<EMAIL>',
                'amount': 999.99,
                'risk_score': 85,
                'risk_factors': ['High value', 'New customer', 'Multiple attempts'],
                'placed_at': timezone.now().isoformat(),
            }
        ]
    
    @staticmethod
    def _get_high_value_transactions(since, threshold=500):
        """Get high value transactions"""
        high_value_orders = Order.objects.filter(
            placed_at__gte=since,
            total__gte=threshold,
            payment_status='Paid'
        ).select_related('customer__user', 'payment_method')[:20]
        
        return [
            {
                'id': order.id,
                'customer_email': order.customer.user.email if order.customer else None,
                'amount': float(order.total),
                'payment_method': order.payment_method.name if order.payment_method else None,
                'placed_at': order.placed_at.isoformat(),
            }
            for order in high_value_orders
        ]
    
    @staticmethod
    def _get_gateway_status():
        """Get payment gateway status"""
        # This would integrate with actual gateway health checks
        return {
            'stripe': {'status': 'operational', 'response_time': 120},
            'paypal': {'status': 'operational', 'response_time': 95},
            'braintree': {'status': 'degraded', 'response_time': 450},
        }
    
    @staticmethod
    def _get_alerts():
        """Get current payment alerts"""
        alerts = []
        
        # Check for high failure rate
        recent_orders = Order.objects.filter(
            placed_at__gte=timezone.now() - timedelta(hours=1)
        )
        if recent_orders.count() > 0:
            failure_rate = recent_orders.exclude(payment_status='Paid').count() / recent_orders.count()
            if failure_rate > 0.1:  # 10% failure rate threshold
                alerts.append({
                    'type': 'HIGH_FAILURE_RATE',
                    'severity': 'WARNING',
                    'message': f'Payment failure rate is {failure_rate:.1%} in the last hour',
                    'created_at': timezone.now().isoformat(),
                })
        
        # Check for gateway issues (placeholder)
        alerts.append({
            'type': 'GATEWAY_SLOW',
            'severity': 'INFO',
            'message': 'Braintree gateway response time is elevated',
            'created_at': timezone.now().isoformat(),
        })
        
        return alerts


class PaymentManagementService:
    """Service for payment management operations"""
    
    @staticmethod
    def bulk_process_payments(payment_ids, operation, performed_by, **kwargs):
        """Bulk process payment operations"""
        processed_count = 0
        errors = []
        
        for payment_id in payment_ids:
            try:
                order = Order.objects.get(id=payment_id)
                
                if operation == 'refund':
                    # This would integrate with payment gateway refund API
                    # For now, just log the action
                    PaymentTransactionAudit.objects.create(
                        transaction_id=f"refund_{order.id}",
                        payment_method=order.payment_method.name if order.payment_method else 'Unknown',
                        order_id=order.id,
                        customer_id=order.customer.id if order.customer else None,
                        amount=kwargs.get('refund_amount', order.total),
                        status='REFUNDED',
                        staff_user=performed_by,
                        action='refund_initiated',
                        notes=kwargs.get('reason', '')
                    )
                    processed_count += 1
                
                elif operation == 'dispute':
                    # Create dispute record
                    PaymentDispute.objects.create(
                        dispute_id=f"DISPUTE_{order.id}_{timezone.now().timestamp()}",
                        order=order,
                        dispute_type='INQUIRY',
                        amount=order.total,
                        reason=kwargs.get('reason', 'Staff initiated dispute'),
                        assigned_to=performed_by
                    )
                    processed_count += 1
                
                elif operation == 'investigate':
                    # Flag for investigation
                    PaymentTransactionAudit.objects.create(
                        transaction_id=f"investigate_{order.id}",
                        payment_method=order.payment_method.name if order.payment_method else 'Unknown',
                        order_id=order.id,
                        customer_id=order.customer.id if order.customer else None,
                        amount=order.total,
                        status='UNDER_INVESTIGATION',
                        staff_user=performed_by,
                        action='flagged_for_investigation',
                        notes=kwargs.get('reason', '')
                    )
                    processed_count += 1
                
            except Order.DoesNotExist:
                errors.append(f"Order {payment_id} not found")
            except Exception as e:
                errors.append(f"Error processing payment {payment_id}: {str(e)}")
        
        return {
            'processed_count': processed_count,
            'errors': errors,
            'total_requested': len(payment_ids)
        }
