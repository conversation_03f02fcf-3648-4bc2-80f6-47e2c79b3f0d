@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: $font-weight-medium;
  border-radius: $border-radius;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
}

// Size variants
.sm {
  padding: 2px 6px;
  font-size: $font-size-xs;
  line-height: 1.2;
}

.md {
  padding: 4px 8px;
  font-size: $font-size-xs;
  line-height: 1.3;
}

.lg {
  padding: 6px 12px;
  font-size: $font-size-sm;
  line-height: 1.4;
}

// Dot variant
.dot {
  padding: 6px;
  border-radius: 50%;
  
  &.sm {
    padding: 4px;
  }
  
  &.lg {
    padding: 8px;
  }
}

// Color variants
.default {
  background-color: $gray-100;
  color: $gray-800;
}

.primary {
  background-color: $primary-100;
  color: $primary-800;
}

.secondary {
  background-color: $gray-100;
  color: $gray-700;
}

.success {
  background-color: $green-100;
  color: $green-800;
}

.warning {
  background-color: $yellow-100;
  color: $yellow-800;
}

.error {
  background-color: $red-100;
  color: $red-800;
}

.info {
  background-color: $blue-100;
  color: $blue-800;
}

// Hover effects for interactive badges
.badge:hover {
  &.primary {
    background-color: $primary-200;
  }
  
  &.success {
    background-color: $green-200;
  }
  
  &.warning {
    background-color: $yellow-200;
  }
  
  &.error {
    background-color: $red-200;
  }
  
  &.info {
    background-color: $blue-200;
  }
  
  &.default {
    background-color: $gray-200;
  }
  
  &.secondary {
    background-color: $gray-200;
  }
}
