@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;

// Mobile-first base styles
.wishlist {
  width: 100%;
  padding: 1rem 1rem 0;
  margin: 0;
  max-width: 100%;
  background-color: transparent;
  box-shadow: none;

  .wishlist__items {
    @include flexbox(flex-start, flex-start, column);
    gap: 1rem;

    .wishlist__item {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      gap: 0.5rem;
      padding: 0.8rem;
      background-color: white;
      box-shadow: $box-shadow-1;
      position: relative;

      &.inactive {
        opacity: 0.6;
      }

      .item__details {
        .product__title {
          font-size: $font-size-3;
        }

        .product__price,
        .added__date,
        .product__link {
          color: $primary-dark-text-color;
        }

        .item__actions {
          @include flexbox(space-between, center);
          // width: calc(100% - 1rem);

          .product__link {
            color: $primary-blue;
            font-weight: bold;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .product__image__container {
        @include flexbox(center, center);
        width: 100%;
        position: relative;
        padding: 0.5rem;
        background-color: $alice-blue;

        .inactive__overlay {
          @include flexbox(center, center);
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 10;
        }

        .inactive__text {
          color: white;
          font-weight: bold;
          text-align: center;
          padding: 0.3rem;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: 4px;
          font-size: 14px;
        }

        .product__image {
          object-fit: contain;
          width: 100%;
          height: 150px;
          max-width: 200px;
        }
      }

      .product__details {
        @include flexbox(space-between, flex-start, column);
        gap: 0.5rem;

        .product__info {
          text-align: center;

          h3 {
            font-size: 16px;
            margin-bottom: 0.5rem;
          }

          .product__price {
            font-size: 18px;
            font-weight: bold;
          }

          .product__date {
            font-size: 12px;
          }
        }

        .product__actions {
          @include flexbox(center, center);
          width: 100%;
          gap: 1rem;

          button {
            flex: 1;
            max-width: 150px;
          }

          .delete__button {
            flex: 0;
            min-width: 40px;
          }
        }
      }
    }
  }

  .emptyMessage {
    text-align: center;
    color: $primary-dark-text-color;
    font-size: $font-size-3;
  }

  .loader,
  .error {
    text-align: center;
    font-size: $font-size-3;
    color: $warning-text;
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .wishlist {
    padding: 0;
    // background-color: $info-bg;
    // box-shadow: $box-shadow-1;
    max-width: 800px;
    margin: auto;

    .wishlist__items {
      // background-color: rgb(119, 146, 168);
      // width: calc(100% - 1rem);
      gap: $padding-3;

      .wishlist__item {
        // background-color: yellow;
        // width: calc(100% - 1rem);
        grid-template-columns: 20% 80%;
        gap: 1rem;
        padding: 1rem;

        .product__image__container {
          @include flexbox(flex-start, flex-start);
          width: auto;

          .inactive__text {
            padding: 0.5rem;
            font-size: 16px;
          }

          .product__image {
            height: 100px;
            max-width: none;
          }
        }

        .item__details {
          padding: 0 1rem 0 0;
        }

        .product__details {
          gap: 1rem;

          .product__info {
            text-align: left;

            h3 {
              font-size: $font-size-3;
              margin-bottom: 0;
            }

            .product__price {
              font-size: inherit;
              font-weight: normal;
            }

            .product__date {
              font-size: inherit;
            }
          }

          .product__actions {
            @include flexbox(flex-end, center);
            width: auto;
            gap: 0.5rem;

            button {
              flex: 0;
              max-width: none;
            }

            .delete__button {
              flex: 0;
              min-width: auto;
            }
          }
        }
      }
    }
  }
}
