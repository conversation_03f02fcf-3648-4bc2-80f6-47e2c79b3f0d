@use '../../../scss/mixins' as *;
@use '../../../scss/variables' as *;
@use 'sass:map';

.navbar__links {
  width: fit-content;
  padding: 8px 10px;
  font-weight: map.get($font-weight, 'medium');
  @include flexbox(flex-start, center);
  color: #fff;
  height: 100%;
  gap: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: $border-radius-1;

  &:hover {
    opacity: 0.9;
    background-color: rgba(255, 255, 255, 0.1);

    i {
      transform: translateY(1px);
    }
  }



  button {
    background: none;
    border: none;
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 0;
    margin: 0;
    @include flexbox(center, center);
    transition: inherit;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &:focus {
      outline: none;
    }
  }

  i {
    font-size: 14px;
    @include flexbox(center, center);
    line-height: 1;
    transition: transform 0.2s ease;
    height: 14px;
    width: 14px;
    pointer-events: none;
  }
}

// Responsive adjustments using project media queries
@media (max-width: $mobile) {
  .navbar__links {
    padding: $padding-1 $padding-2;
    gap: calc($padding-1 / 2);

    button {
      font-size: $font-size-3;
    }

    i {
      font-size: $font-size-1;
      height: $font-size-1;
      width: $font-size-1;
    }
  }
}