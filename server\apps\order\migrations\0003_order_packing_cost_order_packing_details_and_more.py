# Generated by Django 5.2.5 on 2025-09-08 09:13

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='packing_cost',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=6),
        ),
        migrations.AddField(
            model_name='order',
            name='packing_details',
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name='order',
            name='total_volume',
            field=models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=12),
        ),
    ]
