"""
Cart Validation Service

Handles cart validation logic including item availability, quantity limits,
and business rule validation.
"""

import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from django.core.exceptions import ValidationError

from ..models import Cart, CartItem
from apps.products.models import ProductVariant


class CartValidationService:
    """Service for handling cart validation operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_cart_for_checkout(self, cart: Cart) -> Dict[str, Any]:
        """
        Comprehensive validation of cart before checkout
        
        Args:
            cart: Cart instance
            
        Returns:
            Dictionary with validation result
        """
        try:
            validation_results = []
            
            # Check if cart has selected items
            selected_items = cart.cart_items.filter(is_selected=True)
            if not selected_items.exists():
                return {
                    'valid': False,
                    'errors': ['No items selected for checkout'],
                    'message': 'Please select at least one item to proceed'
                }
            
            # Validate each selected item
            for item in selected_items:
                item_validation = self.validate_cart_item(item)
                if not item_validation['valid']:
                    validation_results.extend(item_validation['errors'])
            
            # Check cart-level business rules
            cart_rules_validation = self._validate_cart_business_rules(cart, selected_items)
            if not cart_rules_validation['valid']:
                validation_results.extend(cart_rules_validation['errors'])
            
            if validation_results:
                return {
                    'valid': False,
                    'errors': validation_results,
                    'message': 'Cart validation failed'
                }
            
            return {
                'valid': True,
                'message': 'Cart is valid for checkout'
            }
            
        except Exception as e:
            self.logger.error(f"Error validating cart {cart.id}: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'message': 'Cart validation failed due to system error'
            }
    
    def validate_cart_item(self, cart_item: CartItem) -> Dict[str, Any]:
        """
        Validate a single cart item
        
        Args:
            cart_item: CartItem instance
            
        Returns:
            Dictionary with validation result
        """
        try:
            errors = []
            
            # Check product variant availability
            if not cart_item.product_variant.is_active:
                errors.append(f"Product '{cart_item.product.title}' is no longer available")
            
            # Check stock availability
            if cart_item.quantity > cart_item.product_variant.stock_qty:
                errors.append(
                    f"Only {cart_item.product_variant.stock_qty} units of "
                    f"'{cart_item.product.title}' are available"
                )
            
            # Check minimum quantity requirements
            min_quantity = getattr(cart_item.product_variant, 'min_order_quantity', 1)
            if cart_item.quantity < min_quantity:
                errors.append(
                    f"Minimum order quantity for '{cart_item.product.title}' is {min_quantity}"
                )
            
            # Check maximum quantity limits
            max_quantity = getattr(cart_item.product_variant, 'max_order_quantity', None)
            if max_quantity and cart_item.quantity > max_quantity:
                errors.append(
                    f"Maximum order quantity for '{cart_item.product.title}' is {max_quantity}"
                )
            
            # Check price validity
            if cart_item.product_variant.price <= 0:
                errors.append(f"Invalid price for '{cart_item.product.title}'")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'item_id': cart_item.id
            }
            
        except Exception as e:
            self.logger.error(f"Error validating cart item {cart_item.id}: {e}")
            return {
                'valid': False,
                'errors': [f"Validation error for item: {str(e)}"],
                'item_id': cart_item.id
            }
    
    def validate_add_to_cart(self, product_variant: ProductVariant, quantity: int) -> Dict[str, Any]:
        """
        Validate adding an item to cart
        
        Args:
            product_variant: ProductVariant instance
            quantity: Quantity to add
            
        Returns:
            Dictionary with validation result
        """
        try:
            errors = []
            
            # Check if product variant is active
            if not product_variant.is_active:
                errors.append("This product is no longer available")
            
            # Check if product is active
            if not product_variant.product.is_active:
                errors.append("This product is no longer available")
            
            # Check quantity is positive
            if quantity <= 0:
                errors.append("Quantity must be greater than 0")
            
            # Check stock availability
            if quantity > product_variant.stock_qty:
                errors.append(f"Only {product_variant.stock_qty} units are available")
            
            # Check minimum quantity
            min_quantity = getattr(product_variant, 'min_order_quantity', 1)
            if quantity < min_quantity:
                errors.append(f"Minimum order quantity is {min_quantity}")
            
            # Check maximum quantity
            max_quantity = getattr(product_variant, 'max_order_quantity', None)
            if max_quantity and quantity > max_quantity:
                errors.append(f"Maximum order quantity is {max_quantity}")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'message': 'Item can be added to cart' if len(errors) == 0 else 'Cannot add item to cart'
            }
            
        except Exception as e:
            self.logger.error(f"Error validating add to cart for variant {product_variant.id}: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'message': 'Validation failed due to system error'
            }
    
    def _validate_cart_business_rules(self, cart: Cart, selected_items) -> Dict[str, Any]:
        """
        Validate cart-level business rules
        
        Args:
            cart: Cart instance
            selected_items: QuerySet of selected cart items
            
        Returns:
            Dictionary with validation result
        """
        try:
            errors = []
            
            # Check minimum order value (example business rule)
            from .cart_service import CartCalculationService
            calc_service = CartCalculationService()
            totals = calc_service.calculate_selected_totals(cart)
            
            min_order_value = Decimal('10.00')  # Example minimum
            if totals['total_price'] < min_order_value:
                errors.append(f"Minimum order value is ${min_order_value}")
            
            # Check maximum weight limits (example business rule)
            max_weight = Decimal('50000.00')  # 50kg in grams
            if totals['total_weight'] > max_weight:
                errors.append(f"Order exceeds maximum weight limit of {max_weight/1000}kg")
            
            # Add more business rules as needed
            
            return {
                'valid': len(errors) == 0,
                'errors': errors
            }
            
        except Exception as e:
            self.logger.error(f"Error validating cart business rules for cart {cart.id}: {e}")
            return {
                'valid': False,
                'errors': [str(e)]
            }
