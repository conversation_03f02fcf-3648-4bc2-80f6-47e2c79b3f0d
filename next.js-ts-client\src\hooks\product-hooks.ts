import { useQuery } from "@tanstack/react-query"
import APIClient from "../lib/api-client"
import { Category, ProductShape } from "../types/product-types"
import { CACHE_KEY_PRODUCTS } from "../constants/constants"
import filterStore from "../stores/filter-store"
import { FilterOptionsShape } from "../types/types"


const apiClient = new APIClient<Category[]>('/products/categories/')

export const useCategories = () => useQuery({
  queryKey: ['categories'],
  queryFn: ({ signal }) => apiClient.get({ signal }), // ✅ Pass abort signal
  staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
  // initialData:  Here we can add categories as static data
  // cacheTime: 1000 * 60 * 60, <-- This is wrong!
  gcTime: 1000 * 60 * 60, // 1 hour
  retry: (failureCount, error) => {
    // ✅ Don't retry on cancelled requests
    if (error.name === 'CanceledError' || error.message === 'canceled') {
      return false
    }
    return failureCount < 3
  }
})

export const useProducts = (slug: string, page: number, queryString: string, searchQuery: string = '') => {
  const { selectedFilters } = filterStore()

  // Initialize API client based on whether searchQuery exists or not
  const apiClient = searchQuery
    ? new APIClient<ProductShape>(`/products/?search=${searchQuery}`)
    : new APIClient<ProductShape>(`/products/category/${slug}/`)

  const queryParams = new URLSearchParams(queryString)

  // Add filters to the query parameters
  Object.entries(selectedFilters).forEach(([key, value]) => {
    if (key === 'price_range' && Array.isArray(value)) {
      queryParams.set('min_price', value[0].toString())
      queryParams.set('max_price', value[1].toString())
    } else if (Array.isArray(value)) {
      value.forEach((val) => queryParams.append(key, val.toString()))
    } else {
      queryParams.set(key, value.toString())
    }
  })

  // If searchQuery exists, add it to queryParams
  if (searchQuery) {
    queryParams.set('search', searchQuery)
  }

  return useQuery({
    queryKey: [CACHE_KEY_PRODUCTS, slug, selectedFilters, page, queryString, searchQuery],
    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),
  })
}

export const useProductFilterOptions = (productTypeId: number) => {
  const apiClient = new APIClient<FilterOptionsShape>('/products/product-filter-options/')

  return useQuery({
    queryKey: ['filterOptions', productTypeId],
    queryFn: () => apiClient.get({
      params: { product_type_id: productTypeId }
    }),
    enabled: !!productTypeId && productTypeId > 0, // Only fetch if productTypeId is valid
    staleTime: 1000 * 60 * 60 * 24, // 24 hours - filters don't change often
    gcTime: 1000 * 60 * 60 * 24, // 24 hours - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    refetchOnReconnect: false, // Don't refetch on network reconnect
    retry: 1, // Only retry once on failure
  })
}
