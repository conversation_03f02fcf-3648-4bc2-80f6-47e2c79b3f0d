from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from . import views

app_name = 'staff_products'

# Create router and register viewsets
router = DefaultRouter()

# Core product management
router.register(r'products', views.ProductStaffViewSet, basename='products')
router.register(r'categories', views.CategoryStaffViewSet, basename='categories')
router.register(r'brands', views.BrandStaffViewSet, basename='brands')
router.register(r'product-types', views.ProductTypeStaffViewSet, basename='product-types')

# Attribute management
router.register(r'attributes', views.AttributeStaffViewSet, basename='attributes')
router.register(r'attribute-values', views.AttributeValueStaffViewSet, basename='attribute-values')

# Product variants and images
router.register(r'variants', views.ProductVariantStaffViewSet, basename='variants')
router.register(r'images', views.ProductImageStaffViewSet, basename='images')

# Reviews and discounts
router.register(r'reviews', views.ReviewStaffViewSet, basename='reviews')
router.register(r'discounts', views.DiscountStaffViewSet, basename='discounts')

# Audit and operations
router.register(r'audit', views.ProductAuditViewSet, basename='audit')
router.register(r'bulk-operations', views.BulkOperationViewSet, basename='bulk-operations')

# Association management
router.register(r'associations', views.AssociationManagementViewSet, basename='associations')
router.register(r'brand-product-types', views.BrandProductTypeStaffViewSet, basename='brand-product-types')
router.register(r'product-attribute-values', views.ProductAttributeValueStaffViewSet, basename='product-attribute-values')
router.register(r'variant-attribute-values', views.ProductVariantAttributeValueStaffViewSet, basename='variant-attribute-values')

urlpatterns = [
    path('', include(router.urls)),
]
