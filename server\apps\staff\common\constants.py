"""
Constants for staff app
Centralized constants used across different domains
"""

# Audit Action Types
AUDIT_ACTIONS = {
    'GROUP_CREATED': 'group_created',
    'GROUP_UPDATED': 'group_updated',
    'GROUP_DELETED': 'group_deleted',
    'USER_ADDED_TO_GROUP': 'user_added_to_group',
    'USER_REMOVED_FROM_GROUP': 'user_removed_from_group',
    'PERMISSION_GRANTED': 'permission_granted',
    'PERMISSION_REVOKED': 'permission_revoked',
    'USER_STAFF_TOGGLED': 'user_staff_toggled',
    'PRODUCT_CREATED': 'product_created',
    'PRODUCT_UPDATED': 'product_updated',
    'PRODUCT_DELETED': 'product_deleted',
    'ORDER_STATUS_CHANGED': 'order_status_changed',
    'ORDER_ASSIGNED': 'order_assigned',
}

# Staff Group Names (predefined groups)
STAFF_GROUPS = {
    # System Administration
    'SUPER_ADMIN': 'Super Administrator (SA)',

    # Staff Management Roles
    'STAFF_MANAGER': 'Staff Manager (SM)',
    'DEPARTMENT_HEAD': 'Department Head (DH)',
    'HR_ADMINISTRATOR': 'HR Administrator (HRA)',

    # Product Management
    'PRODUCT_MANAGER': 'Product Management Executive (PME)',
    'PRODUCT_TEAM_MEMBER': 'Product Management Group Member (PMGM)',
    'PRODUCT_VIEWER': 'Product Catalog Viewer (PCV)',
    'INVENTORY_MANAGER': 'Inventory Management Executive (IME)',

    # Order Management
    'ORDER_MANAGER': 'Order Management Executive (OME)',
    'ORDER_TEAM_MEMBER': 'Order Management Group Member (OMGM)',
    'ORDER_FULFILLMENT': 'Order Fulfillment Specialist (OFS)',

    # Customer Management
    'CUSTOMER_MANAGER': 'Customer Management Executive (CME)',
    'CUSTOMER_SERVICE': 'Customer Service Representative (CSR)',
    'CUSTOMER_ANALYST': 'Customer Data Analyst (CDA)',

    # Content Management
    'CONTENT_MANAGER': 'Content Management Executive (CTME)',
    'CONTENT_MODERATOR': 'Content Moderator (CTM)',

    # Finance & Analytics
    'FINANCE_MANAGER': 'Finance Manager (FM)',
    'BUSINESS_ANALYST': 'Business Analyst (BA)',
}

# Permission Categories
PERMISSION_CATEGORIES = {
    'PRODUCTS': 'products',
    'ORDERS': 'orders',
    'CUSTOMERS': 'customers',
    'INVENTORY': 'inventory',
    'CONTENT': 'content',
    'FINANCE': 'finance',
    'ANALYTICS': 'analytics',
    'SYSTEM': 'system',
}

# Cache Keys
CACHE_KEYS = {
    'USER_PERMISSIONS': 'staff_user_permissions_{user_id}',
    'USER_GROUPS': 'staff_user_groups_{user_id}',
    'USER_PROFILE': 'staff_user_profile_{user_id}',
    'GROUP_MEMBERS': 'staff_group_members_{group_id}',
    'PERMISSION_LIST': 'staff_permissions_list',
}

# Cache Timeouts (in seconds)
CACHE_TIMEOUTS = {
    'USER_PERMISSIONS': 300,  # 5 minutes
    'USER_GROUPS': 300,       # 5 minutes
    'USER_PROFILE': 600,      # 10 minutes
    'GROUP_MEMBERS': 180,     # 3 minutes
    'PERMISSION_LIST': 3600,  # 1 hour
}

# API Response Messages
API_MESSAGES = {
    'SUCCESS': 'Operation completed successfully',
    'CREATED': 'Resource created successfully',
    'UPDATED': 'Resource updated successfully',
    'DELETED': 'Resource deleted successfully',
    'NOT_FOUND': 'Resource not found',
    'PERMISSION_DENIED': 'You do not have permission to perform this action',
    'INVALID_DATA': 'Invalid data provided',
    'USER_NOT_ACTIVE': 'User account is not active',
    'USER_ALREADY_MEMBER': 'User is already a member of this group',
    'USER_NOT_MEMBER': 'User is not a member of this group',
    'CANNOT_DELETE_SUPERUSER': 'Cannot modify superuser permissions',
}

# Pagination Defaults
PAGINATION = {
    'DEFAULT_PAGE_SIZE': 20,
    'MAX_PAGE_SIZE': 100,
    'MIN_PAGE_SIZE': 1,
}

# File Upload Limits
FILE_UPLOAD = {
    'MAX_SIZE_MB': 10,
    'ALLOWED_EXTENSIONS': ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'],
    'IMAGE_EXTENSIONS': ['.jpg', '.jpeg', '.png', '.gif'],
    'DOCUMENT_EXTENSIONS': ['.pdf', '.doc', '.docx'],
}

# Date/Time Formats
DATE_FORMATS = {
    'API_DATE': '%Y-%m-%d',
    'API_DATETIME': '%Y-%m-%dT%H:%M:%S.%fZ',
    'DISPLAY_DATE': '%B %d, %Y',
    'DISPLAY_DATETIME': '%B %d, %Y at %I:%M %p',
}

# Status Choices
STATUS_CHOICES = {
    'ACTIVE': 'active',
    'INACTIVE': 'inactive',
    'PENDING': 'pending',
    'SUSPENDED': 'suspended',
    'ARCHIVED': 'archived',
}

# Priority Levels
PRIORITY_LEVELS = {
    'LOW': 1,
    'MEDIUM': 2,
    'HIGH': 3,
    'URGENT': 4,
    'CRITICAL': 5,
}

# Log Levels
LOG_LEVELS = {
    'DEBUG': 'debug',
    'INFO': 'info',
    'WARNING': 'warning',
    'ERROR': 'error',
    'CRITICAL': 'critical',
}

# HTTP Status Codes (commonly used)
HTTP_STATUS = {
    'OK': 200,
    'CREATED': 201,
    'NO_CONTENT': 204,
    'BAD_REQUEST': 400,
    'UNAUTHORIZED': 401,
    'FORBIDDEN': 403,
    'NOT_FOUND': 404,
    'METHOD_NOT_ALLOWED': 405,
    'CONFLICT': 409,
    'INTERNAL_SERVER_ERROR': 500,
}

# Validation Rules
VALIDATION = {
    'MIN_PASSWORD_LENGTH': 8,
    'MAX_USERNAME_LENGTH': 150,
    'MAX_EMAIL_LENGTH': 254,
    'MAX_NAME_LENGTH': 100,
    'MAX_DESCRIPTION_LENGTH': 500,
    'MAX_NOTES_LENGTH': 1000,
}

# Feature Flags
FEATURES = {
    'AUDIT_LOGGING': True,
    'CACHE_ENABLED': True,
    'EMAIL_NOTIFICATIONS': True,
    'BULK_OPERATIONS': True,
    'ADVANCED_SEARCH': True,
    'EXPORT_DATA': True,
}

# Email Templates
EMAIL_TEMPLATES = {
    'USER_ADDED_TO_GROUP': 'staff/emails/user_added_to_group.html',
    'USER_REMOVED_FROM_GROUP': 'staff/emails/user_removed_from_group.html',
    'PERMISSION_CHANGED': 'staff/emails/permission_changed.html',
    'ACCOUNT_SUSPENDED': 'staff/emails/account_suspended.html',
}

# Default Permissions by Group
DEFAULT_GROUP_PERMISSIONS = {
    # Staff Management Roles
    STAFF_GROUPS['STAFF_MANAGER']: [
        'auth.add_user',
        'auth.change_user',
        'auth.view_user',
        'auth.add_group',
        'auth.change_group',
        'auth.delete_group',
        'auth.view_group',
        'staff.add_staffprofile',
        'staff.change_staffprofile',
        'staff.view_staffprofile',
        'staff.add_groupmembership',
        'staff.change_groupmembership',
        'staff.view_groupmembership',
        'core.can_toggle_staff_status',
        'core.can_manage_staff_roles',
        'core.can_view_audit_logs',
    ],
    STAFF_GROUPS['DEPARTMENT_HEAD']: [
        'auth.view_user',
        'auth.change_user',
        'auth.view_group',
        'staff.view_staffprofile',
        'staff.change_staffprofile',
        'staff.view_groupmembership',
        'staff.change_groupmembership',
        'core.can_manage_staff_roles',
    ],
    STAFF_GROUPS['HR_ADMINISTRATOR']: [
        'auth.add_user',
        'auth.change_user',
        'auth.view_user',
        'staff.add_staffprofile',
        'staff.change_staffprofile',
        'staff.view_staffprofile',
        'staff.delete_staffprofile',
        'core.can_toggle_staff_status',
        'core.can_view_audit_logs',
    ],

    # Product Management
    STAFF_GROUPS['PRODUCT_MANAGER']: [
        'products.add_product',
        'products.change_product',
        'products.delete_product',
        'products.view_product',
        'products.add_category',
        'products.change_category',
        'products.delete_category',
        'products.view_category',
        'products.add_brand',
        'products.change_brand',
        'products.delete_brand',
        'products.view_brand',
    ],
    STAFF_GROUPS['PRODUCT_TEAM_MEMBER']: [
        'products.add_product',
        'products.change_product',
        'products.view_product',
        'products.change_category',
        'products.view_category',
        'products.view_brand',
    ],
    STAFF_GROUPS['PRODUCT_VIEWER']: [
        'products.view_product',
        'products.view_category',
        'products.view_brand',
    ],

    # Order Management
    STAFF_GROUPS['ORDER_MANAGER']: [
        'order.add_order',
        'order.change_order',
        'order.delete_order',
        'order.view_order',
        'customers.view_customer',
        'customers.change_customer',
    ],
    STAFF_GROUPS['ORDER_TEAM_MEMBER']: [
        'order.change_order',
        'order.view_order',
        'customers.view_customer',
    ],
    STAFF_GROUPS['ORDER_FULFILLMENT']: [
        'order.view_order',
        'order.change_order',
    ],

    # Customer Management
    STAFF_GROUPS['CUSTOMER_MANAGER']: [
        'customers.add_customer',
        'customers.change_customer',
        'customers.delete_customer',
        'customers.view_customer',
        'order.view_order',
    ],
    STAFF_GROUPS['CUSTOMER_SERVICE']: [
        'customers.view_customer',
        'customers.change_customer',
        'order.view_order',
        'order.change_order',
    ],
    STAFF_GROUPS['CUSTOMER_ANALYST']: [
        'customers.view_customer',
        'order.view_order',
    ],

    # Content Management
    STAFF_GROUPS['CONTENT_MANAGER']: [
        'products.change_product',
        'products.view_product',
    ],
    STAFF_GROUPS['CONTENT_MODERATOR']: [
        'products.view_product',
    ],

    # Finance & Analytics
    STAFF_GROUPS['FINANCE_MANAGER']: [
        'order.view_order',
        'customers.view_customer',
    ],
    STAFF_GROUPS['BUSINESS_ANALYST']: [
        'products.view_product',
        'products.view_category',
        'products.view_brand',
        'order.view_order',
        'customers.view_customer',
    ],
}
