from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'staff_cart'

# Create router and register viewsets
router = DefaultRouter()

# Cart management
router.register(r'carts', views.CartStaffViewSet, basename='carts')
router.register(r'cart-items', views.CartItemStaffViewSet, basename='cart-items')

urlpatterns = [
    path('', include(router.urls)),
]
