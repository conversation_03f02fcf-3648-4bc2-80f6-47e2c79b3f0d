'use client'

import { useEffect, useState } from 'react'
import { useForm, SubmitHandler } from 'react-hook-form'
import { useRouter } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import authStore from '@/src/stores/auth-store'
import { useRegister } from '@/src/hooks/auth-hooks'
import { registerSchema } from '@/src/schemas/schemas'

import Link from 'next/link'
import PhoneNumberInput from '@/src/components/utils/phone-number-input/PhoneNumberInput'
import { ErrorResponse } from '@/src/types/types'
import styles from './Initiate.module.scss'
import AuthLayout from '../../AuthLayout'

type RegisterFormInputs = {
  username: string
}

export interface InitRegUserShape {
  email?: string
  phone_number?: string
}

const InitiateRegistration = () => {
  const router = useRouter()
  const { mutation } = useRegister()
  const { isLoggedIn, setRegInitiated } = authStore()
  const [isPhoneInput, setIsPhoneInput] = useState(false)
  const [usernameValue, setUsernameValue] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<RegisterFormInputs>({
    resolver: zodResolver(registerSchema),
  })

  // Handle input change and sync with form state
  const handleInputChange = (value: string) => {
    setUsernameValue(value)
    setValue('username', value) // Sync with react-hook-form
  }
  // Watch for changes to usernameValue and toggle phone input mode
  useEffect(() => {
    if (usernameValue && /^[0-9+]/.test(usernameValue)) {
      setIsPhoneInput(true)
    } else {
      setIsPhoneInput(false)
    }
  }, [usernameValue])

  // Navigate on successful registration
  useEffect(() => {
    if (mutation.isSuccess) {
      setRegInitiated(true)
      router.push('/register/verify')
    }
  }, [mutation.isSuccess, router, setRegInitiated])

  useEffect(() => {
    if (isLoggedIn) {
      router.push('/')
    }
  }, [isLoggedIn, router])

  // Return null to prevent rendering anything if user is already logged in
  if (isLoggedIn) {
    return null
  }

  const onSubmit: SubmitHandler<RegisterFormInputs> = async (data) => {
    let submissionData: InitRegUserShape = {}

    if (isPhoneInput) {
      const username = data.username.startsWith('+')
        ? data.username
        : `+${data.username}`
      submissionData = { phone_number: username }
    } else {
      submissionData = { email: data.username }
    }

    console.log(submissionData)
    mutation.mutate(submissionData)
  }

  return (
    <AuthLayout
      title='Create your account'
      error={mutation.error as AxiosError<ErrorResponse>}
    >
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <div className='form_group'>
          <label className='form_label' htmlFor='username'>
            Email or Phone number:
          </label>
          {isPhoneInput ? (
            <PhoneNumberInput
              defaultCountry='lk'
              onlyCountries={['lk', 'us']}
              pending={mutation.isPending}
              value={usernameValue || ''}
              onChange={(value: string) => handleInputChange(value)}
              placeholder='Enter phone number'
            />
          ) : (
            <div className={styles.form_group}>
              <input
                className='form_input'
                type='email'
                id='username'
                disabled={mutation.isPending}
                {...register('username')}
                value={usernameValue}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder='Enter email or phone number'
              />
            </div>
          )}
          {errors.username && (
            <p className='form_error'>
              {errors.username.message} &#128543; Eg: +***********
            </p>
          )}
        </div>

        {/* Register with an email */}
        {/* <div className='form_group'>
          <label className='form_label' htmlFor="username">Enter a Phone number:</label>
          <input
            className='form_input'
            type="email"
            id="username"
            disabled={mutation.isPending}
            {...register("username")}
            value={usernameValue}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder="Enter email or phone number"
          />
        </div> */}

        {/* Register with an phone number   */}
        {/* <div className='form_group'>
          <label className='form_label' htmlFor="username">Enter a Phone number:</label>
          <PhoneNumberInput
            defaultCountry="lk"
            onlyCountries={['lk', 'us']}
            pending={mutation.isPending}
            value={usernameValue || ''}
            onChange={(value: string) => handleInputChange(value)}
            placeholder='Enter phone number'
          />
          {errors.username && <p className='form_error'>{errors.username.message} &#128543; Eg: +***********</p>}
        </div> */}

        <button type='submit' disabled={mutation.isPending}>
          {mutation.isPending ? (
            // <img src={loading_svg} alt="Loading..." className='loading_svg' />
            <p>Continuing...</p>
          ) : (
            'Continue'
          )}
        </button>
        <p className={styles.login_or_register}>
          Already have an account?
          <Link href='/login'> Login</Link>
        </p>
      </form>
    </AuthLayout>
  )
}

export default InitiateRegistration
