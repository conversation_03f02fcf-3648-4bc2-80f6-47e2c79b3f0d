@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.loading_container {
  padding: 2rem 0;
}

// Skeleton animation
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton_item,
.thumbnail_skeleton,
.main_image_skeleton,
.title_skeleton,
.rating_skeleton,
.price_skeleton,
.original_price_skeleton,
.variant_title_skeleton,
.variant_skeleton,
.quantity_label_skeleton,
.quantity_controls_skeleton,
.button_skeleton,
.tab_skeleton,
.content_line_skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: $border-radius-1;
}

// Breadcrumb skeleton
.breadcrumb_skeleton {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
  margin-bottom: 2rem;

  .skeleton_item {
    height: 20px;
    width: 80px;
  }

  span {
    color: $primary-lighter-text-color;
  }
}

// Product details skeleton
.product_details_skeleton {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Image section
.image_section {
  display: grid;
  grid-template-columns: 12% 88%;
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
  }
}

.thumbnails {
  @include flexbox(center, center, column);
  gap: 0.5rem;

  @media (max-width: 768px) {
    flex-direction: row;
    order: 2;
  }
}

.thumbnail_skeleton {
  width: 60px;
  height: 60px;
}

.main_image_skeleton {
  width: 100%;
  height: 400px;

  @media (max-width: 768px) {
    order: 1;
    height: 300px;
  }
}

// Info section
.info_section {
  padding: 1rem 0;
}

.title_skeleton {
  height: 32px;
  width: 80%;
  margin-bottom: 1rem;
}

.rating_skeleton {
  height: 20px;
  width: 150px;
  margin-bottom: 1rem;
}

.price_section {
  margin: 1rem 0;
  @include flexbox(flex-start, baseline);
  gap: 1rem;
}

.price_skeleton {
  height: 28px;
  width: 100px;
}

.original_price_skeleton {
  height: 20px;
  width: 80px;
}

.variants_section {
  margin: 1.5rem 0;
}

.variant_title_skeleton {
  height: 20px;
  width: 120px;
  margin-bottom: 0.5rem;
}

.variants_skeleton {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
  flex-wrap: wrap;
}

.variant_skeleton {
  height: 60px;
  width: 80px;
}

.quantity_section {
  margin: 1.5rem 0;
}

.quantity_label_skeleton {
  height: 20px;
  width: 80px;
  margin-bottom: 0.5rem;
}

.quantity_controls_skeleton {
  height: 32px;
  width: 120px;
}

.buttons_section {
  margin: 2rem 0;
  @include flexbox(flex-start, center);
  gap: 1rem;
  flex-wrap: wrap;

  @media (max-width: 425px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.button_skeleton {
  height: 48px;
  flex: 1;
  min-width: 150px;

  @media (max-width: 425px) {
    width: 100%;
  }
}

// Tabs skeleton
.tabs_skeleton {
  margin: 2rem 0;
}

.tabs_header_skeleton {
  @include flexbox(flex-start, center);
  gap: 2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e7e7e7;
  padding-bottom: 1rem;
}

.tab_skeleton {
  height: 24px;
  width: 100px;
}

.tab_content_skeleton {
  padding: 1rem 0;
}

.content_line_skeleton {
  height: 16px;
  margin-bottom: 0.5rem;

  &:nth-child(1) {
    width: 100%;
  }

  &:nth-child(2) {
    width: 90%;
  }

  &:nth-child(3) {
    width: 85%;
  }

  &:nth-child(4) {
    width: 70%;
  }
}