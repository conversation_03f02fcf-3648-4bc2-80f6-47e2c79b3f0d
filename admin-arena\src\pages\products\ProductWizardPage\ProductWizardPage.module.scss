@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerLeft {
  @include flex-start;
  gap: $spacing-4;
  flex: 1;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-3;
  }
}

.backButton {
  @include flex-center;
  gap: $spacing-2;
  color: $gray-600;

  &:hover {
    color: $gray-900;
  }
}

.titleSection {
  flex: 1;
}

.title {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.subtitle {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-base;
}

.headerIcon {
  @include flex-center;
  width: 64px;
  height: 64px;
  background-color: $primary-100;
  border-radius: $border-radius-lg;
  color: $primary-600;

  svg {
    width: 32px;
    height: 32px;
  }

  @include mobile-only {
    width: 48px;
    height: 48px;

    svg {
      width: 24px;
      height: 24px;
    }
  }
}

.wizardContainer {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: $spacing-6;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.stepsCard {
  height: fit-content;
  position: sticky;
  top: $spacing-6;

  @include mobile-only {
    position: static;
  }
}

.steps {
  @include flex-column;
  gap: $spacing-1;
}

.step {
  @include flex-start;
  gap: $spacing-3;
  padding: $spacing-4;
  border-radius: $border-radius;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: $gray-50;
  }

  &.active {
    background-color: $primary-50;
    border: 1px solid $primary-200;
  }

  &.completed {
    background-color: $green-50;

    &:hover {
      background-color: $green-100;
    }
  }
}

.stepNumber {
  @include flex-center;
  width: 32px;
  height: 32px;
  border-radius: $border-radius-full;
  background-color: $gray-200;
  color: $gray-600;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  flex-shrink: 0;

  .step.active & {
    background-color: $primary-500;
    color: white;
  }

  .step.completed & {
    background-color: $green-500;
    color: white;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.stepContent {
  flex: 1;
  min-width: 0;
}

.stepTitle {
  margin: 0 0 $spacing-1 0;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $gray-900;
}

.stepDescription {
  margin: 0;
  font-size: $font-size-xs;
  color: $gray-600;
  line-height: 1.4;
}

.contentCard {
  min-height: 600px;
  @include flex-column;
}

.stepHeader {
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;
  margin-bottom: $spacing-6;

  .stepTitle {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
  }

  .stepDescription {
    margin: 0;
    font-size: $font-size-base;
    color: $gray-600;
  }
}

.stepContent {
  flex: 1;
  margin-bottom: $spacing-6;
}

.stepActions {
  @include flex-between;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;

  @include mobile-only {
    flex-direction: column-reverse;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }

  .title {
    font-size: $font-size-xl;
  }

  .wizardContainer {
    grid-template-columns: 1fr;
  }

  .steps {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: $spacing-2;
    gap: $spacing-2;
  }

  .step {
    flex-shrink: 0;
    min-width: 200px;
    padding: $spacing-3;
  }

  .stepNumber {
    width: 28px;
    height: 28px;
    font-size: $font-size-xs;
  }

  .stepTitle {
    font-size: $font-size-xs;
  }

  .stepDescription {
    display: none;
  }
}

// Add placeholder styles for development
.placeholder {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-500;
  text-align: center;

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

// Enhanced step navigation styles
.steps {
  .step {
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover:not(.active):not(.completed) {
      background: $gray-50;
    }

    &.completed {
      .stepNumber {
        background: $success-500;
        color: white;
      }

      .stepTitle {
        color: $success-700;
      }
    }

    &.active {
      background: $primary-25;
      border-color: $primary-300;

      .stepNumber {
        background: $primary-500;
        color: white;
      }

      .stepTitle {
        color: $primary-900;
      }
    }
  }
}