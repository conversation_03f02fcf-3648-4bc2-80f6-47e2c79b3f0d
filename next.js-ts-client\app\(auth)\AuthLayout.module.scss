@use '../../src/scss/variables' as *;
@use '../../src/scss/mixins' as *;
@use '../../src/scss/animations' as *;

.register_container {
  background-image: url(../../public/images/pc_hardware.jpg);
  background-color: #70829e;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  overflow: hidden; // Add this to prevent the blur from extending outside

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    z-index: 0; // Change this to 0
  }

  // Add this new rule
  > * {
    position: relative;
    z-index: 1;
  }
}

.form_container {
  width: 400px;
  box-shadow: #0000000d 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset;
  padding: $padding-5;
  background-color: #fff;
  border-radius: $border-radius-1;
  // @include slideInAnimation(0.8s, ease-out);
  @include slideInAnimation();

  button {
    margin: 1.5rem auto 1rem auto;
    @include btn(#fff, $primary-blue);
    width: 100%;
    padding: 8px 0;
    transition: all 0.3s ease-in;

    &:hover {
      background-color: darken($primary-blue, 10%);
    }
  }
}

@media (width > $tablet) {
  .register_container {
    padding-top: 4rem;
  }

  .form_container {
    padding: 25px;
  }
}
