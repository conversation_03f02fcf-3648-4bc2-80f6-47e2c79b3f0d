@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.dragIndicator {
  @include drag-handle;
  @include flex-center;
  gap: $spacing-xs;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: rgba($primary-500, 0.1);
    border-radius: $border-radius-sm;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    background-color: rgba($primary-500, 0.05);

    &::before {
      opacity: 1;
    }

    .dragText {
      opacity: 1;
    }
  }

  svg {
    width: 16px;
    height: 16px;
    color: $text-placeholder;
    transition: color 0.2s ease;
  }

  &:hover svg {
    color: $primary-500;
  }
}

.dragText {
  @include text-xs;
  color: $text-placeholder;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Variants
.default {
  padding: $spacing-sm $spacing-md;
  min-width: 60px;
}

.compact {
  padding: $spacing-xs;
  width: 24px;
  height: 24px;

  svg {
    width: 14px;
    height: 14px;
  }
}

.minimal {
  padding: $spacing-xs;
  width: 20px;
  height: 20px;

  svg {
    width: 12px;
    height: 12px;
  }
}

// Responsive
@media (max-width: 768px) {
  .dragIndicator {
    &.default {
      padding: $spacing-xs $spacing-sm;
      min-width: 50px;
    }

    .dragText {
      display: none;
    }
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .dragIndicator {
    transition: none;

    &::before {
      transition: none;
    }

    svg {
      transition: none;
    }

    .dragText {
      transition: none;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .dragIndicator {
    border: 1px solid $border-color;

    &:hover {
      border-color: $primary-500;
      background-color: $primary-50;
    }

    svg {
      color: $text-primary;
    }

    &:hover svg {
      color: $primary-700;
    }
  }
}