'use client'

import { AxiosError } from 'axios'
import debounce from 'lodash/debounce'
import { useEffect, useMemo, useState } from 'react'
import styles from './FilterOptions.module.scss'
import filterStore from '@/src/stores/filter-store'
import { useProductFilterOptions } from '@/src/hooks/product-hooks'
import { ErrorResponse } from '@/src/types/types'
import Alert from '@/src/components/utils/alert/Alert'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import FilterSkeleton from '../skeletons/FilterSkeleton'

const FilterOptions = () => {
  // Access filter state and update function from the filterStore
  const { selectedFilters, updateFilter, productTypeId } = filterStore()

  // Fetch filter options data using a custom hook
  // This will only fetch when productTypeId changes (i.e., different category/product type)
  // and will use cached data for subsequent visits to the same category
  const { data: filters, isPending, error } = useProductFilterOptions(productTypeId)

  // Track if we've ever had filters loaded to prevent unnecessary skeleton display
  const [hasLoadedFilters, setHasLoadedFilters] = useState(false)

  // Update hasLoadedFilters when filters are available
  useEffect(() => {
    if (filters) {
      setHasLoadedFilters(true)
    }
  }, [filters])

  // Reset hasLoadedFilters when productTypeId changes (new category)
  useEffect(() => {
    setHasLoadedFilters(false)
  }, [productTypeId])

  // Local state for price range to provide immediate UI feedback
  const [localPriceRange, setLocalPriceRange] = useState<[number, number]>([0, 0])

  // Update local price range when filters or selected filters change
  useEffect(() => {
    if (filters) {
      const priceRange = selectedFilters.price_range as [number, number] | undefined
      setLocalPriceRange([
        priceRange?.[0] ?? filters.price_range.min,
        priceRange?.[1] ?? filters.price_range.max,
      ])
    }
  }, [filters, selectedFilters.price_range])

  // Memoize the debounced function to prevent unnecessary re-creation
  const debouncedUpdateFilter = useMemo(
    () => debounce((filterName: string, value: [number, number]) => {
      updateFilter(filterName, value)
    }, 1000),
    [updateFilter]
  )

  // Handle price range changes
  const handlePriceRangeChange = (minOrMax: 'min' | 'max', value: number) => {
    const updatedPriceRange: [number, number] = minOrMax === 'min'
      ? [
        Math.min(value, localPriceRange[1]), // Ensure min doesn't exceed max
        localPriceRange[1]
      ]
      : [
        localPriceRange[0],
        Math.max(value, localPriceRange[0]) // Ensure max doesn't go below min
      ]

    setLocalPriceRange(updatedPriceRange)
    debouncedUpdateFilter('price_range', updatedPriceRange)
  }

  // Handle checkbox changes for multi-select filters
  const handleCheckboxChange = (filterName: string, value: string | number) => {
    const currentValues = selectedFilters[filterName] as (string | number)[] || []
    const updatedValues = currentValues.includes(value)
      ? currentValues.filter((v) => v !== value)
      : [...currentValues, value]
    updateFilter(filterName, updatedValues)
  }

  // Handle radio button changes for single-select filters
  const handleRadioChange = (filterName: string, value: string | number) => {
    updateFilter(filterName, value)
  }

  // Handle label clicks for checkboxes and radio buttons
  const handleLabelClick = (event: React.MouseEvent<HTMLLabelElement>) => {
    const inputElement = event.currentTarget.previousElementSibling as HTMLInputElement

    if (inputElement.type === 'checkbox') {
      // Toggle checkbox
      inputElement.checked = !inputElement.checked
      handleCheckboxChange(inputElement.name, inputElement.value)
    } else if (inputElement.type === 'radio') {
      // Check radio button and update filter
      if (!inputElement.checked) {
        inputElement.checked = true
        handleRadioChange(inputElement.name, inputElement.value)
      }
    }
  }

  // Case 1: No products in category (productTypeId is 0)
  if (productTypeId === 0) {
    return <div>No filters available for this category.</div>
  }

  // Case 2: Query is fetching data for the first time (show skeleton)
  // Only show skeleton if we're loading for the first time AND don't have any cached data
  // AND we haven't loaded filters before in this session
  // This prevents skeleton from showing when products are loading but filters are already available
  if (isPending && !filters && !hasLoadedFilters) {
    return <FilterSkeleton />
  }

  // Case 3: Error occurred
  if (error) {
    return <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} />
  }

  // Case 4: Data is available (show filters)
  if (filters) {
    return (
      <div className={styles.filter_options}>
        <section className={styles.primary_filters}>
          {/* Price filter */}
          <div>
            <h3>Price <span>(${filters.price_range.min}</span> - <span>${filters.price_range.max})</span></h3>
            <div className={styles.price_range_sliders}>
              <div>
                <span>Min: </span>
                <input
                  type="range"
                  min={filters.price_range.min}
                  max={filters.price_range.max}
                  step={0.1}
                  value={localPriceRange[0]}
                  onChange={(e) => handlePriceRangeChange('min', Number(e.target.value))}
                />
                <label>${localPriceRange[0]}</label>
              </div>
              <div>
                <span>Max: </span>
                <input
                  type="range"
                  min={filters.price_range.min}
                  max={filters.price_range.max}
                  step={0.1}
                  value={localPriceRange[1]}
                  onChange={(e) => handlePriceRangeChange('max', Number(e.target.value))}
                />
                <label>${localPriceRange[1]}</label>
              </div>
            </div>
          </div>
          {/* Condition filter */}
          <div>
            <h3>Condition</h3>
            {filters.condition.map(([label, value]) => (
              <div key={value}>
                <input
                  type="radio"
                  name="condition"
                  value={value}
                  checked={selectedFilters.condition === value}
                  onChange={() => handleRadioChange('condition', value)}
                />
                <label onClick={handleLabelClick}>{label}</label>
              </div>
            ))}
          </div>
          {/* Brands filter */}
          <div>
            <h3>Brands</h3>
            {filters.brands.map((brand) => (
              <div key={brand.id}>
                <input
                  type="checkbox"
                  name="brand"
                  value={brand.slug}
                  checked={(selectedFilters.brand as string[] | undefined)?.includes(brand.slug)}
                  onChange={() => handleCheckboxChange('brand', brand.slug)}
                />
                <label htmlFor={`brand-${brand.id}`} onClick={handleLabelClick}>{brand.slug}</label>
              </div>
            ))}
          </div>
        </section>
        {/* Attribute filters */}
        <section className={styles.attribute_filters}>
          {Object.entries(filters.attribute_filters).map(([attribute, values]) => (
            <div key={attribute} className="filter-section">
              <h3>{attribute}</h3>
              {values.map((value) => (
                <div key={value}>
                  <input
                    type="checkbox"
                    name={attribute}
                    value={value}
                    checked={(selectedFilters[attribute] as string[] | undefined)?.includes(value)}
                    onChange={() => handleCheckboxChange(attribute, value)}
                  />
                  <label onClick={handleLabelClick}>{value}</label>
                </div>
              ))}
            </div>
          ))}
        </section>
      </div>
    )
  }

  // Case 5: No filters returned for a valid productTypeId (unlikely but possible)
  return <div>No filters available.</div>
}

export default FilterOptions
