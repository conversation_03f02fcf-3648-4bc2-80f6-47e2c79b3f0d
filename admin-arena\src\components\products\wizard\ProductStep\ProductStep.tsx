// Product information step in product wizard
// Allows entering basic product details like name, description, etc.

import React from 'react'
import { FiPackage, FiInfo } from 'react-icons/fi'
import { Card } from '../../../ui/Card'
import { Input } from '../../../ui/Input'
import { Textarea } from '../../../ui/Textarea'
import { Switch } from '../../../ui/Switch'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useEffect } from 'react'
import styles from './ProductStep.module.scss'

interface ProductStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  is_active: z.boolean(),
})

type ProductFormData = z.infer<typeof productSchema>

export const ProductStep: React.FC<ProductStepProps> = ({ data, onUpdate }) => {
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: data.product?.name || '',
      slug: data.product?.slug || '',
      description: data.product?.description || '',
      is_active: data.product?.is_active ?? true,
    },
  })

  const watchedValues = watch()

  // Auto-generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  // Update wizard data when form values change
  useEffect(() => {
    const { name, slug, description, is_active } = watchedValues
    
    if (name || slug || description || is_active !== undefined) {
      onUpdate({
        product: {
          name: name || '',
          slug: slug || '',
          description: description || '',
          is_active: is_active ?? true,
        }
      })
    }
  }, [watchedValues, onUpdate])

  // Auto-generate slug when name changes
  useEffect(() => {
    const name = watchedValues.name
    if (name && (!watchedValues.slug || watchedValues.slug === generateSlug(data.product?.name || ''))) {
      const newSlug = generateSlug(name)
      setValue('slug', newSlug)
    }
  }, [watchedValues.name, watchedValues.slug, setValue, data.product?.name])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Product Information</h3>
          <p>Enter the basic information for your product. This will be visible to customers.</p>
        </div>
      </div>

      <div className={styles.content}>
        <Card className={styles.formCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardIcon}>
              <FiPackage />
            </div>
            <div>
              <h4>Basic Information</h4>
              <p>Essential product details</p>
            </div>
          </div>

          <div className={styles.form}>
            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label htmlFor="name" className={styles.label}>
                  Product Name *
                </label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter product name"
                  error={errors.name?.message}
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="slug" className={styles.label}>
                  URL Slug *
                </label>
                <Input
                  id="slug"
                  {...register('slug')}
                  placeholder="product-url-slug"
                  error={errors.slug?.message}
                />
                <span className={styles.helpText}>
                  This will be used in the product URL
                </span>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="description" className={styles.label}>
                Product Description *
              </label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Enter detailed product description..."
                rows={6}
                error={errors.description?.message}
              />
              <span className={styles.helpText}>
                Provide a detailed description that will help customers understand your product
              </span>
            </div>

            <div className={styles.formGroup}>
              <div className={styles.switchGroup}>
                <div className={styles.switchInfo}>
                  <label htmlFor="is_active" className={styles.label}>
                    Active Product
                  </label>
                  <span className={styles.helpText}>
                    Active products are visible to customers
                  </span>
                </div>
                <Switch
                  id="is_active"
                  {...register('is_active')}
                  defaultChecked={watchedValues.is_active}
                />
              </div>
            </div>
          </div>
        </Card>

        <Card className={styles.summaryCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardIcon}>
              <FiInfo />
            </div>
            <div>
              <h4>Product Summary</h4>
              <p>Review your product information</p>
            </div>
          </div>

          <div className={styles.summary}>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Category:</span>
              <span className={styles.summaryValue}>
                {data.category?.name || 'Not selected'}
              </span>
            </div>

            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Product Type:</span>
              <span className={styles.summaryValue}>
                {data.productType?.name || 'Not selected'}
              </span>
            </div>

            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Brand:</span>
              <span className={styles.summaryValue}>
                {data.brand?.name || 'Not selected'}
              </span>
            </div>

            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Attributes:</span>
              <span className={styles.summaryValue}>
                {data.attributes?.length || 0} selected
              </span>
            </div>

            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Product Name:</span>
              <span className={styles.summaryValue}>
                {watchedValues.name || 'Not entered'}
              </span>
            </div>

            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Status:</span>
              <span className={`${styles.summaryValue} ${watchedValues.is_active ? styles.active : styles.inactive}`}>
                {watchedValues.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
