
'use client'

import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ort } from "react-icons/fa"
import { useParams, useSearchParams, useRouter } from "next/navigation"
import SimpleProductCard from "../../../components/simple-product-card/SimpleProductCard"
import Alert from "../../../../../../src/components/utils/alert/Alert"
import Pagination from "../../../../../../src/components/utils/pagination/Pagination"
import ProductListSkeleton from "../../../components/skeletons/ProductListSkeleton"
import { ITEMS_PER_PAGE } from "../../../../../../src/constants/constants"
import { useProducts } from "../../../../../../src/hooks/product-hooks"
import { ProductShape } from "../../../../../../src/types/product-types"
import filterStore from "../../../../../../src/stores/filter-store"
import styles from './ProductList.module.scss'
import Sorting from "../../../components/sorting/SortBy"
import FilterOptions from "../../../components/filters/FilterOptions"

const ProductList = () => {
  const params = useParams()
  const slug = params.slug as string
  const router = useRouter()
  const searchParams = useSearchParams()

  const [showSorting, setShowSorting] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const { setCurrentCategory, setCategoryProductType, shouldFetchFilters } = filterStore()

  const page = parseInt(searchParams.get('page') || '1', 10)
  const searchQuery = searchParams.get('search') || ''

  const { isPending, error, data: products } = useProducts(slug, page, searchParams.toString(), searchQuery)

  // Handle category changes
  useEffect(() => {
    // Set current category and reset filters if category changed
    setCurrentCategory(slug)
    setShowFilters(false)
    setShowSorting(false)
  }, [slug, setCurrentCategory])

  // Set the product type ID when products are loaded (only if not cached)
  useEffect(() => {
    if (products?.results.length) {
      const productTypeId = products.results[0].product_type
      // Only update if we don't have this category cached or if it's different
      if (shouldFetchFilters(slug)) {
        setCategoryProductType(slug, productTypeId)
      }
    }
  }, [products, slug, shouldFetchFilters, setCategoryProductType])

  const toggleFilters = () => setShowFilters(!showFilters)
  const toggleSorting = () => setShowSorting(!showSorting)

  const handlePageChange = (newPage: number) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()))
    current.set('page', newPage.toString())
    const search = current.toString()
    const query = search ? `?${search}` : ''
    router.push(`/products/category/${slug}${query}`)
  }

  // Show skeleton loading when data is being fetched
  if (isPending) {
    return (
      <div className='container'>
        <div className={styles.product_list__container}>
          {/* Mobile controls for filters and sorting */}
          <div className={styles.mobile_controls}>
            <button onClick={toggleFilters}>
              {!showFilters ? <><FaFilter /> Filters</> : <span>Close Filters</span>}
            </button>
            <button onClick={toggleSorting}>
              <FaSort /> Sort
            </button>
          </div>

          {/* Filters section - keep filters visible when products are loading */}
          <section className={`${styles.filters} ${showFilters ? styles.show : ''}`}>
            <FilterOptions />
          </section>

          <section className={styles.product_list__wrapper}>
            {/* Sorting section skeleton */}
            <div className={`${styles.sorting} ${showSorting ? styles.show : ''}`}>
              <div className={styles.sort_select_skeleton}></div>
            </div>

            {/* Product list skeleton */}
            <ul className={styles.product_list}>
              {[...Array(12)].map((_, i) => (
                <li key={i}>
                  <div className={styles.product_card_skeleton}>
                    <div className={styles.image_skeleton}></div>
                    <div className={styles.info_skeleton}>
                      <div className={styles.title_skeleton}></div>
                      <div className={styles.rating_skeleton}></div>
                      <div className={styles.price_skeleton}>
                        <div className={styles.current_price_skeleton}></div>
                        <div className={styles.original_price_skeleton}></div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>

            {/* Pagination skeleton */}
            <div className={styles.pagination_skeleton}>
              <div className={styles.pagination_button_skeleton}></div>
              {[...Array(5)].map((_, i) => (
                <div key={i} className={styles.pagination_number_skeleton}></div>
              ))}
              <div className={styles.pagination_button_skeleton}></div>
            </div>
          </section>
        </div>
      </div>
    )
  }

  return (
    <div className='container'>
      <div className={styles.product_list__container}>
        {/* Mobile controls for filters and sorting */}
        <div className={styles.mobile_controls}>
          <button onClick={toggleFilters}>
            {!showFilters ? <><FaFilter /> Filters</> : <span>Close Filters</span>}
          </button>
          {products && products.results.length > 1 && <button onClick={toggleSorting}>
            <FaSort /> Sort
          </button>}
        </div>

        {/* Filters section */}
        <section className={`${styles.filters} ${showFilters ? styles.show : ''}`}>
          <FilterOptions />
        </section>

        <section className={styles.product_list__wrapper}>
          {/* Sorting section */}
          <div className={`${styles.sorting} ${showSorting ? styles.show : ''}`}>
            {products && products.results.length > 1 && <Sorting />}
          </div>

          {/* Product list */}
          {error ? (
            <Alert variant='error' message={error.message} />
          ) : (
            <>
              <ul className={styles.product_list}>
                {products?.results.length === 0 ? (
                  <p className={styles.empty_products}>No products found.</p>
                ) : (
                  products?.results.map((product: ProductShape) => (
                    <li key={product.slug}>
                      <SimpleProductCard product={product} />
                    </li>
                  ))
                )}
              </ul>
              {/* Add Pagination */}
              {products && products.count > 0 && (
                <Pagination
                  currentPage={page}
                  totalPages={Math.ceil(products.count / ITEMS_PER_PAGE)}
                  onPageChange={handlePageChange}
                />
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

export default ProductList