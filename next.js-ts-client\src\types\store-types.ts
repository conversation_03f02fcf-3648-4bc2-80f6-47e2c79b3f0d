import { AddressFormInputs } from '../components/account/addresses/ManageAddresses'
import { AttributeValue, PaymentOptionsShape } from './types'

// export interface FilterStoreShape {
//   productTypeId: number
//   setProductTypeId: (proType: number) => void
//   // ------------ //
//   // filters: {}

//   // ------------ //
//   primaryFilters: []
//   attributeFilters: Filters
//   updateAttributeFilters: (data: Filters) => void
//   updatePrimaryFilters: (data: []) => void
//   resetAttributeFilters: () => void
//   resetPrimaryFilters: () => void
// }

interface ProductImage {
  id: number
  image: string
  alternative_text: string
  order: number
}

export interface ExtraData {
  [key: string]: string
}

interface ProductVariant {
  id: number
  is_active: boolean
  order: number
  price: number
  price_label: string
  price_label_attr_title: string
  product_image: ProductImage[]
  sku: string
  stock_qty: number
  attribute_value: AttributeValue[]
}

export interface CartItem {
  cart_item_id: string | null
  product_id: string | null
  product_variant: ProductVariant | null
  quantity: number | null
  extra_data: ExtraData
}

interface SimpleCartItem {
  id: number | null
  product_id: number | null
  product_variant: number | null
  quantity: number | null
  extra_data: ExtraData
}

export interface AddressShape {
  id: number
  full_name: string
  street_name: string
  address_line_1?: string
  address_line_2?: string
  postal_code: string
  city_or_village: string
  state_or_region: string
  country: string
  country_code: string
}

export interface CustomerShape {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number: string
  birth_date: string
  address?: AddressFormInputs[]
}

export interface CartItemShape {
  id: number
  product: {
    id: number
    title: string
    slug: string
  }
  product_variant: {
    id: number
    price: number
    price_label: {
      id: number
      attribute_value: string
    }
    product_image: [
      {
        id: number
        image: string
        alternative_text: string
      }
    ]
    stock_qty: number
  }
  extra_data: string
  qty_price: number
  quantity: number
  is_selected: boolean
}

export interface CartStoreShape {
  cartId: string | null
  selectedVariant: ProductVariant | null
  cartItem: SimpleCartItem
  customer: CustomerShape | null
  selectedAddress: AddressFormInputs | null
  // paymentOptions: string[]
  selectedPaymentOption: PaymentOptionsShape | null

  // Selection state
  // serializable selection state (persist-safe)
  selectedItemIds: number[]
  selectAllItems: boolean

  setCustomer: (customer: CustomerShape) => void
  setSelectedAddress: (address: AddressFormInputs) => void
  // setPaymentOptions: (options: string[]) => void
  setSelectedPaymentOption: (paymentOption: PaymentOptionsShape) => void

  setCartId: (newCartId: string | null) => void
  // setCartItems: (cartItemData: CartItem[]) => void

  setCartItemId: (cart_item_id: number) => void
  // setProductId: (product_id: number | null) => void
  setProductVariant: (product_variant: ProductVariant) => void
  // setQuantity: (quantity: number | null) => void
  setExtraData: (extra_data: ExtraData) => void
  resetExtraData: () => void

  // Selection methods
  toggleItemSelection: (itemId: number) => void
  toggleSelectAll: (allItemIds: number[]) => void
  clearSelection: () => void
  setSelectedItems: (itemIds: number[]) => void
  isItemSelected: (itemId: number) => boolean
}
