@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.stripe_form {
  .pay_btn {
    @include btn(#fff, $lighten-blue);
    width: 100%;
    margin: 1rem 0 0 0;
    padding: 0.5rem 0;
    transition: all 0.2s ease-in;

    p {
      font-size: 18px;
      font-weight: bold;
    }

    &:hover {
      background-color: darken($lighten-blue, 10%);
      // color: darken(#fff, 10%);
    }
  }

  .error_message {
    padding: 1rem 0 0 0;
    color: $error-red;
    text-align: center;
  }
}
