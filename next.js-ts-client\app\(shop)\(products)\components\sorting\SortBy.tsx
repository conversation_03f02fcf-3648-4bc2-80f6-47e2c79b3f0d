'use client'

import React from 'react'
import { useSearch<PERSON>ara<PERSON>, useRouter, useParams } from 'next/navigation'
import styles from './SortBy.module.scss'

// Define sorting options
const sortOptions = [
  { value: 'title_asc', label: 'Title (A-Z)' },
  { value: 'title_desc', label: 'Title (Z-A)' },
  { value: 'price_asc', label: 'Price (Low to High)' },
  { value: 'price_desc', label: 'Price (High to Low)' },
]

const Sorting = () => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const params = useParams()
  const slug = params.slug as string

  // Handle changes in the sorting select element
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()))

    if (e.target.value === '') {
      // Remove the 'sort_by' parameter if the default option is selected
      current.delete('sort_by')
    } else {
      // Set the 'sort_by' parameter with the selected value
      current.set('sort_by', e.target.value)
    }

    const search = current.toString()
    const query = search ? `?${search}` : ''
    router.push(`/products/category/${slug}${query}`)
  }

  return (
    <div className={styles.sorting}>
      <select
        id="sort-select"
        className={styles.select}
        value={searchParams.get('sort_by') || ''}
        onChange={handleSortChange}
      >
        <option value="">Sort by: Default</option>
        {sortOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}

export default Sorting
