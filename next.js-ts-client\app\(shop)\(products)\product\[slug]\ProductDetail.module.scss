@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.breadcrumbs {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
  margin: 1rem 0 2rem 0;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;

  a {
    color: $primary-blue;
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: color.adjust($primary-blue, $lightness: -10%);
      text-decoration: underline;
    }
  }

  span {
    color: $primary-dark-text-color;
    font-weight: 500;
  }
}

  @media (width > $mobile) {
    .breadcrumbs {
      font-size: $font-size-3;
    }
  }