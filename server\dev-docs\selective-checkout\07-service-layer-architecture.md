# Service Layer Architecture for Cart Operations

## Overview

This document outlines the refactored service layer architecture implemented for the cart application. The refactoring addresses the following key issues:

- Business logic was embedded directly in views and serializers
- Inconsistent error handling across the application
- Lack of proper separation of concerns
- Focus on all cart items instead of selected items only

## Service Layer Structure

```
apps/cart/services/
├── __init__.py
├── base.py                 # Base service class with error handling
├── cart_service.py         # Cart calculation service
├── selection_service.py    # Cart item selection service
├── validation_service.py   # Cart validation service
└── shipping_service.py     # Cart shipping service wrapper
```

## Service Classes

### BaseCartService

**Location**: `apps/cart/services/base.py`

Provides common functionality and consistent error handling patterns for all cart services.

**Key Features**:
- Standardized error handling with logging
- Transaction management support
- Consistent response formats
- Field validation utilities

**Methods**:
- `handle_service_error()` - Standardized error handling
- `execute_with_error_handling()` - Execute functions with error handling
- `validate_required_fields()` - Validate required fields
- `create_success_response()` - Create standardized success responses
- `create_error_response()` - Create standardized error responses

### CartCalculationService

**Location**: `apps/cart/services/cart_service.py`

Handles all cart-related calculations focused on selected cart items only.

**Key Methods**:
- `calculate_selected_totals(cart)` - Calculate comprehensive totals for selected items
- `calculate_item_total_price(cart_item)` - Calculate total price for individual items
- `check_all_items_selected(cart)` - Check if all items are selected

**Returns**:
```python
{
    'total_price': Decimal,
    'total_weight': Decimal,
    'total_volume': Decimal,
    'item_count': int,
    'shipping_cost': Decimal,
    'packing_cost': Decimal,
    'grand_total': Decimal,
    'has_selected_items': bool
}
```

### CartSelectionService

**Location**: `apps/cart/services/selection_service.py`

Manages cart item selection operations including individual and bulk operations.

**Key Methods**:
- `toggle_item_selection(cart_item, is_selected)` - Toggle individual item selection
- `bulk_select_items(cart, item_ids=None)` - Bulk select items
- `bulk_deselect_items(cart, item_ids=None)` - Bulk deselect items
- `get_selection_summary(cart)` - Get selection state summary
- `validate_selection_for_checkout(cart)` - Validate selection for checkout

### CartValidationService

**Location**: `apps/cart/services/validation_service.py`

Handles cart validation logic including item availability and business rules.

**Key Methods**:
- `validate_cart_for_checkout(cart)` - Comprehensive cart validation
- `validate_cart_item(cart_item)` - Validate individual cart item
- `validate_add_to_cart(product_variant, quantity)` - Validate adding items

**Validation Checks**:
- Product variant availability
- Stock availability
- Quantity limits (min/max)
- Business rules (minimum order value, weight limits)

### CartShippingService

**Location**: `apps/cart/services/shipping_service.py`

Wrapper service that integrates with existing shipping services but focuses on selected cart items only.

**Key Methods**:
- `calculate_shipping_for_selected_items(cart, destination_address, get_all_options)` - Calculate shipping for selected items
- `get_shipping_estimate_for_selected_items(cart, destination_zone)` - Get shipping estimate
- `validate_shipping_calculation(cart)` - Validate shipping calculation
- `clear_shipping_calculation(cart)` - Clear shipping data

## Integration with Models

### Cart Model Changes

**Removed Methods**:
- `get_cart_weight()` - Calculated on all items (deprecated)
- Methods that focus on all items instead of selected items

**Added Properties**:
- `selected_totals` - Property that uses CartCalculationService

**Kept Methods**:
- `get_selected_cart_weight()` - Weight of selected items
- `get_selected_items_count()` - Count of selected items
- `get_all_items_selected()` - Check if all items selected

### CartItem Model Changes

**Removed Methods**:
- `get_total_item_price()` - Moved to CartCalculationService

**Kept Methods**:
- `update_selection()` - Update selection state

## Serializer Refactoring

### CartSerializer

Now uses service layer for all calculations:
```python
def get_selected_total_price(self, cart):
    return cart.selected_totals['total_price']
```

### CartWithShippingSerializer

Focused on selected items only:
- Removed deprecated fields for all items
- Uses service layer for calculations
- Consistent with selected items focus

### CartItemSerializer

Uses service layer for price calculation:
```python
def get_qty_price(self, cart_item):
    from .services.cart_service import CartCalculationService
    calc_service = CartCalculationService()
    return calc_service.calculate_item_total_price(cart_item)
```

## View Refactoring

### CartViewSet

Views now delegate to service layer:
- `selected_summary()` - Uses CartSelectionService and CartCalculationService
- `bulk_select()` - Uses CartSelectionService
- `bulk_deselect()` - Uses CartSelectionService
- `calculate_shipping()` - Uses CartShippingService

### Error Handling

All views now use consistent error handling patterns from the service layer.

## Order Creation Integration

### CreateOrderSerializer

Updated to use service layer:
```python
# Validation
cart_validation = validation_service.validate_cart_for_checkout(cart)
shipping_validation = shipping_service.validate_shipping_calculation(cart)

# Calculations
totals = calc_service.calculate_selected_totals(cart)
item_total_price = calc_service.calculate_item_total_price(cart_item)
```

## Benefits of Service Layer Architecture

### 1. Separation of Concerns
- Views handle only HTTP concerns
- Serializers handle only data transformation
- Services handle business logic

### 2. Consistent Error Handling
- Standardized error responses
- Comprehensive logging
- Transaction management

### 3. Selected Items Focus
- All calculations focus on selected items
- Consistent behavior across the application
- Proper selective checkout support

### 4. Maintainability
- Business logic centralized in services
- Easy to test and modify
- Clear dependencies and interfaces

### 5. Reusability
- Services can be used across different parts of the application
- Consistent behavior regardless of entry point

## Usage Examples

### Calculate Selected Totals
```python
from apps.cart.services import CartCalculationService

calc_service = CartCalculationService()
totals = calc_service.calculate_selected_totals(cart)
```

### Bulk Select Items
```python
from apps.cart.services import CartSelectionService

selection_service = CartSelectionService()
result = selection_service.bulk_select_items(cart, item_ids=[1, 2, 3])
```

### Validate Cart for Checkout
```python
from apps.cart.services import CartValidationService

validation_service = CartValidationService()
validation_result = validation_service.validate_cart_for_checkout(cart)
```

## Migration Notes

### Deprecated Fields/Methods
The following fields and methods have been removed or deprecated:
- Cart model methods that calculate on all items
- CartItem.get_total_item_price() method
- Serializer methods with embedded business logic

### New Dependencies
- All cart operations now depend on the service layer
- Import services from `apps.cart.services`
- Use service methods instead of direct model calculations

## Testing Considerations

### Service Testing
- Test each service independently
- Mock dependencies where appropriate
- Test error handling scenarios

### Integration Testing
- Test service integration with views and serializers
- Verify selected items focus
- Test transaction handling

## Future Enhancements

### Potential Improvements
1. Add caching layer for expensive calculations
2. Implement async processing for heavy operations
3. Add more sophisticated business rules
4. Enhance error recovery mechanisms
5. Add performance monitoring and metrics
