// Global error handlers for Next.js 15 compatibility issues

export function setupGlobalErrorHandlers() {
  if (typeof window === 'undefined') return

  // Handle unhandled promise rejections (common with streaming issues)
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason?.message?.includes('transformAlgorithm')) {
      console.warn('🚨 Caught stream controller error in unhandled rejection:', event.reason)
      // Prevent the error from being logged as unhandled
      event.preventDefault()
    }
  })

  // Handle general errors
  window.addEventListener('error', (event) => {
    if (event.error?.message?.includes('transformAlgorithm')) {
      console.warn('🚨 Caught stream controller error in global handler:', event.error)
      // Prevent the error from being logged
      event.preventDefault()
    }
  })
}

// Utility to wrap fetch calls with better error handling
export async function safeFetch(url: string, options?: RequestInit): Promise<Response> {
  try {
    const response = await fetch(url, options)
    return response
  } catch (error) {
    if (error instanceof Error && error.message.includes('transformAlgorithm')) {
      console.warn('🚨 Stream controller error in fetch, retrying with different approach...')
      // Retry with different fetch options
      return fetch(url, {
        ...options,
        // Disable streaming for this request
        cache: 'no-store',
      })
    }
    throw error
  }
}