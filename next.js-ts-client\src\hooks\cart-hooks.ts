import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import APIClient from '../lib/api-client'
import { CartItemShape, ExtraData } from '../types/store-types'
import { ProductShape } from '../types/product-types'
import cartStore from '../stores/cart-store'
import { CACHE_KEY_CART_ITEMS, SIMPLE_CART } from '../constants/constants'
import {
  CartShape,
  ErrorResponse,
  ShippingCalculationResponse,
  SimpleCartShape,
} from '../types/types'

interface ShippingCalculationReq {
  destination_address_id?: number
  get_all_options?: boolean
}

export interface UpdateQtyShape {
  // itemId: number
  quantity: number
}

interface CreateCartShape {
  customer?: string | null
}

const apiClient = new APIClient<CartResponseShape, CreateCartShape>('/cart/')

interface SimpleCartItemShape {
  // cart_id: string
  product_id: number
  // variant_id: number
  quantity: number
  extra_data: ExtraData
  product_variant: number
}

interface CartResponseShape {
  id: string
  cart_items: CartItemShape[]
  customer: string | null
  cart_weight: number
  total_price: number
  shipping_cost: number
  grand_total: number
}

export const useAddToCart = (product: ProductShape, qty: number) => {
  const queryClient = useQueryClient()
  const { cartId, setCartId, setCartItemId, selectedVariant, cartItem } =
    cartStore()
  const { extra_data } = cartItem

  const apiClient2 = new APIClient<CartResponseShape, SimpleCartItemShape>(
    `/cart/${cartId}/items/`
  )

  console.log(cartItem)
  console.log(extra_data)

  const addToCartMutation = useMutation({
    mutationFn: ({
      product_id,
      product_variant,
      quantity,
      extra_data,
    }: SimpleCartItemShape) =>
      apiClient2.post({
        // cart_id,
        product_id,
        product_variant: product_variant,
        quantity,
        extra_data,
      }),
    onSuccess: (data) => {
      setCartItemId(parseInt(data.id))
      // Only invalidate simple cart cache to avoid triggering expensive useCart queries
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART],
      })
    },
    onError: (error) => {
      console.error('Error adding item to cart:', error)
    },
  })

  const createCartMutation = useMutation<
    CartResponseShape,
    AxiosError<ErrorResponse>,
    CreateCartShape
  >({
    mutationFn: () => apiClient.post({}),
    onSuccess: (data) => {
      if (!selectedVariant) {
        return
      }
      console.log('cart id (res) in createCartMutation', data)
      setCartId(data.id)
      addToCartMutation.mutate({
        // cart_id: data.id,
        product_id: product.id,
        product_variant: selectedVariant.id,
        quantity: qty,
        extra_data: extra_data,
      })
      // Only invalidate simple cart cache to avoid triggering expensive useCart queries
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART],
      })
    },
    onError: (error) => {
      console.error('Error creating cart:', error)
    },
  })

  const handleAddToCart = () => {
    if (!selectedVariant) {
      return // Early return if no variant selected
    }
    if (cartId) {
      addToCartMutation.mutate({
        // cart_id: cartId,
        product_id: product.id,
        product_variant: selectedVariant.id,
        quantity: qty,
        extra_data: extra_data,
      })
    } else {
      createCartMutation.mutate({})
    }
  }

  return {
    handleAddToCart,
    isPending: addToCartMutation.isPending || createCartMutation.isPending,
    error: addToCartMutation.error || createCartMutation.error,
  }
}

export const useCart = () => {
  const { cartId } = cartStore()

  const apiClient = new APIClient<CartShape>(`/cart/${cartId}`)

  return useQuery({
    queryKey: [CACHE_KEY_CART_ITEMS, cartId],
    queryFn: apiClient.get,
    enabled: !!cartId, // Only run the query if cartId is truthy
    // keepPreviousData: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
    staleTime: 0,
  })
}

/**
 * Hook to fetch simple cart data (for cart previews and header displays)
 * 
 * Cache settings are now handled globally in query-client.ts:
 * - staleTime: 1 minute (simple_cart default)
 * - gcTime: 5 minutes
 * - refetchOnWindowFocus: false (disabled for better UX)
 * - Smart retry logic for failed requests
 * 
 * The simple cart is optimized for quick previews and header displays,
 * balancing between freshness and performance.
 */
export const useSimpleCart = () => {
  const { cartId } = cartStore()

  const apiClient = new APIClient<SimpleCartShape>(`/cart/${cartId}/simple`)

  return useQuery({
    queryKey: [SIMPLE_CART, cartId],
    queryFn: apiClient.get,
    enabled: !!cartId, // Only run the query if cartId is truthy
  })
}

export const useDeleteCartItem = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/cart/${cartId}/items`)

  return useMutation({
    mutationFn: (itemId: number) => apiClient.delete(itemId),
    // onMutate: async (itemId) => {
    //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)

    //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)

    //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
    //     const updatedCartItems = oldData?.cart_items.filter(
    //       (item) => item.id !== itemId
    //     )
    //     return { ...oldData, cart_items: updatedCartItems }
    //   })

    //   return { previousCartItems }
    // },

    onSuccess: () => {
      // Invalidate both regular cart and simple cart queries
      // queryClient.invalidateQueries({
      //   queryKey: [CACHE_KEY_CART_ITEMS],
      // })
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART],
      })
    },

    // onError: (err, itemId, context) => {
    //   if (context?.previousCartItems) {
    //     queryClient.setQueryData(CACHE_KEY_CART_ITEMS, context.previousCartItems)
    //   }
    // },

    // onSettled: async () => {
    //   return await queryClient.invalidateQueries({
    //     queryKey: CACHE_KEY_CART_ITEMS
    //   })
    // },

    // onSettled: () => {
    //   queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
    //   // queryClient.invalidateQueries({ queryKey: CACHE_KEY_CART_ITEMS });
    // },
  })
  // const handleDeleteCartItem = (itemId) => {
  //   mutate({ itemId })
  // }

  // return { isPending, isError, mutate }
}

export const useUpdateCart = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  const handleQuantityUpdate = (itemId: number, newQuantity: number) => {
    const cartItemApiClient = new APIClient<UpdateQtyShape>(
      `/cart/${cartId}/items/${itemId}/`
    ) // Item-specific endpoint
    mutation.mutate({ itemId, newQuantity, apiClient: cartItemApiClient })
  }

  const mutation = useMutation<
    UpdateQtyShape,
    AxiosError<ErrorResponse>,
    {
      itemId: number
      newQuantity: number
      apiClient: APIClient<UpdateQtyShape>
    }
  >({
    mutationFn: ({ newQuantity, apiClient }) => {
      // Using the specific APIClient instance for this item
      return apiClient.patch({ quantity: newQuantity })
    },

    onSuccess: () => {
      // Invalidate both regular cart and simple cart queries
      // queryClient.invalidateQueries({
      //   queryKey: [CACHE_KEY_CART_ITEMS],
      // })
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART],
      })
    },
  })

  return { mutation, handleQuantityUpdate }
}

export const useUpdateCartCustomer = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  const apiClient = new APIClient<CartShape>(`/cart/${cartId}/`)

  const mutation = useMutation<CartShape, AxiosError<ErrorResponse>, {}>({
    mutationFn: () => {
      // Send PATCH request to update cart with customer
      return apiClient.patch({})
    },
    onSuccess: () => {
      // Invalidate both regular cart and simple cart queries
      // queryClient.invalidateQueries({
      //   queryKey: [CACHE_KEY_CART_ITEMS],
      // })
      queryClient.invalidateQueries({
        queryKey: [SIMPLE_CART],
      })
    },
    onError: (error) => {
      console.error('Error updating cart customer:', error)
    },
  })

  return {
    updateCartCustomer: mutation.mutate,
    isPending: mutation.isPending,
    error: mutation.error,
    isSuccess: mutation.isSuccess,
  }
}

export const useShippingCalculation = () => {
  const { cartId } = cartStore()

  const apiClient = new APIClient<
    ShippingCalculationResponse,
    ShippingCalculationReq
  >(`/cart/${cartId}/items/calculate_shipping/`)

  const mutation = useMutation<
    ShippingCalculationResponse,
    AxiosError<ErrorResponse>,
    ShippingCalculationReq
  >({
    mutationFn: (data) => apiClient.post(data),
    onSuccess: () => {
      // No need to invalidate cache since we use the response data directly
      // The shipping calculation response already contains the updated cart data
    },
    onError: (error) => {
      console.error('Error calculating shipping:', error)
    },
  })

  return {
    calculateShipping: mutation.mutate,
    isPending: mutation.isPending,
    error: mutation.error,
    isSuccess: mutation.isSuccess,
    data: mutation.data,
  }
}
