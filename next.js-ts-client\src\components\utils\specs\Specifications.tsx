import React from 'react'
import styles from './Specifications.module.scss'

interface ProductVariant {
  id: number
  order: number
  price: number
  price_label_attr_title: string
  price_label: string
  sku: string
  stock_qty: number
  is_active: boolean
  product_image: {
    id: number
    image: string
    alternative_text: string
    order: number
  }[]
  attribute_value: {
    id: number
    attribute: {
      id: number
      title: string
    }
    attribute_value: string
    for_filtering: boolean
    is_active: boolean
    selectable: boolean
  }[]
}

interface SpecificationItem {
  [key: string]: string
}

interface Props {
  selectedVariant: ProductVariant | null // Add this
}

const Specifications = ({ selectedVariant }: Props) => {
  const getSpecifications = (): SpecificationItem[] => {
    const specifications: SpecificationItem[] = []

    // Only show specifications for selected variant
    if (selectedVariant) {
      selectedVariant.attribute_value.forEach((attr) => {
        specifications.push({
          [attr.attribute.title]: attr.attribute_value
        })
      })
    }

    return specifications
  }

  const specifications = getSpecifications()

  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th>Name</th>
          <th>Value</th>
        </tr>
      </thead>
      <tbody>
        {specifications.map((spec, index) => (
          <tr key={index}>
            {Object.entries(spec).map(([key, value]) => (
              <React.Fragment key={key}>
                <td className={styles.t_data}>{key}</td>
                <td className={styles.t_data}>{value}</td>
              </React.Fragment>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )
}

export default Specifications
