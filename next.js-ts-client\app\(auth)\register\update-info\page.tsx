'use client'

import authStore from '@/src/stores/auth-store'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import AuthLayout from '../../AuthLayout'
import UpdateAuthContact from './UpdateAuthContact'
import { VerifyAuthContact } from './VerifyAuthContact'

const UpdateAuthInfoContainer = () => {
  const router = useRouter()
  const [verificationStep, setVerificationStep] = useState(false)
  const [altUsername, setAltUsername] = useState<string | null>(null)

  const {
    username,
    // setIsLoggedIn: login,
    setRegInitiated,
    setVerificationCodeSubmitted,
    setPasswordSubmitted,
    setUpdateAuthInfoSubmitted,
    setCustomerDetailsSubmitted,
  } = authStore()

  const handleContactInfoSuccess = (username: string) => {
    setAltUsername(username)
    setVerificationStep(true)
  }


  const handleVerificationSuccess = () => {
    // login()
    setPasswordSubmitted(false)
    setRegInitiated(false)
    setVerificationCodeSubmitted(false)
    setCustomerDetailsSubmitted(false)
    setUpdateAuthInfoSubmitted(false)
    router.push('/account/profile/')
  }

  const handleSkip = () => {
    router.push('/account/profile/')
  }

  return (
    <AuthLayout title="Update Your Information" error={null}>
      {!verificationStep ? (
        <UpdateAuthContact
          username={username}
          onSuccess={handleContactInfoSuccess}
          onSkip={handleSkip}
        />
      ) : (
        <VerifyAuthContact
          altUsername={altUsername!}
          onSuccess={handleVerificationSuccess}
        />
      )}
    </AuthLayout>
  )
}

export default UpdateAuthInfoContainer