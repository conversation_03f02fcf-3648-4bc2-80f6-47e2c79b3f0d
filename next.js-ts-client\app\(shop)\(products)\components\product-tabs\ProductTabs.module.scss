@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.product_tabs {
  margin: 2rem auto;
  max-width: 1200px;
}

.tabs_header {
  @include flexbox(flex-start, flex-end);
  border-bottom: 1px solid #ddd;

  button {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    color: $primary-dark-text-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      color: $primary-blue;
    }

    &.active {
      color: $primary-blue;
      border-bottom: 3px solid $primary-blue;
    }
  }
}

.tabs_content {
  padding: 1.5rem 0;

  h3 {
    font-size: $font-size-3;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin-bottom: 1rem;
  }

  p {
    line-height: 1.6;
    color: $primary-dark-text-color;
  }
}

.product_description,
.specifications,
.reviews {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}