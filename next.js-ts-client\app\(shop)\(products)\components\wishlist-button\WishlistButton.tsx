'use client'

import { useState } from 'react'
import { FaHeart, FaRegHeart } from 'react-icons/fa'
import styles from './WishlistButton.module.scss'
import { useToggleWishlist } from '@/src/hooks/wishlist-hooks'

interface WishlistButtonProps {
  productId: number
}

export default function WishlistButton({ productId }: WishlistButtonProps) {
  const mutation = useToggleWishlist()
  const [isInWishlist, setIsInWishlist] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleToggleWishlist = async () => {
    setIsLoading(true)

    try {
      // Simulate API call - replace with actual wishlist logic
      mutation.mutate(productId)

      setIsInWishlist(!isInWishlist)

      console.log(isInWishlist ? 'Removed from wishlist:' : 'Added to wishlist:', productId)

    } catch (error) {
      console.error('Failed to update wishlist:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      className={`${styles.wishlist_btn} ${isInWishlist ? styles.in_wishlist : ''}`}
      onClick={handleToggleWishlist}
      disabled={isLoading}
      title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      {isLoading ? (
        <div className={styles.spinner}></div>
      ) : isInWishlist ? (
        <FaHeart />
      ) : (
        <FaRegHeart />
      )}
    </button>
  )
}