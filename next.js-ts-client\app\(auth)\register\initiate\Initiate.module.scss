@use '../../../../src/scss/mixins' as *;
@use '../../../../src/scss/variables' as *;

// Main form group container
// .form_group {
//   @include flexbox(flex-start, stretch, column);
//   width: 100%;
//   // margin-bottom: 15px;
//   // row-gap: 4px;

//   // Nested form group (for the email input wrapper)
//   .form_group {
//     margin-bottom: 0;
//     width: 100%;
//   }

//   .form_label {
//     font-weight: bold;
//     color: $primary-dark-blue;
//     width: 100%;
//     display: block;
//   }

//   .form_input {
//     width: 100%;
//     border: .1px solid $primary-dark-text-color;
//     border-radius: 3px;
//     padding: 8px 10px;
//     font-size: 16.5px;
//     box-sizing: border-box;

//     &:focus {
//       outline: 2px solid $lighten-blue;
//       border: none;
//     }
//   }

//   .form_error {
//     color: $error-red;
//     text-align: left;
//     margin-top: 4px;
//     font-size: 14px;
//   }
// }

// .login_or_register {
//   text-align: center;

//   a {
//     color: $primary-blue;
//     transition: all 0.3s ease-in;

//     &:hover {
//       color: darken($primary-blue, 10%);
//       text-decoration: underline;
//     }
//   }
// }
