# API Documentation: Packing & Shipping Service

## 📋 Overview

This document outlines the REST API endpoints for the packing and shipping service, including box management, packing rules, carrier configuration, and shipping calculations.

## 🔐 Authentication & Authorization

All endpoints require authentication. Admin-only endpoints are marked with **[ADMIN]**.

**Authentication Methods:**

- JWT Token: `Authorization: Bearer <token>`
- Session Authentication (for Django Admin integration)

## 📦 Box Management API

### List Available Boxes

**GET** `/api/shipping/boxes/`

Returns all active shipping boxes with their specifications.

**Query Parameters:**

- `is_active` (boolean): Filter by active status
- `is_mailer` (boolean): Filter mailer vs regular boxes
- `min_volume` (decimal): Minimum volume filter
- `max_volume` (decimal): Maximum volume filter

**Response:**

```json
{
  "count": 15,
  "results": [
    {
      "id": 1,
      "name": "Small Box",
      "internal_length": 20.00,
      "internal_width": 15.00,
      "internal_height": 10.00,
      "max_weight": 2000.00,
      "cost": 2.50,
      "volume": 3000.0000,
      "is_mailer": false,
      "is_active": true
    }
  ]
}
```

### Create Box **[ADMIN]**

**POST** `/api/shipping/boxes/`

Create a new shipping box configuration.

**Request Body:**

```json
{
  "name": "Medium Box",
  "internal_length": 30.00,
  "internal_width": 25.00,
  "internal_height": 15.00,
  "max_weight": 5000.00,
  "cost": 4.50,
  "is_mailer": false
}
```

### Update Box **[ADMIN]**

**PUT** `/api/shipping/boxes/{id}/`

Update existing box configuration.

### Delete Box **[ADMIN]**

**DELETE** `/api/shipping/boxes/{id}/`

Soft delete (set is_active=False) a box configuration.

## 📏 Packing Rules API

### List Packing Rules

**GET** `/api/shipping/rules/`

Returns all active packing rules ordered by priority.

**Query Parameters:**

- `is_active` (boolean): Filter by active status
- `priority` (integer): Filter by priority level

**Response:**

```json
{
  "count": 8,
  "results": [
    {
      "id": 1,
      "name": "Small Items Mailer Rule",
      "priority": 1,
      "is_active": true,
      "min_weight": null,
      "max_weight": 500.00,
      "min_volume": null,
      "max_volume": 1000.0000,
      "preferred_box": {
        "id": 5,
        "name": "Padded Mailer"
      },
      "force_mailer": true,
      "additional_cost": 0.00,
      "product_types": [
        {
          "id": 1,
          "title": "Electronics Accessories"
        }
      ]
    }
  ]
}
```

### Create Packing Rule **[ADMIN]**

**POST** `/api/shipping/rules/`

Create a new packing rule.

**Request Body:**

```json
{
  "name": "Heavy Items Rule",
  "priority": 5,
  "min_weight": 5000.00,
  "max_weight": 20000.00,
  "preferred_box": 3,
  "additional_cost": 5.00,
  "product_types": [1, 2]
}
```

### Update Packing Rule **[ADMIN]**

**PUT** `/api/shipping/rules/{id}/`

Update existing packing rule.

### Delete Packing Rule **[ADMIN]**

**DELETE** `/api/shipping/rules/{id}/`

Remove a packing rule.

## 🚚 Carrier Management API

### List Carriers

**GET** `/api/shipping/carriers/`

Returns all configured shipping carriers.

**Response:**

```json
{
  "count": 3,
  "results": [
    {
      "id": 1,
      "name": "Posten Bring",
      "code": "posten_bring",
      "is_active": true,
      "base_cost": 0.00,
      "services": [
        {
          "id": 1,
          "service_name": "Standard Parcel",
          "service_code": "standard",
          "estimated_days": 3,
          "is_active": true
        }
      ]
    }
  ]
}
```

### Create Carrier **[ADMIN]**

**POST** `/api/shipping/carriers/`

Add a new shipping carrier.

**Request Body:**

```json
{
  "name": "DHL Express",
  "code": "dhl_express",
  "api_endpoint": "https://api.dhl.com/v1",
  "base_cost": 5.00
}
```

### Update Carrier **[ADMIN]**

**PUT** `/api/shipping/carriers/{id}/`

Update carrier configuration.

## 🧮 Shipping Calculation API

### Calculate Shipping for Cart

**POST** `/api/shipping/calculate/`

Calculate optimal packing and shipping cost for cart items.

**Request Body:**

```json
{
  "cart_id": 123,
  "destination_address": {
    "street": "123 Main St",
    "city": "Oslo",
    "postal_code": "0150",
    "country": "NO"
  },
  "force_recalculate": false
}
```

**Response:**

```json
{
  "success": true,
  "packing_result": {
    "boxes": [
      {
        "box": {
          "id": 1,
          "name": "Small Box",
          "cost": 2.50
        },
        "items": [
          {
            "sku": "PROD-001",
            "quantity": 2,
            "weight": 150.00
          }
        ],
        "utilization": 75.5,
        "total_weight": 300.00,
        "total_cost": 2.50
      }
    ],
    "total_cost": 2.50,
    "total_weight": 300.00,
    "total_volume": 2250.0000,
    "unpacked_items": []
  },
  "shipping_rate": {
    "carrier_name": "Posten Bring",
    "service_name": "Standard Parcel",
    "cost": 8.50,
    "estimated_days": 2,
    "tracking_available": true
  },
  "total_shipping_cost": 11.00,
  "calculation_time": "2024-01-15T10:30:00Z"
}
```

### Calculate Shipping for Custom Items

**POST** `/api/shipping/calculate/custom/`

Calculate shipping for custom item specifications (useful for quotes).

**Request Body:**

```json
{
  "items": [
    {
      "length": 25.0,
      "width": 20.0,
      "height": 15.0,
      "weight": 1500.0,
      "quantity": 1,
      "is_fragile": false
    }
  ],
  "destination_address": {
    "country": "NO",
    "postal_code": "0150"
  }
}
```

## 📊 Analytics & Reporting API

### Shipping Cost Analytics **[ADMIN]**

**GET** `/api/shipping/analytics/costs/`

Get shipping cost analytics and trends.

**Query Parameters:**

- `start_date` (date): Start date for analytics
- `end_date` (date): End date for analytics
- `group_by` (string): Group by 'day', 'week', 'month'

**Response:**

```json
{
  "period": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  },
  "summary": {
    "total_orders": 1250,
    "average_shipping_cost": 12.75,
    "average_packing_cost": 3.25,
    "most_used_box": "Medium Box",
    "carrier_distribution": {
      "Posten Bring": 85.5,
      "DHL Express": 14.5
    }
  },
  "trends": [
    {
      "date": "2024-01-01",
      "orders": 45,
      "avg_shipping_cost": 11.50,
      "avg_packing_cost": 3.00
    }
  ]
}
```

### Box Utilization Report **[ADMIN]**

**GET** `/api/shipping/analytics/boxes/`

Get box usage and efficiency analytics.

**Response:**

```json
{
  "box_usage": [
    {
      "box_name": "Small Box",
      "usage_count": 450,
      "usage_percentage": 35.2,
      "average_utilization": 78.5,
      "total_cost": 1125.00
    }
  ],
  "efficiency_metrics": {
    "overall_utilization": 72.3,
    "wasted_space_cost": 125.50,
    "optimization_potential": 8.5
  }
}
```

## 🔧 Configuration API

### Get Shipping Configuration **[ADMIN]**

**GET** `/api/shipping/config/`

Get current shipping service configuration.

**Response:**

```json
{
  "default_carrier": "posten_bring",
  "fallback_shipping_cost": 15.00,
  "max_package_weight": 20000.00,
  "calculation_timeout": 30,
  "cache_duration": 300,
  "auto_recalculate": true,
  "supported_countries": ["NO", "SE", "DK", "FI", "DE", "NL", "BE", "FR", "GB"]
}
```

### Update Shipping Configuration **[ADMIN]**

**PUT** `/api/shipping/config/`

Update shipping service configuration.

**Request Body:**

```json
{
  "fallback_shipping_cost": 18.00,
  "calculation_timeout": 45,
  "auto_recalculate": true
}
```

## 🧪 Testing & Validation API

### Test Packing Algorithm **[ADMIN]**

**POST** `/api/shipping/test/packing/`

Test packing algorithm with sample data.

**Request Body:**

```json
{
  "test_items": [
    {
      "length": 10.0,
      "width": 8.0,
      "height": 5.0,
      "weight": 200.0,
      "quantity": 3
    }
  ],
  "available_boxes": [1, 2, 3]
}
```

### Validate Carrier Connection **[ADMIN]**

**POST** `/api/shipping/test/carrier/{carrier_id}/`

Test connection to shipping carrier API.

**Response:**

```json
{
  "carrier_name": "Posten Bring",
  "connection_status": "success",
  "response_time": 245,
  "api_version": "v2.1",
  "last_tested": "2024-01-15T10:30:00Z",
  "error_message": null
}
```

## 📝 Error Responses

### Standard Error Format

```json
{
  "error": {
    "code": "PACKING_FAILED",
    "message": "Unable to find suitable packaging for items",
    "details": {
      "unpacked_items": ["PROD-001", "PROD-002"],
      "reason": "Items exceed maximum box dimensions"
    }
  }
}
```

### Common Error Codes

- `PACKING_FAILED`: Unable to pack items
- `CARRIER_UNAVAILABLE`: Shipping carrier API unavailable
- `INVALID_ADDRESS`: Destination address validation failed
- `WEIGHT_EXCEEDED`: Items exceed maximum weight limits
- `CONFIGURATION_ERROR`: Shipping service misconfiguration

## 🔄 Webhooks

### Shipping Rate Update

**POST** `{webhook_url}/shipping/rate-update/`

Triggered when shipping rates are updated from carrier APIs.

**Payload:**

```json
{
  "event": "shipping.rate.updated",
  "carrier": "posten_bring",
  "timestamp": "2024-01-15T10:30:00Z",
  "affected_routes": [
    {
      "origin": "NO",
      "destination": "SE",
      "old_rate": 12.50,
      "new_rate": 13.00
    }
  ]
}
```

This API documentation provides comprehensive coverage of all shipping service endpoints, enabling both frontend integration and administrative management of the packing and shipping system.
