// Define the slideIn animation globally
@keyframes slideIn {
  0% {
    transform: translateX(-50px);
    /* Start from 50px left */
    opacity: 0;
    /* Fully transparent */
  }

  100% {
    transform: translateX(0);
    /* Move to natural position */
    opacity: 1;
    /* Fully visible */
  }
}

// Optional: Create a mixin for reusable animation settings
@mixin slideInAnimation($duration: 0.5s, $easing: ease-out) {
  animation: slideIn $duration $easing;
}