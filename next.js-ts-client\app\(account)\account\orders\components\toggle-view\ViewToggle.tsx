import { FaList, FaTable } from 'react-icons/fa'
import styles from './ViewToggle.module.scss'

interface Props {
  activeIcon: 'list' | 'table'
  onToggle: (mode: 'list' | 'table') => void
}

const ViewToggle = ({ activeIcon, onToggle }: Props) => {
  return (
    <div className={styles.toggle_view}>
      <FaList
        onClick={() => onToggle('list')}
        className={`${styles.icon} ${
          activeIcon === 'list' ? styles.active : ''
        }`}
      />
      <FaTable
        onClick={() => onToggle('table')}
        className={`${styles.icon} ${
          activeIcon === 'table' ? styles.active : ''
        }`}
      />
    </div>
  )
}

export default ViewToggle
