'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import styles from './error.module.scss'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Product page error:', error)
  }, [error])

  return (
    <div className={`container ${styles.error_container}`}>
      <div className={styles.error_content}>
        <h1>Something went wrong!</h1>
        <p>
          We encountered an error while loading this product. This could be due to:
        </p>
        <ul>
          <li>A temporary server issue</li>
          <li>Network connectivity problems</li>
          <li>The product may no longer be available</li>
        </ul>

        <div className={styles.error_actions}>
          <button
            onClick={reset}
            className={styles.retry_button}
          >
            Try again
          </button>
          <Link href="/products" className={styles.browse_button}>
            Browse all products
          </Link>
          <Link href="/" className={styles.home_button}>
            Go to homepage
          </Link>
        </div>
      </div>
    </div>
  )
}