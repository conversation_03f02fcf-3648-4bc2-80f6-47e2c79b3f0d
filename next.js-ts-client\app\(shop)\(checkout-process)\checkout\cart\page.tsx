'use client'

import <PERSON><PERSON> from '@/src/components/utils/alert/Alert'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import Spinner from '@/src/components/utils/spinner/Spinner'
import {
  useSimpleCart,
  useDeleteCartItem,
  useUpdateCart,
} from '@/src/hooks/cart-hooks'
import cartStore from '@/src/stores/cart-store'
import { CartItemShape } from '@/src/types/store-types'
import { ErrorResponse } from '@/src/types/types'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import CartItemsList from '../../components/cart/CartItemsList'
import SelectedCartSummary from '../../components/cart/SelectedCartSummary'
// server is now source-of-truth for selection; do not sync into client store here
import styles from './Cart.module.scss'

const Cart = () => {
  const router = useRouter()
  const [noStockAlert, setNoStockAlert] = useState(false)

  const { cartId } = cartStore()
  const { isLoading, error, data } = useSimpleCart()
  const { handleQuantityUpdate, mutation } = useUpdateCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()
  // derive selected ids directly from server data (server = source of truth)
  const serverSelectedIds = React.useMemo(() => {
    const items = Array.isArray(data?.cart_items) ? data.cart_items : []
    return new Set<number>(
      items.filter((it) => it?.is_selected === true).map((it) => Number(it.id))
    )
  }, [data?.cart_items])

  const handleIncrement = (item: CartItemShape) => {
    const newQuantity = item.quantity + 1
    handleQuantityUpdate(item.id, newQuantity)
  }

  const handleDecrement = (item: CartItemShape) => {
    if (item.quantity > 1) {
      const newQuantity = item.quantity - 1
      handleQuantityUpdate(item.id, newQuantity)
    }
  }

  const handleCheckout = (cartItems?: CartItemShape[] | null) => {
    if (!cartItems || cartItems.length === 0) {
      alert('Please select items to checkout')
      return
    }

    // Check only server-selected items for stock availability
    const selectedItems = cartItems.filter((item) =>
      serverSelectedIds.has(item.id)
    )

    if (selectedItems.length === 0) {
      alert('Please select items to checkout')
      return
    }

    const hasOutOfStockItems = selectedItems.some(
      (item) => item.product_variant.stock_qty === 0
    )

    if (hasOutOfStockItems) {
      setNoStockAlert(true)
    } else {
      router.push('/checkout/address-choice')
    }
  }

  useEffect(() => {
    // When cart data changes, check stock for server-selected items
    if (data && data?.cart_items?.length > 0) {
      // Check only selected items for out-of-stock status (server selection)
      const selectedItems = data.cart_items.filter((item) =>
        serverSelectedIds.has(item.id)
      )
      const stillOutOfStock = selectedItems.some(
        (item) => item.product_variant.stock_qty === 0
      )
      // Hide the alert if no selected items are out of stock.
      if (!stillOutOfStock) {
        setNoStockAlert(false)
      }
    }
  }, [data, serverSelectedIds])

  return (
    <div>
      {noStockAlert && (
        <Alert
          variant='error'
          message={`
      Some items are out of stock. Please remove them from the cart to proceed with checkout process.`}
        />
      )}

      {mutation.error && (
        <Alert
          variant='error'
          message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
        />
      )}

      {!cartId ? (
        <EmptyCart />
      ) : isLoading ? (
        <Spinner color='#0091CF' size={20} loading={true} />
      ) : error ? (
        <Alert
          variant='error'
          message={getErrorMessage(error as AxiosError<ErrorResponse>)}
        />
      ) : !data ||
        data?.cart_items?.length === 0 ||
        Object.keys(data).length === 0 ? (
        <EmptyCart />
      ) : (
        <>
          <div className={`${styles.cart} container`}>
            <h2>Shopping Cart</h2>
            <hr />
            <div className={styles.cart__cart_items}>
              <CartItemsList
                cartItems={data.cart_items}
                selectedIds={serverSelectedIds}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                deleteCartItem={deleteCartItem}
                showSelection={true}
              />

              <div className={styles.cart__summaries}>
                <SelectedCartSummary
                  cartItems={data.cart_items}
                  showComparison={false}
                  selectedIds={serverSelectedIds}
                  totalPrice={data?.selected_total_price}
                  item_count={data?.selected_item_count}
                  cart_weight={data?.selected_cart_weight}
                  onCheckout={() => handleCheckout(data?.cart_items)}
                />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Cart
