'use client'

import { useState, useEffect } from 'react'
import { ProductShape, ProductVariant } from '../../../../../src/types/product-types'
import ProductImageGallery from '../product-image-gallery/ProductImageGallery'
import ProductInfo from '../product-info/ProductInfo'
import ProductTabs from '../product-tabs/ProductTabs'
import { findVariantImage } from '../utils/product-utils'
import cartStore from '../../../../../src/stores/cart-store'
import styles from './ProductDetailsClient.module.scss'

interface ProductDetailsClientProps {
  product: ProductShape
  defaultVariant: ProductVariant | null
  initialImage: string
}

export default function ProductDetailsClient({
  product,
  defaultVariant,
  initialImage
}: ProductDetailsClientProps) {
  const { resetExtraData } = cartStore()
  const [selectedImage, setSelectedImage] = useState<string>(initialImage)
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(defaultVariant)
  const [qty, setQty] = useState(1)

  // Reset cart extra data when component mounts or product changes
  useEffect(() => {
    resetExtraData()
  }, [product.id, resetExtraData])

  // Update selectedImage when selectedVariant changes
  useEffect(() => {
    if (selectedVariant && product) {
      const imageUrl = findVariantImage(
        selectedVariant,
        product,
        process.env.NEXT_PUBLIC_CLOUDINARY_URL || ''
      )
      if (imageUrl) {
        setSelectedImage(imageUrl)
      }
    }
  }, [selectedVariant, product])

  const handleImageClick = (imageSrc: string) => {
    setSelectedImage(imageSrc)
  }

  const handleVariantClick = (variant: ProductVariant) => {
    setSelectedVariant(variant)
  }

  const handleQtyChange = (newQty: number) => {
    if (!isNaN(newQty) && newQty >= 1 && newQty <= 10) {
      setQty(newQty)
    }
  }

  return (
    <>
      <section className={styles.product_details}>
        <ProductImageGallery
          product={product}
          selectedImage={selectedImage}
          selectedVariant={selectedVariant}
          onImageClick={handleImageClick}
        />

        <ProductInfo
          product={product}
          selectedVariant={selectedVariant}
          qty={qty}
          onVariantClick={handleVariantClick}
          onQtyChange={handleQtyChange}
        />
      </section>

      <ProductTabs
        product={product}
        selectedVariant={selectedVariant}
      />
    </>
  )
}