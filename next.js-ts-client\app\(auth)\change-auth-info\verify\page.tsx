'use client'

import Alert from '@/src/components/utils/alert/Alert'
import { useSendAuthInfoVerifyCode, useSendVerifyCode } from '@/src/hooks/auth-hooks'
import { verificationCodeSchema } from '@/src/schemas/schemas'
import { ErrorResponse } from '@/src/types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import AuthLayout from '../../AuthLayout'
import styles from './ChangeAuthInfo.module.scss'

export type VerifyCodeShape = z.infer<typeof verificationCodeSchema>

const VerifyAuthInfo = () => {
  const router = useRouter()
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyCodeShape>({
    resolver: zodResolver(verificationCodeSchema),
  })

  const { mutation } = useSendAuthInfoVerifyCode()


  const onSubmit: SubmitHandler<VerifyCodeShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        router.push('/account/profile')
      },
    })
  }

  return (
    <AuthLayout title="Verify Contact Information" error={mutation.error as AxiosError<ErrorResponse> | null}>
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <Alert
          variant="success"
          message={`Enter the received verification code here.`}
        // textAlign='center'
        />

        <div className='form_group'>
          <label className='form_label'>Enter the verification code:</label>
          {/* <input
              className='form_input'
              type='number'
              {...register("code")}
              disabled={mutation.isPending}
            /> */}
          <input
            className='form_input'
            type='text'
            inputMode='numeric'
            id='verification_code'
            disabled={mutation.isPending}
            onInput={(e) => {
              e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
            }}
            {...register("code")}
          // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
          />
          {errors.code && (
            <p className='form_error'>{errors.code.message}</p>
          )}
        </div>

        <button type="submit" disabled={mutation.isPending}>
          {mutation.isPending ? (
            // <img src={loading_svg} alt="Loading..." className='loading_svg' />
            'Submitting...'
          ) : 'Submit'}
        </button>
      </form>
    </AuthLayout>
  )
}

export default VerifyAuthInfo