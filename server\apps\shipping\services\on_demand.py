"""
On-demand shipping calculation service for decoupled cart operations.

This service provides shipping calculations that can be called independently
from cart operations, supporting the new architecture where shipping is
calculated only when explicitly requested by the user.
"""

import time
import logging
from decimal import Decimal
from typing import Optional, Dict, Any, List
from django.utils import timezone
from django.db import transaction

from .cart import CartShippingService
from .packing import PackingService
from .shipping import ShippingService
from apps.cart.models import Cart


class OnDemandShippingService:
    """
    Service for on-demand shipping calculations.
    
    This service is designed to be called when users explicitly request
    shipping calculations, typically before checkout.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cart_shipping_service = CartShippingService()
        self.packing_service = PackingService()
        self.shipping_service = ShippingService()
    
    def calculate_shipping_for_cart(
        self,
        cart: Cart,
        destination_address: Optional[Any] = None,
        get_all_options: bool = False,
        selected_only: bool = True
    ) -> Dict[str, Any]:
        """
        Calculate shipping costs for a cart with detailed options.

        Args:
            cart: Cart instance
            destination_address: Destination address (Address model or dict)
            get_all_options: If True, returns all shipping options instead of just the best one
            selected_only: If True, calculate shipping for selected items only (default: True)

        Returns:
            Dictionary with shipping calculation results
        """
        start_time = time.time()
        
        try:
            # Validate cart has items (selected items only if specified)
            cart_items_query = cart.cart_items.select_related(
                'product', 'product_variant', 'product__product_type'
            )

            if selected_only:
                cart_items = cart_items_query.filter(is_selected=True)
                empty_message = 'No items selected for shipping calculation'
            else:
                cart_items = cart_items_query.all()
                empty_message = 'Cannot calculate shipping for empty cart'

            if not cart_items:
                return {
                    'success': False,
                    'error': 'Cart is empty' if not selected_only else 'No items selected',
                    'message': empty_message
                }
            
            # Calculate optimal packaging
            self.logger.info(f"Starting packing calculation for cart {cart.id}")
            packing_result = self.packing_service.calculate_optimal_packaging(cart_items)
            
            if not packing_result.success:
                return {
                    'success': False,
                    'error': 'Packing calculation failed',
                    'message': 'Unable to determine optimal packaging',
                    'packing_warnings': packing_result.warnings,
                    'unpacked_items': packing_result.unpacked_items
                }
            
            # Calculate shipping options
            if get_all_options:
                shipping_options = self._get_all_shipping_options(packing_result, destination_address)
            else:
                best_option = self._get_best_shipping_option(packing_result, destination_address)
                shipping_options = [best_option] if best_option else []
            
            # Update cart with calculated values
            self._update_cart_shipping_data(cart, packing_result, shipping_options)
            
            calculation_time = time.time() - start_time
            
            result = {
                'success': True,
                'message': 'Shipping calculated successfully',
                'calculation_time': round(calculation_time, 3),
                'packing_cost': float(packing_result.total_cost),
                'total_weight': float(packing_result.total_weight),
                'total_volume': float(packing_result.total_volume),
                'shipping_options': shipping_options,
                'packing_details': {
                    'boxes_count': len(packing_result.boxes),
                    'method_used': packing_result.method_used,
                    'calculation_time': packing_result.calculation_time,
                    'warnings': packing_result.warnings
                }
            }
            
            if shipping_options:
                # Use the first (best) option for cart update
                best_option = shipping_options[0]
                result['shipping_cost'] = best_option['cost']
                result['estimated_delivery_days'] = best_option.get('estimated_days', 'Unknown')
                result['carrier_name'] = best_option.get('carrier_name', 'Unknown')
            
            self.logger.info(f"Shipping calculation completed for cart {cart.id} in {calculation_time:.3f}s")
            return result
            
        except Exception as e:
            calculation_time = time.time() - start_time
            self.logger.error(f"Shipping calculation failed for cart {cart.id}: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'message': 'Shipping calculation failed',
                'calculation_time': round(calculation_time, 3)
            }
    
    def _get_all_shipping_options(self, packing_result, destination_address) -> List[Dict[str, Any]]:
        """Get all available shipping options"""
        try:
            shipping_rates = self.shipping_service.get_all_shipping_rates(
                packing_result, destination_address
            )
            
            options = []
            for rate in shipping_rates:
                options.append({
                    'carrier_name': rate.carrier_name,
                    'service_name': rate.service_name,
                    'cost': float(rate.cost),
                    'estimated_days': rate.estimated_days,
                    'max_days': getattr(rate, 'max_days', None),
                    'tracking_available': rate.tracking_available,
                    'insurance_available': getattr(rate, 'insurance_available', False),
                    'signature_available': getattr(rate, 'signature_available', False)
                })
            
            # Sort by cost (cheapest first)
            options.sort(key=lambda x: x['cost'])
            return options
            
        except Exception as e:
            self.logger.error(f"Failed to get shipping options: {e}")
            return []
    
    def _get_best_shipping_option(self, packing_result, destination_address) -> Optional[Dict[str, Any]]:
        """Get the best (cheapest) shipping option"""
        options = self._get_all_shipping_options(packing_result, destination_address)
        return options[0] if options else None
    
    def _update_cart_shipping_data(self, cart: Cart, packing_result, shipping_options: List[Dict]):
        """Update cart with calculated shipping data"""
        try:
            with transaction.atomic():
                # Update packing data
                cart.packing_cost = packing_result.total_cost
                cart.total_weight = packing_result.total_weight
                cart.total_volume = packing_result.total_volume
                cart.last_shipping_calculation = timezone.now()
                
                # Update shipping cost with best option
                if shipping_options:
                    cart.shipping_cost = Decimal(str(shipping_options[0]['cost']))
                else:
                    cart.shipping_cost = Decimal('0.00')
                
                # Store detailed packing information
                cart.packing_details = {
                    'boxes': [
                        {
                            'box_id': packed_box.box.id,
                            'box_title': packed_box.box.title,
                            'box_cost': float(packed_box.total_cost),
                            'utilization': round(packed_box.utilization, 2),
                            'total_weight': float(packed_box.total_weight),
                            'items_count': len(packed_box.items)
                        }
                        for packed_box in packing_result.boxes
                    ],
                    'method_used': packing_result.method_used,
                    'calculation_time': packing_result.calculation_time,
                    'calculated_at': timezone.now().isoformat(),
                    'shipping_options_count': len(shipping_options)
                }
                
                cart.save(update_fields=[
                    'packing_cost', 'shipping_cost', 'total_weight', 'total_volume',
                    'last_shipping_calculation', 'packing_details'
                ])
                
        except Exception as e:
            self.logger.error(f"Failed to update cart {cart.id} with shipping data: {e}")
            raise
    
    def get_shipping_estimate(self, cart: Cart, destination_zone: str = 'domestic') -> Dict[str, Any]:
        """
        Get a quick shipping estimate without full calculation.
        
        This can be used to show rough estimates during cart building.
        """
        try:
            total_weight = cart.get_cart_weight()
            
            # Simple weight-based estimation
            if total_weight <= 500:  # 500g
                estimated_cost = Decimal('10.00')
            elif total_weight <= 2000:  # 2kg
                estimated_cost = Decimal('15.00')
            elif total_weight <= 5000:  # 5kg
                estimated_cost = Decimal('25.00')
            elif total_weight <= 10000:  # 10kg
                estimated_cost = Decimal('35.00')
            else:  # Over 10kg
                estimated_cost = Decimal('50.00')
            
            # Adjust for international shipping
            if destination_zone == 'international':
                estimated_cost *= Decimal('2.5')
            
            return {
                'success': True,
                'estimated_cost': float(estimated_cost),
                'weight': total_weight,
                'note': 'This is a rough estimate. Calculate exact shipping before checkout.'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get shipping estimate for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'estimated_cost': 0.00
            }
