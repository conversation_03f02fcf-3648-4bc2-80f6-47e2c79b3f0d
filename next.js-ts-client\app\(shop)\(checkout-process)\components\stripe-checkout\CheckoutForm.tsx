'use client'

import { PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import styles from './CheckoutForm.module.scss'
import { CACHE_KEY_ORDER_ITEMS } from '@/src/constants/constants'

interface Props {
  amount: number
  orderId: number
}

const CheckoutForm = ({ orderId, amount }: Props) => {
  const stripe = useStripe()
  const elements = useElements()

  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const queryClient = useQueryClient()

  const onSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    setLoading(true)

    if (!stripe || !elements) {
      setErrorMessage('Stripe is not loaded. Please refresh and try again.')
      setLoading(false)
      return
    }

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `http://localhost:3000/checkout-process/order/${orderId}`,
        // payment_method_data: {
        //   billing_details: {
        //     email: customer.email,
        //     name: customer.name,
        //   },
        // },
      },
      // redirect: 'if_required', // Avoids unnecessary re-directions
    })

    if (error) {
      setErrorMessage(error.message || 'An error occurred during payment.')
    } else {
      handleSuccessfulPayment()
    }

    setLoading(false)
  }

  // Handles post-payment success actions
  const handleSuccessfulPayment = () => {
    // Invalidate cache for updated order items
    queryClient.invalidateQueries({ queryKey: [CACHE_KEY_ORDER_ITEMS] })
  }

  return (
    <form onSubmit={onSubmit} className={styles.stripe_form}>
      <PaymentElement
        options={{
          layout: {
            type: 'accordion',
            defaultCollapsed: false,
          },
        }}
      />
      <button
        className={styles.pay_btn}
        type='submit'
        disabled={!stripe || loading}
      >
        {loading ? (
          <p>Processing...</p>
        ) : (
          // <img src={loading_svg} alt="Processing..." className="loading_svg" />
          <p>
            Pay <span>${amount}</span>
          </p>
        )}
      </button>
      {errorMessage && <p className={styles.error_message}>{errorMessage}</p>}
    </form>
  )
}

export default CheckoutForm
