// Product Types management route
import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { ProductTypesPage } from '../../pages/products/ProductTypesPage'

export const Route = createFileRoute('/products/types')({
  component: ProductTypesRoute,
})

function ProductTypesRoute() {
  return (
    <AuthGuard permission="staff.view_producttype">
      <ProductTypesPage />
    </AuthGuard>
  )
}
