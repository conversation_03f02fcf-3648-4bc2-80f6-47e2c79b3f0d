"""
Common utilities for staff app
Shared functions and helpers used across different domains
"""

from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


def get_client_ip(request):
    """
    Extract client IP address from request
    Handles proxy headers and fallbacks
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    return ip


def get_user_agent(request):
    """
    Extract user agent from request
    """
    return request.META.get('HTTP_USER_AGENT', '')


def cache_user_data(user_id, data, cache_key_suffix, timeout=300):
    """
    Cache user-specific data with standardized key format
    
    Args:
        user_id: User ID
        data: Data to cache
        cache_key_suffix: Suffix for cache key (e.g., 'permissions', 'groups')
        timeout: Cache timeout in seconds (default: 5 minutes)
    """
    cache_key = f'staff_user_{cache_key_suffix}_{user_id}'
    cache.set(cache_key, data, timeout)
    return cache_key


def get_cached_user_data(user_id, cache_key_suffix):
    """
    Retrieve cached user-specific data
    
    Args:
        user_id: User ID
        cache_key_suffix: Suffix for cache key
        
    Returns:
        Cached data or None if not found
    """
    cache_key = f'staff_user_{cache_key_suffix}_{user_id}'
    return cache.get(cache_key)


def clear_user_cache(user_id, cache_key_suffixes=None):
    """
    Clear cached data for a user
    
    Args:
        user_id: User ID
        cache_key_suffixes: List of cache key suffixes to clear, or None for all common ones
    """
    if cache_key_suffixes is None:
        cache_key_suffixes = ['permissions', 'groups', 'profile']
    
    for suffix in cache_key_suffixes:
        cache_key = f'staff_user_{suffix}_{user_id}'
        cache.delete(cache_key)


def validate_staff_user(user):
    """
    Validate that a user is eligible for staff operations

    Args:
        user: User instance

    Returns:
        bool: True if user is valid staff member
    """
    return (
        user and
        user.is_authenticated and
        user.is_active and
        user.is_staff
    )


def validate_active_user(user):
    """
    Validate that a user is active and can be assigned to groups

    Args:
        user: User instance

    Returns:
        bool: True if user is active and can be assigned to groups
    """
    return (
        user and
        user.is_active
    )


def get_user_full_name(user):
    """
    Get user's full name with fallback to email prefix
    
    Args:
        user: User instance
        
    Returns:
        str: User's full name or email prefix
    """
    try:
        customer = user.customer
        full_name = f"{customer.first_name} {customer.last_name}".strip()
        return full_name if full_name else user.email.split('@')[0]
    except (AttributeError, Exception):
        return user.email.split('@')[0]


def format_audit_details(action, old_data=None, new_data=None, **kwargs):
    """
    Format audit details in a standardized way
    
    Args:
        action: Action being performed
        old_data: Previous state data
        new_data: New state data
        **kwargs: Additional details
        
    Returns:
        dict: Formatted audit details
    """
    details = {
        'action': action,
        'timestamp': timezone.now().isoformat(),
    }
    
    if old_data is not None:
        details['old_data'] = old_data
    
    if new_data is not None:
        details['new_data'] = new_data
    
    details.update(kwargs)
    return details


def get_date_range_filter(request, default_days=30):
    """
    Extract date range filters from request parameters
    
    Args:
        request: HTTP request
        default_days: Default number of days to look back
        
    Returns:
        dict: Date range filters
    """
    filters = {}
    
    # Get from_date
    from_date = request.query_params.get('from_date')
    if from_date:
        try:
            filters['from_date'] = timezone.datetime.fromisoformat(from_date)
        except ValueError:
            logger.warning(f"Invalid from_date format: {from_date}")
    
    # Get to_date
    to_date = request.query_params.get('to_date')
    if to_date:
        try:
            filters['to_date'] = timezone.datetime.fromisoformat(to_date)
        except ValueError:
            logger.warning(f"Invalid to_date format: {to_date}")
    
    # Set default from_date if not provided
    if 'from_date' not in filters and 'to_date' not in filters:
        filters['from_date'] = timezone.now() - timedelta(days=default_days)
    
    return filters


def paginate_queryset(queryset, request, page_size=20):
    """
    Simple pagination helper
    
    Args:
        queryset: Django queryset
        request: HTTP request
        page_size: Items per page
        
    Returns:
        dict: Paginated data
    """
    try:
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', page_size))
    except (ValueError, TypeError):
        page = 1
        page_size = 20
    
    # Ensure reasonable limits
    page_size = min(max(page_size, 1), 100)
    page = max(page, 1)
    
    start = (page - 1) * page_size
    end = start + page_size
    
    total_count = queryset.count()
    items = queryset[start:end]
    
    return {
        'items': items,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_pages': (total_count + page_size - 1) // page_size,
            'has_next': end < total_count,
            'has_previous': page > 1
        }
    }


def log_staff_action(action, user, details=None, level='info'):
    """
    Log staff actions with standardized format
    
    Args:
        action: Action description
        user: User performing the action
        details: Additional details
        level: Log level (info, warning, error)
    """
    log_message = f"Staff Action: {action} by {user.email}"
    if details:
        log_message += f" - Details: {details}"
    
    log_func = getattr(logger, level, logger.info)
    log_func(log_message)
