// Analytics hooks using TanStack Query
// Provides reactive analytics data management

import { useQuery } from '@tanstack/react-query'
import { AnalyticsService } from '../services/analytics-service'
import { queryKeys } from '../services/query-keys'

/**
 * Hook for dashboard analytics
 */
export const useAnalytics = (period?: string) => {
  return useQuery({
    queryKey: queryKeys.analytics.salesByPeriod(period || 'month'),
    queryFn: () => AnalyticsService.getDashboardAnalytics(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for sales analytics
 */
export const useSalesAnalytics = (period?: string) => {
  return useQuery({
    queryKey: queryKeys.analytics.salesByPeriod(period || 'month'),
    queryFn: () => AnalyticsService.getSalesAnalytics(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for product analytics
 */
export const useProductAnalytics = (period?: string) => {
  return useQuery({
    queryKey: queryKeys.products.analytics(),
    queryFn: () => AnalyticsService.getProductAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for customer analytics
 */
export const useCustomerAnalytics = (period?: string) => {
  return useQuery({
    queryKey: queryKeys.customers.analytics(),
    queryFn: () => AnalyticsService.getCustomerAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for performance metrics
 */
export const usePerformanceMetrics = (metric?: string) => {
  return useQuery({
    queryKey: queryKeys.analytics.performanceByMetric(metric || 'overview'),
    queryFn: () => AnalyticsService.getPerformanceMetrics(metric),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for generating reports
 */
export const useGenerateReport = () => {
  return useQuery({
    queryKey: queryKeys.analytics.reports(),
    queryFn: AnalyticsService.getAvailableReports,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}
