// Customers hooks using TanStack Query
// Provides reactive customer data management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { CustomerService } from '../services/customer-service'
import { useNotifications } from '../stores/ui-store'
import { queryKeys } from '../services/query-keys'
import { Customer, CustomerFilters, Address } from '../types/api-types'

/**
 * Hook for fetching paginated customers list
 */
export const useCustomers = (filters?: CustomerFilters) => {
  return useQuery({
    queryKey: queryKeys.customers.list(filters || {}),
    queryFn: () => CustomerService.getCustomers(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData,
  })
}

/**
 * Hook for fetching single customer
 */
export const useCustomer = (id: number) => {
  return useQuery({
    queryKey: queryKeys.customers.detail(id),
    queryFn: () => CustomerService.getCustomer(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for updating customer
 */
export const useUpdateCustomer = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Customer>) => CustomerService.updateCustomer(id, data),
    onSuccess: (updatedCustomer) => {
      showSuccess('Customer Updated', `Customer information has been updated successfully.`)
      
      // Update cached data
      queryClient.setQueryData(
        queryKeys.customers.detail(id),
        updatedCustomer
      )
      
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.customers.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update customer.')
    },
  })
}

/**
 * Hook for customer addresses
 */
export const useCustomerAddresses = (customerId: number) => {
  return useQuery({
    queryKey: queryKeys.customers.addresses(customerId),
    queryFn: () => CustomerService.getCustomerAddresses(customerId),
    enabled: !!customerId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating customer address
 */
export const useCreateCustomerAddress = (customerId: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (addressData: Partial<Address>) => 
      CustomerService.createCustomerAddress(customerId, addressData),
    onSuccess: () => {
      showSuccess('Address Added', 'Customer address has been added successfully.')
      
      // Invalidate addresses
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.customers.addresses(customerId) 
      })
    },
    onError: (error: any) => {
      showError('Address Creation Failed', error.message || 'Failed to create address.')
    },
  })
}

/**
 * Hook for updating customer address
 */
export const useUpdateCustomerAddress = (customerId: number, addressId: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (addressData: Partial<Address>) => 
      CustomerService.updateCustomerAddress(customerId, addressId, addressData),
    onSuccess: () => {
      showSuccess('Address Updated', 'Customer address has been updated successfully.')
      
      // Invalidate addresses
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.customers.addresses(customerId) 
      })
    },
    onError: (error: any) => {
      showError('Address Update Failed', error.message || 'Failed to update address.')
    },
  })
}

/**
 * Hook for deleting customer address
 */
export const useDeleteCustomerAddress = (customerId: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (addressId: number) => 
      CustomerService.deleteCustomerAddress(customerId, addressId),
    onSuccess: () => {
      showSuccess('Address Deleted', 'Customer address has been deleted successfully.')
      
      // Invalidate addresses
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.customers.addresses(customerId) 
      })
    },
    onError: (error: any) => {
      showError('Address Deletion Failed', error.message || 'Failed to delete address.')
    },
  })
}

/**
 * Hook for customer orders
 */
export const useCustomerOrders = (customerId: number) => {
  return useQuery({
    queryKey: queryKeys.customers.orders(customerId),
    queryFn: () => CustomerService.getCustomerOrders(customerId),
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for customer activity/support history
 */
export const useCustomerActivity = (customerId: number) => {
  return useQuery({
    queryKey: queryKeys.customers.activity(customerId),
    queryFn: () => CustomerService.getCustomerActivity(customerId),
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for customer analytics
 */
export const useCustomerAnalytics = () => {
  return useQuery({
    queryKey: queryKeys.customers.analytics(),
    queryFn: CustomerService.getCustomerAnalytics,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for customer segments
 */
export const useCustomerSegments = () => {
  return useQuery({
    queryKey: queryKeys.customers.segments(),
    queryFn: CustomerService.getCustomerSegments,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}
