// TanStack Query client configuration with persistence and error handling
// Optimized for admin dashboard with appropriate cache settings

import { QueryClient } from '@tanstack/react-query'
import { persistQueryClient } from '@tanstack/react-query-persist-client'
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister'

// Create query client with admin-optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes by default
      staleTime: 5 * 60 * 1000,
      // Keep data in cache for 10 minutes
      gcTime: 10 * 60 * 1000, // formerly cacheTime
      // Retry configuration
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors except 408 (timeout) and 429 (rate limit)
        if (error?.status >= 400 && error?.status < 500 && ![408, 429].includes(error.status)) {
          return false
        }
        // Retry up to 3 times for other errors
        return failureCount < 3
      },
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch on window focus for critical data
      refetchOnWindowFocus: true,
      // Don't refetch on reconnect by default (can be overridden per query)
      refetchOnReconnect: 'always',
      // Network mode
      networkMode: 'online',
    },
    mutations: {
      // Don't retry mutations by default
      retry: false,
      // Network mode for mutations
      networkMode: 'online',
    },
  },
})

// Create persister for offline support
const localStoragePersister = createSyncStoragePersister({
  storage: window.localStorage,
  key: 'admin-arena-cache',
  serialize: JSON.stringify,
  deserialize: JSON.parse,
})

// Persist queries to localStorage with selective persistence
persistQueryClient({
  queryClient,
  persister: localStoragePersister,
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  // Only persist certain query types
  hydrateOptions: {
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000,
      },
    },
  },
  // Dehydrate options to control what gets persisted
  dehydrateOptions: {
    shouldDehydrateQuery: (query) => {
      // Only persist certain types of queries
      const queryKey = query.queryKey[0] as string

      // Persist user data, permissions, and relatively static data
      const persistableQueries = [
        'auth',
        'staff',
        'categories',
        'brands',
        'product-types',
        'attributes',
      ]

      // Don't persist real-time data like orders, notifications
      const nonPersistableQueries = [
        'orders',
        'notifications',
        'analytics',
        'dashboard',
      ]

      if (nonPersistableQueries.includes(queryKey)) {
        return false
      }

      return persistableQueries.includes(queryKey)
    },
  },
})

// Error handling for queries
queryClient.setMutationDefaults(['auth', 'login'], {
  mutationFn: async (variables: any) => {
    // This will be overridden by actual mutation functions
    throw new Error('Mutation function not implemented')
  },
  onError: (error: any) => {
    console.error('Authentication error:', error)
  },
})

// Global error handler for queries
queryClient.setQueryDefaults(['orders'], {
  staleTime: 2 * 60 * 1000, // Orders data is more dynamic, shorter stale time
  gcTime: 5 * 60 * 1000,
})

queryClient.setQueryDefaults(['products'], {
  staleTime: 10 * 60 * 1000, // Products change less frequently
  gcTime: 15 * 60 * 1000,
})

queryClient.setQueryDefaults(['customers'], {
  staleTime: 5 * 60 * 1000, // Customer data moderate frequency
  gcTime: 10 * 60 * 1000,
})

queryClient.setQueryDefaults(['analytics'], {
  staleTime: 1 * 60 * 1000, // Analytics data should be fresh
  gcTime: 2 * 60 * 1000,
})

// Query client event listeners for debugging in development
if (import.meta.env.DEV) {
  queryClient.getQueryCache().subscribe((event) => {
    if (event.type === 'queryAdded') {
      console.log('Query added:', event.query.queryKey)
    } else if (event.type === 'queryRemoved') {
      console.log('Query removed:', event.query.queryKey)
    }
  })

  queryClient.getMutationCache().subscribe((event) => {
    if (event.type === 'mutationAdded') {
      console.log('Mutation added:', event.mutation.options.mutationKey)
    }
  })
}

// Utility functions for common query operations
export const queryUtils = {
  /**
   * Invalidate all queries for a specific entity
   */
  invalidateEntity: (entity: string) => {
    return queryClient.invalidateQueries({ queryKey: [entity] })
  },

  /**
   * Remove all queries for a specific entity
   */
  removeEntity: (entity: string) => {
    return queryClient.removeQueries({ queryKey: [entity] })
  },

  /**
   * Prefetch a query
   */
  prefetch: (queryKey: any[], queryFn: () => Promise<any>, options?: any) => {
    return queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: 5 * 60 * 1000,
      ...options,
    })
  },

  /**
   * Set query data
   */
  setData: (queryKey: any[], data: any) => {
    return queryClient.setQueryData(queryKey, data)
  },

  /**
   * Get query data
   */
  getData: (queryKey: any[]) => {
    return queryClient.getQueryData(queryKey)
  },

  /**
   * Cancel all outgoing queries
   */
  cancelQueries: () => {
    return queryClient.cancelQueries()
  },

  /**
   * Clear all cached data
   */
  clear: () => {
    return queryClient.clear()
  },

  /**
   * Reset query client to initial state
   */
  reset: () => {
    return queryClient.resetQueries()
  },
}
