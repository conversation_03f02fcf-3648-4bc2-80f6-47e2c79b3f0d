'use client';

import { Fa<PERSON><PERSON><PERSON>, FaRegHeart } from "react-icons/fa"
import { useRouter } from 'next/navigation'
import { useToggleWishlist, useWishlist } from "@/src/hooks/wishlist-hooks"
import authStore from "@/src/stores/auth-store"
import Tooltip from "@/src/components/utils/tooltip/Tooltip"
import styles from './WishlistButton.module.scss'


interface Props {
  productId: number
}

const WishlistButton = ({ productId }: Props) => {
  const mutation = useToggleWishlist()
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { data: wishlistItems } = useWishlist(1, isLoggedIn)

  const isInWishlist = wishlistItems?.results.some(item => item.product.id === productId)

  const handleToggleWishlist = () => {
    if (!isLoggedIn) {
      router.push('/login')
      return
    }
    mutation.mutate(productId)
  }

  return (
    <Tooltip
      content={isInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      position="top"
    >
      <button
        onClick={handleToggleWishlist}
        className={styles.wishlist__button}
        disabled={mutation.isPending}
        aria-label={isInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      >
        {isInWishlist ? <i><FaHeart /></i> : <i><FaRegHeart /></i>}
      </button>
    </Tooltip>
  )
}

export default WishlistButton
