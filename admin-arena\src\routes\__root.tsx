// Root route configuration for Admin Arena
// Provides global layout and context providers

import { createRootRoute, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'react-hot-toast'
import { queryClient } from '../lib/query-client'
import { AdminLayout } from '../components/layouts/AdminLayout'
import { AuthProvider } from '../components/auth/AuthProvider'
import { ErrorBoundary } from '../components/common/ErrorBoundary'

export const Route = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <AdminLayout>
            <Outlet />
          </AdminLayout>

          {/* Global UI Components */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 5000,
              style: {
                background: '#fff',
                color: '#374151',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />

          {/* Development Tools */}
          {import.meta.env.DEV && (
            <>
              <ReactQueryDevtools
                initialIsOpen={false}
              // buttonPosition="bottom-right"
              />
              <TanStackRouterDevtools
                position="bottom-left"
              />
            </>
          )}
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  )
}