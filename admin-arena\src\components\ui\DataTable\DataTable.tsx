// DataTable component for displaying tabular data
// Supports pagination, sorting, loading states, and custom cell rendering

import React from 'react'
import { FiChevronLeft, FiChevronRight, FiMoreHorizontal } from 'react-icons/fi'
import { But<PERSON> } from '../Button'
import { LoadingSpinner } from '../LoadingSpinner'
import styles from './DataTable.module.scss'

export interface DataTableColumn<T = any> {
  key: string
  title: string
  render?: (item: T, index: number) => React.ReactNode
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

export interface DataTablePagination {
  current: number
  pageSize: number
  total: number
  onChange: (page: number) => void
}

interface DataTableProps<T = any> {
  data: T[]
  columns: DataTableColumn<T>[]
  loading?: boolean
  pagination?: DataTablePagination
  emptyMessage?: string
  className?: string
  onRowClick?: (item: T, index: number) => void
}

export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pagination,
  emptyMessage = 'No data available',
  className = '',
  onRowClick,
}: DataTableProps<T>) => {
  const renderCell = (item: T, column: DataTableColumn<T>, index: number) => {
    if (column.render) {
      return column.render(item, index)
    }
    
    const value = item[column.key]
    if (value === null || value === undefined) {
      return <span className={styles.emptyCell}>—</span>
    }
    
    return String(value)
  }

  const renderPagination = () => {
    if (!pagination) return null

    const { current, pageSize, total, onChange } = pagination
    const totalPages = Math.ceil(total / pageSize)
    const startItem = (current - 1) * pageSize + 1
    const endItem = Math.min(current * pageSize, total)

    if (totalPages <= 1) return null

    const getPageNumbers = () => {
      const pages: (number | string)[] = []
      const maxVisible = 7

      if (totalPages <= maxVisible) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        pages.push(1)
        
        if (current > 3) {
          pages.push('...')
        }
        
        const start = Math.max(2, current - 1)
        const end = Math.min(totalPages - 1, current + 1)
        
        for (let i = start; i <= end; i++) {
          pages.push(i)
        }
        
        if (current < totalPages - 2) {
          pages.push('...')
        }
        
        pages.push(totalPages)
      }

      return pages
    }

    return (
      <div className={styles.pagination}>
        <div className={styles.paginationInfo}>
          Showing {startItem} to {endItem} of {total} results
        </div>
        
        <div className={styles.paginationControls}>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onChange(current - 1)}
            disabled={current === 1}
            className={styles.paginationButton}
          >
            <FiChevronLeft />
            Previous
          </Button>
          
          <div className={styles.pageNumbers}>
            {getPageNumbers().map((page, index) => (
              <button
                key={index}
                className={`${styles.pageNumber} ${
                  page === current ? styles.active : ''
                } ${typeof page === 'string' ? styles.ellipsis : ''}`}
                onClick={() => typeof page === 'number' && onChange(page)}
                disabled={typeof page === 'string'}
              >
                {page === '...' ? <FiMoreHorizontal /> : page}
              </button>
            ))}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onChange(current + 1)}
            disabled={current === totalPages}
            className={styles.paginationButton}
          >
            Next
            <FiChevronRight />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.tableWrapper}>
        <table className={styles.table}>
          <thead className={styles.thead}>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`${styles.th} ${styles[`align-${column.align || 'left'}`]}`}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          
          <tbody className={styles.tbody}>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className={styles.loadingCell}>
                  <div className={styles.loadingContent}>
                    <LoadingSpinner size="md" />
                    <span>Loading...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className={styles.emptyCell}>
                  <div className={styles.emptyContent}>
                    <span>{emptyMessage}</span>
                  </div>
                </td>
              </tr>
            ) : (
              data.map((item, index) => (
                <tr
                  key={index}
                  className={`${styles.tr} ${onRowClick ? styles.clickable : ''}`}
                  onClick={() => onRowClick?.(item, index)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`${styles.td} ${styles[`align-${column.align || 'left'}`]}`}
                    >
                      {renderCell(item, column, index)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {renderPagination()}
    </div>
  )
}
