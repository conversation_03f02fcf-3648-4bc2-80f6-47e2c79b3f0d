// Pure cookie-based authentication provider
// Server handles all token management via HTTP-only cookies
// No frontend token refresh needed

import React, { useEffect } from 'react'
import { useAuth } from '../../hooks/use-auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { initializeAuth } = useAuth()

  useEffect(() => {
    // Initialize authentication state on app load
    // This will check if HTTP-only cookies exist and set auth state accordingly
    const initialize = async () => {
      try {
        await initializeAuth()
      } catch (error) {
        // initializeAuth handles errors internally, no need to handle here
        console.debug('Authentication initialization completed')
      }
    }

    initialize()
  }, [initializeAuth])

  // No token refresh interval needed - server handles this automatically
  // HTTP-only cookies are refreshed server-side when needed

  return <>{children}</>
}
