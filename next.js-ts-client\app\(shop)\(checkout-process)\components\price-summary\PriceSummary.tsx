import React from 'react'
import { RiQuestionFill } from 'react-icons/ri'
import styles from './PriceSummary.module.scss'
import Tooltip from '@/src/components/utils/tooltip/Tooltip'

interface PriceSummaryProps {
  totalPrice: number
  shippingCost?: number
  packingCost?: number
  grandTotal?: number
  item_count: number
  cart_weight: number
  onCheckout?: () => void
  isShippingCalculated?: boolean
}

const PriceSummary: React.FC<PriceSummaryProps> = ({
  totalPrice,
  shippingCost,
  packingCost,
  grandTotal,
  item_count,
  cart_weight,
  onCheckout,
  isShippingCalculated = false,
}) => {
  return (
    <div className={styles.cart__checkout}>
      <div>
        <p>Item count: </p> <p>{item_count}</p>
      </div>

      <div>
        <p>
          Cart weight:
          <Tooltip
            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}
            position='top'
          >
            <i>
              <RiQuestionFill />
            </i>
          </Tooltip>
        </p>
        <p>{cart_weight}g</p>
      </div>

      <div>
        <p>Item cost: </p> <p>${totalPrice.toFixed(2)}</p>
      </div>

      {isShippingCalculated && shippingCost !== undefined && (
        <div>
          <p>Shipping cost: </p> <p>${shippingCost.toFixed(2)}</p>
        </div>
      )}

      {isShippingCalculated && packingCost !== undefined && packingCost > 0 && (
        <div>
          <p>Packing cost: </p> <p>${packingCost.toFixed(2)}</p>
        </div>
      )}

      {isShippingCalculated && grandTotal !== undefined && (
        <div className={styles.total}>
          <p>
            <strong>Total: </strong>
          </p>{' '}
          <p>
            <strong>${grandTotal.toFixed(2)}</strong>
          </p>
        </div>
      )}

      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}
    </div>
  )
}

export default PriceSummary
