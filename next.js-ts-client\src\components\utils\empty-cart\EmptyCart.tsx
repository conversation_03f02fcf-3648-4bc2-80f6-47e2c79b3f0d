import Link from 'next/link'
import { FaShoppingCart } from 'react-icons/fa'
import styles from './EmptyCart.module.scss'

interface Props {
  message?: string
  showIcon?: boolean
  actionText?: string
  actionHref?: string
}

const EmptyCart = ({
  message,
  showIcon = true,
  actionText = "Go Shopping",
  actionHref = "/"
}: Props) => {
  return (
    <div className={styles.empty_cart} role="region" aria-label="Empty cart">
      {showIcon && (
        <div className={styles.icon_section} aria-hidden="true">
          <FaShoppingCart />
        </div>
      )}

      <div className={styles.content_section}>
        <h2 className={styles.heading}>
          {message ? message : "Your cart is empty"}
        </h2>
        <p className={styles.description}>
          Add some products to your cart to get started with your shopping journey.
        </p>
      </div>

      <div className={styles.action_section}>
        <Link
          href={actionHref}
          className={styles.action_button}
          aria-label={`${actionText} - Browse products to add to your cart`}
        >
          {actionText}
        </Link>
      </div>
    </div>
  )
}
export default EmptyCart