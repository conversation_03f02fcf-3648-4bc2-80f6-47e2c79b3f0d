@use '@/src/scss/variables' as *;
@use '@/src/scss/mixins' as *;

.table {
  overflow-x: auto;
  margin-top: 20px;
}

.table__orders {
  width: 100%;
  border-collapse: collapse;
  // font-size: $font-size-3;
  color: $primary-dark-text-color;

  th,
  td {
    padding: $padding-3;
    // border-bottom: 1px solid $sky-lighter-blue;
  }

  th {
    text-align: left;
    background-color: $sky-lighter-blue;
    font-weight: bold;
    color: $primary-dark-blue;
  }

  // tr:nth-child(even) {
  //   background-color: $sky-lighter-blue; // Uncomment and style as needed
  // }

  tr:hover {
    background-color: $sky-lighter-blue;
  }

  .action {
    @include flexbox(space-between, center, row);
    column-gap: 10px;
  }
}

.status {
  padding: 6px 10px;
  border-radius: $border-radius-1;
  font-size: $font-size-1;
  font-weight: bold;
  text-transform: uppercase;

  &.Pending {
    background-color: $primary-yellow;
    color: $primary-dark;
  }

  &.Processing {
    background-color: $info-bg;
    color: $info-text;
  }

  &.Shipped {
    background-color: $success-bg;
    color: $success-text;
  }

  &.Paid {
    background-color: $success-bg;
    color: $success-text;
  }

  &.Failed {
    background-color: $error-bg;
    color: $error-text;
  }
}

.viewButton {
  @include btn(white, $lighten-blue);
  text-decoration: none;
  font-size: $font-size-1;
  transition: background-color 0.3s ease;
  text-align: center;

  &:hover {
    background-color: darken($lighten-blue, 10%);
    // color: darken(#fff, 15%);
  }
}

@include mobile {
  .table__orders {
    font-size: $font-size-1;

    th,
    td {
      padding: 8px;
    }
  }

  .action {
    @include flexbox(center, center, column);
    row-gap: 5px;

    .viewButton {
      text-align: center;
      padding: 3px 6px;
    }
  }
}
