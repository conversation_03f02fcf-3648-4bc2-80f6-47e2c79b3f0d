@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.cart__checkout {
  background-color: $sky-lighter-blue;
  padding: 1.5rem;
  @include flexbox(flex-start, flex-start, column);
  gap: 1.5rem;

  div {
    @include flexbox(space-between, center);
    width: 100%;
    font-size: 1.2rem;

    p:first-child {
      font-weight: bold;
      color: $primary-dark-text-color;
    }

    p:last-child {
      font-weight: bold;
      color: $primary-blue;
    }
  }

  div:nth-child(2) {
    p:first-child {
      @include flexbox(flex-start, center);

      // display: inline;
      // width: 100%;
      // display: inline-flex;
      column-gap: 2px;
      // background-color: #803f3f;
    }
  }

  .total {
    border-top: 1px solid $primary-blue;
    padding-top: 1rem;
    margin-top: 0.5rem;

    p {
      font-size: 1.3rem !important;
    }
  }

  button {
    @include btn(#fff, $lighten-blue);
    align-self: center;
    width: 100%;
    padding: 1rem 0;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.7px;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($lighten-blue, 10%);
      color: darken(#fff, 15%);
    }
  }
}
