@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;
@use '../../../../src/scss/animations' as *;

/* Hide the spinners in Chrome, Safari, Edge, and Opera */
// input[type=number]::-webkit-outer-spin-button,
// input[type=number]::-webkit-inner-spin-button {
//   -webkit-appearance: none;
//   margin: 0;
// }

// /* Hide the spinners in Firefox */
// input[type=number] {
//   -moz-appearance: textfield;
// }

.register_container {
  width: 100%;
  background-image: url(../../../../public/images/pc_hardware.jpg);
  background-color: #70829e;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  overflow: hidden; // Add this to prevent the blur from extending outside

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    z-index: 0; // Change this to 0
  }

  // Add this new rule
  >* {
    position: relative;
    z-index: 1;
  }
}


.reset_password {
  padding: $padding-5;
  border-radius: $border-radius-1;
  margin: 2rem auto 0 auto;
  width: 400px;
  align-self: flex-start;
  background-color: #fff;

  /* Animation settings */
  @include slideInAnimation(0.8s, ease-out);
  /* Using the mixin */
  /* Duration of the animation and easing */

  /* Optionally, you can add a delay */
  /* animation-delay: 0.2s; */
}