0000000000000000000000000000000000000000 278064410a68f0d0766881c19d60fafb918cc602 Kanishka <<EMAIL>> 1697863304 +0530	commit (initial): before adding auto product ordering feature
278064410a68f0d0766881c19d60fafb918cc602 c07f187c54a819d60efb3c1d8141faa503034c7e Kanishka <<EMAIL>> 1697888689 +0530	commit: before add product variant images
c07f187c54a819d60efb3c1d8141faa503034c7e ac88686a673728833b18d457623224bebb7b3bd5 Kanishka <<EMAIL>> 1698157454 +0530	commit: before new database design
ac88686a673728833b18d457623224bebb7b3bd5 29f025173e2e0113e6456f930ce0085f36f46394 Ka<PERSON><PERSON> <<EMAIL>> 1698226174 +0530	commit: code refactoring
29f025173e2e0113e6456f930ce0085f36f46394 0000000000000000000000000000000000000000 Kanishka <<EMAIL>> 1698226592 +0530	Branch: renamed refs/heads/master to refs/heads/main
0000000000000000000000000000000000000000 29f025173e2e0113e6456f930ce0085f36f46394 Kanishka <<EMAIL>> 1698226592 +0530	Branch: renamed refs/heads/master to refs/heads/main
29f025173e2e0113e6456f930ce0085f36f46394 29f025173e2e0113e6456f930ce0085f36f46394 Kanishka <<EMAIL>> 1698227069 +0530	checkout: moving from main to picky-pc-hardware-store-v2.0.0
29f025173e2e0113e6456f930ce0085f36f46394 3b2af31faf99174001db11d9e1b115e35749e7ab Kanishka <<EMAIL>> 1699923828 +0530	commit: v2.0.0 first commit
3b2af31faf99174001db11d9e1b115e35749e7ab 3b2af31faf99174001db11d9e1b115e35749e7ab Kanishka <<EMAIL>> 1699924039 +0530	checkout: moving from picky-pc-hardware-store-v2.0.0 to picky-pc-hardware-store-v3.0.0
3b2af31faf99174001db11d9e1b115e35749e7ab 3b2af31faf99174001db11d9e1b115e35749e7ab Kanishka <<EMAIL>> 1699924268 +0530	checkout: moving from picky-pc-hardware-store-v3.0.0 to picky-pc-hardware-store-v3.0.0picky-pc-hardware-store-
3b2af31faf99174001db11d9e1b115e35749e7ab 3b2af31faf99174001db11d9e1b115e35749e7ab Kanishka <<EMAIL>> 1699924521 +0530	checkout: moving from picky-pc-hardware-store-v3.0.0picky-pc-hardware-store- to picky-pc-hardware-store-v3.0.0
3b2af31faf99174001db11d9e1b115e35749e7ab 3b2af31faf99174001db11d9e1b115e35749e7ab Kanishka <<EMAIL>> 1699924552 +0530	checkout: moving from picky-pc-hardware-store-v3.0.0 to picky-pc-hardware-store-v2.0.0
3b2af31faf99174001db11d9e1b115e35749e7ab cc9e88c28849094e9d50128d67f404fcf8d0069e Kanishka <<EMAIL>> 1699926474 +0530	commit: del
cc9e88c28849094e9d50128d67f404fcf8d0069e 29f025173e2e0113e6456f930ce0085f36f46394 Kanishka <<EMAIL>> 1699926479 +0530	checkout: moving from picky-pc-hardware-store-v2.0.0 to main
29f025173e2e0113e6456f930ce0085f36f46394 29f025173e2e0113e6456f930ce0085f36f46394 Kanishka <<EMAIL>> 1699926571 +0530	reset: moving to 29f025173e2e0113e6456f930ce0085f36f46394
29f025173e2e0113e6456f930ce0085f36f46394 3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 Kanishka <<EMAIL>> 1699944817 +0530	commit: stage one completed
3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 cc9e88c28849094e9d50128d67f404fcf8d0069e Kanishka <<EMAIL>> 1699944856 +0530	checkout: moving from main to picky-pc-hardware-store-v2.0.0
cc9e88c28849094e9d50128d67f404fcf8d0069e 46f750208151bcb031dc1f0af7e9e07250f29e98 Kanishka <<EMAIL>> 1699945329 +0530	commit: del
46f750208151bcb031dc1f0af7e9e07250f29e98 3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 Kanishka <<EMAIL>> 1699945343 +0530	checkout: moving from picky-pc-hardware-store-v2.0.0 to main
3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 Kanishka <<EMAIL>> 1699945569 +0530	checkout: moving from main to picky-pc-hardware-store-v2.0.0
3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 ee5d3ce04dd427c5e1ca6eaac96015a045a6d533 Kanishka <<EMAIL>> 1703835760 +0530	commit: before modify order viewSet
ee5d3ce04dd427c5e1ca6eaac96015a045a6d533 cd69ae558d8c61669e25d822c1f9e13a8df5e2b8 Kanishka <<EMAIL>> 1703835920 +0530	commit: before modify order viewSet
cd69ae558d8c61669e25d822c1f9e13a8df5e2b8 cd69ae558d8c61669e25d822c1f9e13a8df5e2b8 Kanishka <<EMAIL>> 1703836100 +0530	checkout: moving from picky-pc-hardware-store-v2.0.0 to picky-pc-hardware-store-v3.0.0
cd69ae558d8c61669e25d822c1f9e13a8df5e2b8 6253d98e3b677a9ae2f1f32ce408c8b25e7150d7 Kanishka <<EMAIL>> 1706830111 +0530	commit: backup
6253d98e3b677a9ae2f1f32ce408c8b25e7150d7 b291dd5c8b81b86a5e629cfbc173fa9d4b62a118 Kanishka <<EMAIL>> 1708946895 +0530	commit: media dir added
b291dd5c8b81b86a5e629cfbc173fa9d4b62a118 3dd1fc95927232d33be9d4ae4ebf6b7c4fb58d09 Kanishka <<EMAIL>> 1712043290 +0530	commit: before reactivating cart API
3dd1fc95927232d33be9d4ae4ebf6b7c4fb58d09 db6f209f959ebc51456b480d853cc11d62bd09d3 Kanishka <<EMAIL>> 1718496807 +0530	commit: back up
db6f209f959ebc51456b480d853cc11d62bd09d3 7a3ff1d07dc7a71ce82f85123f435c7972604c6d Kanishka <<EMAIL>> 1720154029 +0530	commit: deployment attmp 1
7a3ff1d07dc7a71ce82f85123f435c7972604c6d a067c69cec7e01460b5d8b0a1e623d72c9799172 Kanishka <<EMAIL>> 1720312038 +0530	commit: vercel deploy attmp-1
a067c69cec7e01460b5d8b0a1e623d72c9799172 7c51400bbfa4d2920bc4d2ea547a594e2e1ca4be Kanishka <<EMAIL>> 1720364884 +0530	commit: uritemplate==4.1.1; python_version >= 3.6
7c51400bbfa4d2920bc4d2ea547a594e2e1ca4be 97daa0c723374eb46bcb75109d5655ff35d02963 Kanishka <<EMAIL>> 1720447385 +0530	commit: Python version updated on Pipfile
97daa0c723374eb46bcb75109d5655ff35d02963 a6dd42366eb23e25985fe088482442f3590c5809 Kanishka <<EMAIL>> 1720447803 +0530	commit: Python full version updated on Pipfile
a6dd42366eb23e25985fe088482442f3590c5809 c70255d07cda0272c46216e29687683ffb0777a5 Kanishka <<EMAIL>> 1720449833 +0530	commit: Dockerfile updated
c70255d07cda0272c46216e29687683ffb0777a5 225b16b762f11ec5060269075d3741e302d9d4d9 Kanishka <<EMAIL>> 1720451213 +0530	commit: settings dev.py updated
225b16b762f11ec5060269075d3741e302d9d4d9 1824a7d7eff17fa8257b77c9af36d2ae2f9b6bb0 Kanishka <<EMAIL>> 1720451503 +0530	commit: settings dev.py updated
1824a7d7eff17fa8257b77c9af36d2ae2f9b6bb0 7d4450e72f58952bf475617ecf5aefefcba9cc74 Kanishka <<EMAIL>> 1720452779 +0530	commit: Dockerfile updated
7d4450e72f58952bf475617ecf5aefefcba9cc74 a0923eda528c0a709e531727daaeb44a2222c178 Kanishka <<EMAIL>> 1720453420 +0530	commit: Allowed hosts added
a0923eda528c0a709e531727daaeb44a2222c178 e2efa6caafa2fb7e4156f1cfd0546b9d880f3ffc Kanishka <<EMAIL>> 1720453779 +0530	commit: Allowed hosts added
e2efa6caafa2fb7e4156f1cfd0546b9d880f3ffc 34b171dd8b51c5401145525a5464386bb0babdc4 Kanishka <<EMAIL>> 1720662149 +0530	commit: Removed Pycharm default .gitignore
34b171dd8b51c5401145525a5464386bb0babdc4 7c7ff180aa21762c0e199e682937f90dee794ada Kanishka <<EMAIL>> 1720662372 +0530	commit: Removed Pycharm default .gitignore
7c7ff180aa21762c0e199e682937f90dee794ada c3c17b69d7ab54312cbbcab8f8cff2837cb78063 Kanishka <<EMAIL>> 1720733488 +0530	commit: ALLOWED HOSTS changed
c3c17b69d7ab54312cbbcab8f8cff2837cb78063 a13f527659d41afa47a202fa0c09660ad39adf25 Kanishka <<EMAIL>> 1720748823 +0530	commit: render.yaml file added
a13f527659d41afa47a202fa0c09660ad39adf25 7c784ef96c7535ac390ee51e85e7d5503b0b1652 Kanishka <<EMAIL>> 1720752102 +0530	commit: render.yaml file updated
7c784ef96c7535ac390ee51e85e7d5503b0b1652 76b74f2185c6a4f7be942d017085acb507539f32 Kanishka <<EMAIL>> 1720795362 +0530	commit: packages updated
76b74f2185c6a4f7be942d017085acb507539f32 c4a4fa159652eb6c14fe82bc9bfc87c51bea49d5 Kanishka <<EMAIL>> 1720796967 +0530	commit: social auth env updated
c4a4fa159652eb6c14fe82bc9bfc87c51bea49d5 5e997f7629760596f140ccc31c9abb41b31c89c1 Kanishka <<EMAIL>> 1725159535 +0530	commit: Just after adding ProductFilterOptionsViewSet
5e997f7629760596f140ccc31c9abb41b31c89c1 ebf9f2c374ae62b53bef6dd7051dbf329675eccc Kanishka <<EMAIL>> 1726235842 +0530	commit: before adding phone number sign up option
ebf9f2c374ae62b53bef6dd7051dbf329675eccc 56f9738ed96b7292bd85e4fb1e6035695ea4b7d1 Kanishka <<EMAIL>> 1735830174 +0530	commit: after a big change
56f9738ed96b7292bd85e4fb1e6035695ea4b7d1 76760a444c32a1691902a216aaa7ca541745efd9 dev-kevin-de <<EMAIL>> 1735877588 +0530	commit: dockerfile updated
76760a444c32a1691902a216aaa7ca541745efd9 226765156863168cd798b25310edd438dddcb8ae dev-kevin-de <<EMAIL>> 1735880329 +0530	commit: Update ALLOWED_HOSTS for Koyeb deployment
226765156863168cd798b25310edd438dddcb8ae 5bc9acabe7ec202d6e72545247c4d2a001d1d6fc dev-kani <<EMAIL>> 1739271852 +0530	commit: before merging branches
5bc9acabe7ec202d6e72545247c4d2a001d1d6fc 3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 dev-kani <<EMAIL>> 1739272048 +0530	checkout: moving from picky-pc-hardware-store-v3.0.0 to main
3faaf90b856056a5ad1d2d0e3b6c9e9f7e3289c7 5bc9acabe7ec202d6e72545247c4d2a001d1d6fc dev-kani <<EMAIL>> 1739272788 +0530	merge picky-pc-hardware-store-v3.0.0: Fast-forward
5bc9acabe7ec202d6e72545247c4d2a001d1d6fc 5bc9acabe7ec202d6e72545247c4d2a001d1d6fc dev-kani <<EMAIL>> 1739273817 +0530	checkout: moving from main to Feature
5bc9acabe7ec202d6e72545247c4d2a001d1d6fc cdf1e45d0d03e63ca848183d19828cb580ca5cc2 dev-kani <<EMAIL>> 1739278418 +0530	commit: first-ci-cd test
cdf1e45d0d03e63ca848183d19828cb580ca5cc2 5bc9acabe7ec202d6e72545247c4d2a001d1d6fc dev-kani <<EMAIL>> 1739278589 +0530	checkout: moving from Feature to main
5bc9acabe7ec202d6e72545247c4d2a001d1d6fc cdf1e45d0d03e63ca848183d19828cb580ca5cc2 dev-kani <<EMAIL>> 1739278612 +0530	merge Feature: Fast-forward
cdf1e45d0d03e63ca848183d19828cb580ca5cc2 cdf1e45d0d03e63ca848183d19828cb580ca5cc2 dev-kani <<EMAIL>> 1739280103 +0530	checkout: moving from main to Feature
cdf1e45d0d03e63ca848183d19828cb580ca5cc2 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739280521 +0530	commit: migrations configaration updated
d6df113964f532b33dc52c4735c2e13fb8203554 cdf1e45d0d03e63ca848183d19828cb580ca5cc2 dev-kani <<EMAIL>> 1739280548 +0530	checkout: moving from Feature to main
cdf1e45d0d03e63ca848183d19828cb580ca5cc2 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739280625 +0530	merge Feature: Fast-forward
d6df113964f532b33dc52c4735c2e13fb8203554 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739281387 +0530	checkout: moving from main to Feature
d6df113964f532b33dc52c4735c2e13fb8203554 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739282562 +0530	checkout: moving from Feature to main
d6df113964f532b33dc52c4735c2e13fb8203554 d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739282584 +0530	checkout: moving from main to Feature
d6df113964f532b33dc52c4735c2e13fb8203554 1f62d06b884c29b5bfa3629df18a660b1a5f87ef dev-kani <<EMAIL>> 1739282715 +0530	commit: ci-cd file modified
1f62d06b884c29b5bfa3629df18a660b1a5f87ef d6df113964f532b33dc52c4735c2e13fb8203554 dev-kani <<EMAIL>> 1739282739 +0530	checkout: moving from Feature to main
d6df113964f532b33dc52c4735c2e13fb8203554 1f62d06b884c29b5bfa3629df18a660b1a5f87ef dev-kani <<EMAIL>> 1739282753 +0530	merge Feature: Fast-forward
1f62d06b884c29b5bfa3629df18a660b1a5f87ef 22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 dev-kani <<EMAIL>> 1739282932 +0530	commit: ci-cd file modified
22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 1f62d06b884c29b5bfa3629df18a660b1a5f87ef dev-kani <<EMAIL>> 1739305588 +0530	checkout: moving from main to Feature
1f62d06b884c29b5bfa3629df18a660b1a5f87ef 32ffddb400e6762b7f51e44a62407daa7224cbc5 dev-kani <<EMAIL>> 1739305732 +0530	commit: ci-cd configs modified
32ffddb400e6762b7f51e44a62407daa7224cbc5 22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 dev-kani <<EMAIL>> 1739306709 +0530	checkout: moving from Feature to main
22a89adb5c5c2aa865d2ed8005e7dfb73773e1d5 517fb0b026a3e37e29dd07bb7a887298f426b5fb dev-kani <<EMAIL>> 1739306789 +0530	commit: removed pytest env configs
517fb0b026a3e37e29dd07bb7a887298f426b5fb 32ffddb400e6762b7f51e44a62407daa7224cbc5 dev-kani <<EMAIL>> 1739306828 +0530	checkout: moving from main to Feature
32ffddb400e6762b7f51e44a62407daa7224cbc5 42a5b8d66f4f376bc35f40453b480a1ef751ecfb dev-kani <<EMAIL>> 1739307996 +0530	commit: pytest configs updated
42a5b8d66f4f376bc35f40453b480a1ef751ecfb 517fb0b026a3e37e29dd07bb7a887298f426b5fb dev-kani <<EMAIL>> 1739308201 +0530	checkout: moving from Feature to main
517fb0b026a3e37e29dd07bb7a887298f426b5fb 517fb0b026a3e37e29dd07bb7a887298f426b5fb dev-kani <<EMAIL>> 1739308641 +0530	reset: moving to HEAD
517fb0b026a3e37e29dd07bb7a887298f426b5fb fb3aa27624609b93b6b74919eabbebd316b36cf7 dev-kani <<EMAIL>> 1739308872 +0530	commit: pytest config updated
fb3aa27624609b93b6b74919eabbebd316b36cf7 278185872c87e6318f62cf3fffbe0c5abe408df2 dev-kani <<EMAIL>> 1739322136 +0530	commit: removed .vscode, disabled ci-cd
278185872c87e6318f62cf3fffbe0c5abe408df2 7dca269345e449e20b83b440c4a335bccf1ecf84 dev-kani <<EMAIL>> 1741416344 +0530	commit: dockerfiles and entrypoint.sh updated
7dca269345e449e20b83b440c4a335bccf1ecf84 dd9debb5ae5e7803e0fa47b94bed409a9265132a dev-kani <<EMAIL>> 1741437539 +0530	commit: dockerfile and entrypoint.sh updated
dd9debb5ae5e7803e0fa47b94bed409a9265132a 62bb6a6eb7bd2bd1e3f6d35c50bfdfb46dd2c015 dev-kani <<EMAIL>> 1741438738 +0530	commit: dockerfiles and entrypoint.sh updated
62bb6a6eb7bd2bd1e3f6d35c50bfdfb46dd2c015 8eddd78f8dd47686c38d772028c0c24516d10fc6 dev-kani <<EMAIL>> 1741444401 +0530	commit: log settings updated
8eddd78f8dd47686c38d772028c0c24516d10fc6 1a7bfa15e8617b188e8e70b359419e805e2f4686 dev-kani <<EMAIL>> 1741447063 +0530	commit: Dockerfile updated
1a7bfa15e8617b188e8e70b359419e805e2f4686 3604f56c3f9c56e0bad2ab8da6bcc25ee70a0557 dev-kani <<EMAIL>> 1741456186 +0530	commit: production db settings updated
3604f56c3f9c56e0bad2ab8da6bcc25ee70a0557 216fa6d6b34474a0e0316b719aa57597db12cbc2 dev-kani <<EMAIL>> 1741511804 +0530	commit: production/common whitenoise setting updated
216fa6d6b34474a0e0316b719aa57597db12cbc2 4aea29318fa863ab7069d5e01068fa242869186d dev-kani <<EMAIL>> 1741512778 +0530	commit: entrypoint.sh updated to workers 2
4aea29318fa863ab7069d5e01068fa242869186d 17282a3c42712d1ae9af0e13d2ac77c428a611c3 dev-kani <<EMAIL>> 1741514953 +0530	commit: entrypoint.sh updated
17282a3c42712d1ae9af0e13d2ac77c428a611c3 ecc1116dd3ad4d069b7237c0710285ef16502340 dev-kani <<EMAIL>> 1745333056 +0530	commit: before adding ER diagram
ecc1116dd3ad4d069b7237c0710285ef16502340 5900001bfc10306798a119b015b4412920b9a073 dev-kani <<EMAIL>> 1746926119 +0530	commit: before changing product attribute value links
5900001bfc10306798a119b015b4412920b9a073 5900001bfc10306798a119b015b4412920b9a073 dev-kani <<EMAIL>> 1747065233 +0530	reset: moving to 5900001bfc10306798a119b015b4412920b9a073
5900001bfc10306798a119b015b4412920b9a073 bb63937ce895905f0735bfb1c85a3fb8b960ceb9 dev-kani <<EMAIL>> 1747752936 +0530	commit: before adding attribute filters to the frontend
bb63937ce895905f0735bfb1c85a3fb8b960ceb9 f31c92d5d1ae0664835acab317eaecdbc3bb6f07 dev-kani <<EMAIL>> 1749362903 +0530	commit: pc breaks down
f31c92d5d1ae0664835acab317eaecdbc3bb6f07 1b4942dd97b318d2b2a01902828d08024a630263 Kanishka Samarathunga <<EMAIL>> 1751189987 +0530	commit: before adding Staff app
1b4942dd97b318d2b2a01902828d08024a630263 0000000000000000000000000000000000000000 Kanishka Samarathunga <<EMAIL>> 1751192809 +0530	Branch: renamed refs/heads/main to refs/heads/main
1b4942dd97b318d2b2a01902828d08024a630263 1b4942dd97b318d2b2a01902828d08024a630263 Kanishka Samarathunga <<EMAIL>> 1751192809 +0530	Branch: renamed refs/heads/main to refs/heads/main
1b4942dd97b318d2b2a01902828d08024a630263 8abadae4b0aa176ddff87153f0bbba00a205a336 Kanishka Samarathunga <<EMAIL>> 1751959701 +0530	commit: before adding order-mgt-endpoints
