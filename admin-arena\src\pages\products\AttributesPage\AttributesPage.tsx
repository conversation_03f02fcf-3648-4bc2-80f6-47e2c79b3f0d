// Attributes management page
// Allows viewing, creating, editing, and deleting product attributes

import React, { useState, useMemo } from 'react'
import { FiPlus, FiEdit, FiTrash2, FiSearch, FiTool } from 'react-icons/fi'
import {
  useAttributes,
  useCreateAttribute,
  useUpdateAttribute,
  useDeleteAttribute
} from '../../../hooks/products-hooks/use-attributes'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Modal } from '../../../components/ui/Modal'
import { DataTable } from '../../../components/ui/DataTable/DataTable'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { Attribute } from '../../../types/api-types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import styles from './AttributesPage.module.scss'

const attributeSchema = z.object({
  title: z.string().min(1, 'Attribute name is required'),
})

type AttributeFormData = z.infer<typeof attributeSchema>

export const AttributesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingAttribute, setEditingAttribute] = useState<Attribute | null>(null)

  const { data: attributes, isLoading } = useAttributes()
  const createAttributeMutation = useCreateAttribute()
  const updateAttributeMutation = useUpdateAttribute()
  const deleteAttributeMutation = useDeleteAttribute()



  // Get mutation pending states
  const isFormPending = createAttributeMutation.isPending || updateAttributeMutation.isPending

  const {
    register,
    handleSubmit,
    reset: resetForm,
    formState: { errors },
  } = useForm<AttributeFormData>({
    resolver: zodResolver(attributeSchema),
  })

  // Filter attributes based on search term
  const filteredAttributes = useMemo(() => {
    if (!attributes) return []

    return attributes.filter(attribute =>
      attribute.title.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [attributes, searchTerm])

  const handleCreateAttribute = async (data: AttributeFormData) => {
    try {
      await createAttributeMutation.mutateAsync(data)
      setIsCreateModalOpen(false)
      resetForm()
    } catch {
      // Error is handled by the mutation
    }
  }

  const handleUpdateAttribute = async (data: AttributeFormData) => {
    if (!editingAttribute) return

    try {
      await updateAttributeMutation.mutateAsync({
        id: editingAttribute.id,
        data,
      })
      setEditingAttribute(null)
      resetForm()
    } catch {
      // Error is handled by the mutation
    }
  }

  const handleEditAttribute = (attribute: Attribute) => {
    setEditingAttribute(attribute)
    resetForm({
      title: attribute.title,
    })
  }

  const handleDeleteAttribute = async (attribute: Attribute) => {
    if (window.confirm(`Are you sure you want to delete "${attribute.title}"? This action cannot be undone.`)) {
      try {
        await deleteAttributeMutation.mutateAsync(attribute.id)
      } catch {
        // Error is handled by the mutation
      }
    }
  }

  const columns = [
    {
      key: 'title',
      title: 'Name',
      render: (attribute: Attribute) => (
        <div className={styles.nameCell}>
          <div className={styles.nameIcon}>
            <FiTool />
          </div>
          <div>
            <div className={styles.name}>{attribute.title}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'values_count',
      title: 'Values',
      render: (attribute: Attribute) => (
        <Badge variant="secondary">
          {attribute.values_count || 0} values
        </Badge>
      ),
    },
    {
      key: 'product_types',
      title: 'Product Types',
      render: (attribute: Attribute) => (
        <Badge variant="info">
          {attribute.product_types?.length || 0} types
        </Badge>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (attribute: Attribute) => (
        <div className={styles.actions}>
          <PermissionGuard permission="staff.change_attribute">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditAttribute(attribute)}
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
          <PermissionGuard permission="staff.delete_attribute">
            <Button
              variant="ghost"
              size="sm"
              className={styles.deleteButton}
              onClick={() => handleDeleteAttribute(attribute)}
            >
              <FiTrash2 />
            </Button>
          </PermissionGuard>
        </div>
      ),
    },
  ]

  if (isLoading) {
    return <PageLoading message="Loading attributes..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Attributes</h1>
          <p className={styles.subtitle}>
            Manage product attributes like color, size, material, etc.
          </p>
        </div>

        <div className={styles.actions}>
          <PermissionGuard permission="staff.add_attribute">
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
              className={styles.createButton}
            >
              <FiPlus />
              Add Attribute
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <Card className={styles.attributesCard}>
        <CardHeader>
          <div className={styles.cardHeader}>
            <h2>All Attributes</h2>
            <div className={styles.searchContainer}>
              <div className={styles.searchBox}>
                <FiSearch className={styles.searchIcon} />
                <Input
                  type="text"
                  placeholder="Search attributes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={styles.searchInput}
                />
              </div>
            </div>
          </div>
        </CardHeader>

        <CardBody>
          <DataTable
            data={filteredAttributes}
            columns={columns}
            emptyMessage="No attributes found"
          />
        </CardBody>
      </Card>

      {/* Create/Edit Attribute Modal */}
      <Modal
        isOpen={isCreateModalOpen || !!editingAttribute}
        onClose={() => {
          setIsCreateModalOpen(false)
          setEditingAttribute(null)
          resetForm({ title: '' })
        }}
        title={editingAttribute ? 'Edit Attribute' : 'Create Attribute'}
      >
        <form
          onSubmit={handleSubmit(editingAttribute ? handleUpdateAttribute : handleCreateAttribute)}
          className={styles.form}
        >
          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Attribute Title *
            </label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Enter attribute name (e.g., Color, Size, Material)"
              error={errors.title?.message}
              disabled={isFormPending}
            />
          </div>

          <div className={styles.formActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsCreateModalOpen(false)
                setEditingAttribute(null)
                resetForm()
              }}
              disabled={isFormPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              className={`${styles.submitButton} ${styles.primaryButton}`}
              disabled={isFormPending}
            >
              {isFormPending ? (
                editingAttribute ? 'Updating Attribute...' : 'Creating Attribute...'
              ) : (
                editingAttribute ? 'Update Attribute' : 'Create Attribute'
              )}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  )
}
