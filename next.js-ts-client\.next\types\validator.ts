// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../app/(account)/account/orders/page.tsx
{
  const handler = {} as typeof import("../../app/(account)/account/orders/page.js")
  handler satisfies AppPageConfig<"/account/orders">
}

// Validate ../../app/(account)/account/profile/page.tsx
{
  const handler = {} as typeof import("../../app/(account)/account/profile/page.js")
  handler satisfies AppPageConfig<"/account/profile">
}

// Validate ../../app/(account)/account/wishlist/page.tsx
{
  const handler = {} as typeof import("../../app/(account)/account/wishlist/page.js")
  handler satisfies AppPageConfig<"/account/wishlist">
}

// Validate ../../app/(auth)/change-auth-info/email/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/change-auth-info/email/page.js")
  handler satisfies AppPageConfig<"/change-auth-info/email">
}

// Validate ../../app/(auth)/change-auth-info/phone/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/change-auth-info/phone/page.js")
  handler satisfies AppPageConfig<"/change-auth-info/phone">
}

// Validate ../../app/(auth)/change-auth-info/verify/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/change-auth-info/verify/page.js")
  handler satisfies AppPageConfig<"/change-auth-info/verify">
}

// Validate ../../app/(auth)/change-password/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/change-password/page.js")
  handler satisfies AppPageConfig<"/change-password">
}

// Validate ../../app/(auth)/login/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/login/page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ../../app/(auth)/register/create-customer/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/create-customer/page.js")
  handler satisfies AppPageConfig<"/register/create-customer">
}

// Validate ../../app/(auth)/register/initiate/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/initiate/page.js")
  handler satisfies AppPageConfig<"/register/initiate">
}

// Validate ../../app/(auth)/register/set-password/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/set-password/page.js")
  handler satisfies AppPageConfig<"/register/set-password">
}

// Validate ../../app/(auth)/register/update-info/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/update-info/page.js")
  handler satisfies AppPageConfig<"/register/update-info">
}

// Validate ../../app/(auth)/register/verify/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/verify/page.js")
  handler satisfies AppPageConfig<"/register/verify">
}

// Validate ../../app/(auth)/reset-password/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/reset-password/page.js")
  handler satisfies AppPageConfig<"/reset-password">
}

// Validate ../../app/(shop)/(checkout-process)/checkout/address-choice/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(checkout-process)/checkout/address-choice/page.js")
  handler satisfies AppPageConfig<"/checkout/address-choice">
}

// Validate ../../app/(shop)/(checkout-process)/checkout/cart/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(checkout-process)/checkout/cart/page.js")
  handler satisfies AppPageConfig<"/checkout/cart">
}

// Validate ../../app/(shop)/(checkout-process)/checkout/order/[id]/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(checkout-process)/checkout/order/[id]/page.js")
  handler satisfies AppPageConfig<"/checkout/order/[id]">
}

// Validate ../../app/(shop)/(checkout-process)/checkout/payment-choice/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(checkout-process)/checkout/payment-choice/page.js")
  handler satisfies AppPageConfig<"/checkout/payment-choice">
}

// Validate ../../app/(shop)/(products)/product/[slug]/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(products)/product/[slug]/page.js")
  handler satisfies AppPageConfig<"/product/[slug]">
}

// Validate ../../app/(shop)/(products)/products/category/[slug]/page.tsx
{
  const handler = {} as typeof import("../../app/(shop)/(products)/products/category/[slug]/page.js")
  handler satisfies AppPageConfig<"/products/category/[slug]">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}







// Validate ../../app/(account)/account/layout.tsx
{
  const handler = {} as typeof import("../../app/(account)/account/layout.js")
  handler satisfies LayoutConfig<"/account">
}

// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
