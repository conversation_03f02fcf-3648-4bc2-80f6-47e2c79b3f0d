{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}{{ block.super }}
<script src="{% url 'admin:jsi18n' %}"></script>
{{ media }}
<!-- Include jQuery and Select2 for enhanced dropdowns -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<style>
    .management-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .attribute-selection {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007cba;
    }
    
    .attribute-selection h3 {
        margin-top: 0;
        color: #007cba;
    }
    
    .values-container {
        margin-top: 30px;
    }
    
    .existing-values {
        margin-bottom: 30px;
    }
    
    .existing-values h4 {
        color: #28a745;
        border-bottom: 2px solid #28a745;
        padding-bottom: 10px;
    }
    
    .new-values h4 {
        color: #17a2b8;
        border-bottom: 2px solid #17a2b8;
        padding-bottom: 10px;
    }
    
    .value-row {
        display: grid;
        grid-template-columns: 2fr auto auto;
        align-items: center;
        gap: 15px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: #f9f9f9;
        transition: all 0.3s ease;
        min-height: 60px;
    }
    
    .value-row.saved {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    
    .value-row.editing {
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }
    
    .value-row.error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    
    .value-field {
        min-width: 250px;
    }
    
    .checkbox-field {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
        min-width: 100px;
    }
    
    .checkbox-field label {
        font-size: 12px;
        margin: 0;
        cursor: pointer;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        min-width: 120px;
    }
    
    .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s ease;
    }
    
    .btn-save {
        background-color: #28a745;
        color: white;
    }
    
    .btn-save:hover {
        background-color: #218838;
    }
    
    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-edit:hover {
        background-color: #e0a800;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
    }
    
    .btn-delete:hover {
        background-color: #c82333;
    }
    
    .btn-cancel {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-cancel:hover {
        background-color: #5a6268;
    }
    
    .btn-add-row {
        background-color: #17a2b8;
        color: white;
        padding: 10px 20px;
        margin-top: 15px;
    }
    
    .btn-add-row:hover {
        background-color: #138496;
    }
    
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
    
    .success-message, .error-message {
        padding: 10px 15px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
    }
    
    .success-message {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .hidden {
        display: none;
    }
    
    .value-name {
        font-weight: bold;
        color: #495057;
    }
    
    .no-values {
        text-align: center;
        padding: 40px;
        color: #6c757d;
        font-style: italic;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
    }
    
    .form-check-input {
        margin: 0;
        transform: scale(1.2);
    }
    
    /* Select2 customization */
    .select2-container {
        width: 100% !important;
    }
    
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 12px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 24px;
        color: #495057;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    
    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 4px;
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
    }
    
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007cba;
    }
    
    /* Attribute selection styling */
    .attribute-selection .form-control,
    .attribute-selection .select2-container {
        max-width: 400px;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .value-row {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .checkbox-field {
            justify-content: flex-start;
        }
        
        .action-buttons {
            justify-content: flex-start;
        }
        
        .management-container {
            margin: 10px;
            padding: 15px;
        }
    }
    
    /* Better spacing for form elements */
    .attribute-selection form > div {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }
    
    /* Loading state improvements */
    .btn.loading {
        position: relative;
        color: transparent;
    }
    
    .btn.loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }
    
    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007cba;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="management-container">
    <h1>{{ title }}</h1>
    
    <div class="attribute-selection">
        <h3>📋 Select Attribute</h3>
        <p>Choose an attribute to view and manage its values.</p>
        
        <form id="attribute-form">
            {% csrf_token %}
            <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 300px;">
                    {{ attribute_form.attribute }}
                </div>
                <button type="button" id="load-values-btn" class="btn btn-save" disabled>
                    Load Values
                </button>
            </div>
            {% if attribute_form.attribute.help_text %}
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    {{ attribute_form.attribute.help_text }}
                </div>
            {% endif %}
        </form>
    </div>
    
    <div id="messages-container"></div>
    
    <div id="values-container" class="values-container hidden">
        <div class="existing-values">
            <h4>🔗 Existing Values</h4>
            <div id="existing-values-list">
                <div class="no-values">
                    No existing values found.
                </div>
            </div>
        </div>
        
        <div class="new-values">
            <h4>➕ Add New Values</h4>
            <div id="new-values-list">
                <!-- New value rows will be added here -->
            </div>
            <button type="button" id="add-new-row-btn" class="btn btn-add-row">
                + Add Another Value
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const attributeSelect = document.getElementById('id_attribute_selector');
    const loadBtn = document.getElementById('load-values-btn');
    const valuesContainer = document.getElementById('values-container');
    const existingList = document.getElementById('existing-values-list');
    const newList = document.getElementById('new-values-list');
    const addRowBtn = document.getElementById('add-new-row-btn');
    const messagesContainer = document.getElementById('messages-container');

    let currentAttributeId = null;

    // Initialize Select2 for attribute selector
    $(attributeSelect).select2({
        placeholder: 'Search and select an attribute...',
        allowClear: true,
        width: '100%'
    });

    // Enable/disable load button based on selection (using Select2 events)
    $(attributeSelect).on('change', function() {
        updateLoadButtonState();
        if (!this.value) {
            valuesContainer.classList.add('hidden');
            currentAttributeId = null;
        }
        console.log('Attribute changed:', this.value);
    });

    // Load values when button is clicked
    loadBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Load button clicked!');
        console.log('Button disabled:', loadBtn.disabled);
        console.log('Attribute value:', attributeSelect.value);

        const attributeId = attributeSelect.value;

        if (!attributeId || attributeId === '') {
            showMessage('Please select an attribute first', 'error');
            return;
        }

        currentAttributeId = attributeId;
        loadExistingValues(attributeId);
    });

    // Add new row button
    addRowBtn.addEventListener('click', function() {
        addNewValueRow();
    });

    // Initialize button state
    function updateLoadButtonState() {
        const hasValue = attributeSelect.value && attributeSelect.value !== '';
        loadBtn.disabled = !hasValue;
        console.log('Button state updated - disabled:', loadBtn.disabled, 'value:', attributeSelect.value);
    }

    // Check initial state
    updateLoadButtonState();

    function loadExistingValues(attributeId) {
        console.log('Loading values for attribute:', attributeId);
        showLoading(loadBtn);

        const url = `{% url 'admin:products_ajax_load_values' %}?attribute_id=${attributeId}`;
        console.log('Fetching URL:', url);

        fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                hideLoading(loadBtn);

                if (data.success) {
                    displayExistingValues(data.values);
                    valuesContainer.classList.remove('hidden');

                    // Clear and add initial new rows
                    newList.innerHTML = '';
                    for (let i = 0; i < 3; i++) {
                        addNewValueRow();
                    }
                    showMessage(`Loaded ${data.values.length} existing values`, 'success');
                } else {
                    showMessage('Error loading values: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                hideLoading(loadBtn);
                showMessage('Error: ' + error.message, 'error');
            });
    }

    function displayExistingValues(values) {
        if (values.length === 0) {
            existingList.innerHTML = '<div class="no-values">No existing values found.</div>';
            return;
        }

        existingList.innerHTML = '';
        values.forEach(value => {
            const row = createExistingValueRow(value);
            existingList.appendChild(row);
        });
    }

    function createExistingValueRow(value) {
        const row = document.createElement('div');
        row.className = 'value-row saved';
        row.dataset.valueId = value.id;

        row.innerHTML = `
            <div class="value-field">
                <span class="value-name">${value.attribute_value}</span>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" ${value.is_active ? 'checked' : ''} disabled>
                <label>Is active</label>
            </div>
            <div class="action-buttons">
                <button type="button" class="btn btn-edit" onclick="editValue(this)">Edit</button>
                <button type="button" class="btn btn-delete" onclick="deleteValue(this)">Delete</button>
            </div>
        `;

        return row;
    }

    function addNewValueRow() {
        const row = document.createElement('div');
        row.className = 'value-row';
        const timestamp = Date.now();

        row.innerHTML = `
            <div class="value-field">
                <input type="text" class="form-control value-input" placeholder="Enter attribute value..." required>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" class="form-check-input is-active" id="active_${timestamp}" checked>
                <label for="active_${timestamp}">Is active</label>
            </div>
            <div class="action-buttons">
                <button type="button" class="btn btn-save" onclick="saveNewValue(this)">Save</button>
            </div>
        `;

        newList.appendChild(row);
    }

    function showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        messageDiv.textContent = message;

        messagesContainer.innerHTML = '';
        messagesContainer.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    function showLoading(button) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.classList.add('loading');
    }

    function hideLoading(button) {
        button.disabled = false;
        button.classList.remove('loading');
        button.textContent = button.dataset.originalText || button.textContent;
    }

    // Global functions for button actions
    window.editValue = function(button) {
        const row = button.closest('.value-row');
        const valueSpan = row.querySelector('.value-name');
        const checkbox = row.querySelector('input[type="checkbox"]');
        const currentValue = valueSpan.textContent;

        // Replace span with input
        valueSpan.outerHTML = `<input type="text" class="form-control value-input" value="${currentValue}">`;

        // Enable checkbox
        checkbox.disabled = false;

        // Change buttons
        const actionButtons = row.querySelector('.action-buttons');
        actionButtons.innerHTML = `
            <button type="button" class="btn btn-save" onclick="updateValue(this)">Update</button>
            <button type="button" class="btn btn-cancel" onclick="cancelEdit(this)">Cancel</button>
        `;

        row.classList.add('editing');
    };

    window.updateValue = function(button) {
        const row = button.closest('.value-row');
        const valueId = row.dataset.valueId;
        const valueInput = row.querySelector('.value-input');
        const isActive = row.querySelector('input[type="checkbox"]').checked;
        const newValue = valueInput.value.trim();

        if (!newValue) {
            showMessage('Please enter a value', 'error');
            return;
        }

        showLoading(button);

        fetch('{% url "admin:products_ajax_update_value" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                value_id: valueId,
                attribute_value: newValue,
                is_active: isActive
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(button);

            if (data.success) {
                showMessage(data.message, 'success');

                // Replace input with span
                valueInput.outerHTML = `<span class="value-name">${newValue}</span>`;

                // Disable checkbox
                const checkbox = row.querySelector('input[type="checkbox"]');
                checkbox.disabled = true;

                const actionButtons = row.querySelector('.action-buttons');
                actionButtons.innerHTML = `
                    <button type="button" class="btn btn-edit" onclick="editValue(this)">Edit</button>
                    <button type="button" class="btn btn-delete" onclick="deleteValue(this)">Delete</button>
                `;

                row.classList.remove('editing');
                row.classList.add('saved');
            } else {
                showMessage('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };

    window.cancelEdit = function(button) {
        // Reload the values to restore original state
        if (currentAttributeId) {
            loadExistingValues(currentAttributeId);
        }
    };

    window.deleteValue = function(button) {
        if (!confirm('Are you sure you want to delete this attribute value?')) {
            return;
        }

        const row = button.closest('.value-row');
        const valueId = row.dataset.valueId;

        showLoading(button);

        fetch('{% url "admin:products_ajax_delete_value" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                value_id: valueId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                row.remove();
            } else {
                hideLoading(button);
                showMessage('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };

    window.saveNewValue = function(button) {
        const row = button.closest('.value-row');
        const valueInput = row.querySelector('.value-input');
        const isActive = row.querySelector('.is-active').checked;
        const newValue = valueInput.value.trim();

        if (!newValue) {
            showMessage('Please enter a value', 'error');
            return;
        }

        showLoading(button);

        fetch('{% url "admin:products_ajax_save_value" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                attribute_id: currentAttributeId,
                attribute_value: newValue,
                is_active: isActive
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(button);

            if (data.success) {
                showMessage(data.message, 'success');

                // Move this row to existing values
                const value = {
                    id: data.value_id,
                    attribute_value: data.value_display,
                    is_active: isActive
                };

                const existingRow = createExistingValueRow(value);
                existingList.appendChild(existingRow);

                // Remove the "no values" message if it exists
                const noValues = existingList.querySelector('.no-values');
                if (noValues) {
                    noValues.remove();
                }

                // Remove this row from new values
                row.remove();

                // Add a new empty row
                addNewValueRow();
            } else {
                showMessage('Error: ' + data.error, 'error');
                row.classList.add('error');
                setTimeout(() => row.classList.remove('error'), 3000);
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };
});
</script>
{% endblock %}
