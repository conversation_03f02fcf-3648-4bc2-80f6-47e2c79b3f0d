"""
Cart Selection Service

Handles cart item selection operations including individual selection,
bulk operations, and selection state management.
"""

import logging
from typing import Dict, Any, List, Optional
from django.db import transaction
from django.core.exceptions import ValidationError

from ..models import Cart, CartItem
from .base import BaseCartService, ServiceTransaction


class CartSelectionService(BaseCartService):
    """Service for handling cart item selection operations"""
    
    def toggle_item_selection(self, cart_item: CartItem, is_selected: bool) -> Dict[str, Any]:
        """
        Toggle selection state of a single cart item

        Args:
            cart_item: CartItem instance
            is_selected: New selection state

        Returns:
            Dictionary with operation result
        """
        return self.execute_with_error_handling(
            f"toggle selection for cart item {cart_item.id}",
            self._toggle_item_selection_impl,
            cart_item,
            is_selected
        )

    def _toggle_item_selection_impl(self, cart_item: CartItem, is_selected: bool) -> Dict[str, Any]:
        """Implementation of item selection toggle"""
        with ServiceTransaction(self, f"toggle item {cart_item.id} selection"):
            cart_item.is_selected = is_selected
            cart_item.save(update_fields=['is_selected'])

            return self.create_success_response(
                'Item selection updated successfully',
                {
                    'item_id': cart_item.id,
                    'is_selected': is_selected
                }
            )
    
    def bulk_select_items(self, cart: Cart, item_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Select multiple cart items in bulk
        
        Args:
            cart: Cart instance
            item_ids: List of cart item IDs to select. If None, selects all items
            
        Returns:
            Dictionary with operation result
        """
        try:
            with transaction.atomic():
                if item_ids is None:
                    # Select all items in cart
                    updated_count = cart.cart_items.update(is_selected=True)
                    message = f"Selected all {updated_count} items"
                else:
                    # Select specific items
                    updated_count = cart.cart_items.filter(
                        id__in=item_ids
                    ).update(is_selected=True)
                    message = f"Selected {updated_count} items"
                
                self.logger.info(f"Bulk select operation on cart {cart.id}: {message}")
                
                return {
                    'success': True,
                    'updated_count': updated_count,
                    'message': message
                }
                
        except Exception as e:
            self.logger.error(f"Error in bulk select for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to select items'
            }
    
    def bulk_deselect_items(self, cart: Cart, item_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Deselect multiple cart items in bulk
        
        Args:
            cart: Cart instance
            item_ids: List of cart item IDs to deselect. If None, deselects all items
            
        Returns:
            Dictionary with operation result
        """
        try:
            with transaction.atomic():
                if item_ids is None:
                    # Deselect all items in cart
                    updated_count = cart.cart_items.update(is_selected=False)
                    message = f"Deselected all {updated_count} items"
                else:
                    # Deselect specific items
                    updated_count = cart.cart_items.filter(
                        id__in=item_ids
                    ).update(is_selected=False)
                    message = f"Deselected {updated_count} items"
                
                self.logger.info(f"Bulk deselect operation on cart {cart.id}: {message}")
                
                return {
                    'success': True,
                    'updated_count': updated_count,
                    'message': message
                }
                
        except Exception as e:
            self.logger.error(f"Error in bulk deselect for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to deselect items'
            }
    
    def get_selection_summary(self, cart: Cart) -> Dict[str, Any]:
        """
        Get summary of cart item selection state
        
        Args:
            cart: Cart instance
            
        Returns:
            Dictionary with selection summary
        """
        try:
            total_items = cart.cart_items.count()
            selected_items = cart.cart_items.filter(is_selected=True).count()
            
            return {
                'total_items': total_items,
                'selected_items': selected_items,
                'unselected_items': total_items - selected_items,
                'all_selected': total_items == selected_items if total_items > 0 else False,
                'none_selected': selected_items == 0,
                'partial_selection': 0 < selected_items < total_items
            }
            
        except Exception as e:
            self.logger.error(f"Error getting selection summary for cart {cart.id}: {e}")
            return {
                'total_items': 0,
                'selected_items': 0,
                'unselected_items': 0,
                'all_selected': False,
                'none_selected': True,
                'partial_selection': False
            }
    
    def validate_selection_for_checkout(self, cart: Cart) -> Dict[str, Any]:
        """
        Validate that cart has valid selection for checkout
        
        Args:
            cart: Cart instance
            
        Returns:
            Dictionary with validation result
        """
        try:
            selected_items = cart.cart_items.filter(is_selected=True)
            
            if not selected_items.exists():
                return {
                    'valid': False,
                    'error': 'No items selected for checkout',
                    'message': 'Please select at least one item to proceed with checkout'
                }
            
            # Additional validation can be added here
            # e.g., check stock availability, quantity limits, etc.
            
            return {
                'valid': True,
                'selected_count': selected_items.count(),
                'message': 'Selection is valid for checkout'
            }
            
        except Exception as e:
            self.logger.error(f"Error validating selection for cart {cart.id}: {e}")
            return {
                'valid': False,
                'error': str(e),
                'message': 'Failed to validate selection'
            }
