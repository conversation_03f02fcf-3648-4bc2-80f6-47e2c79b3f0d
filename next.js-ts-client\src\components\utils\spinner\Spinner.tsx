'use client'

import SyncLoader from 'react-spinners/SyncLoader'
import <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'
import PulseLoader from 'react-spinners/PulseLoader'
import RiseLoader from 'react-spinners/RiseLoader'
import RotateLoader from 'react-spinners/RotateLoader'
import ScaleLoader from 'react-spinners/ScaleLoader'
import CircleLoader from 'react-spinners/CircleLoader'
import Underlay from '../underlay/Underlay'

interface Props {
  loading: boolean
  color?: string
  size?: number
  thickness?: number
  bgOpacity?: number
  useUnderlay?: boolean
  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'
}

const Spinner = ({
  loading,
  color,
  size = 150,
  thickness = 5,
  bgOpacity,
  useUnderlay = false,
  spinnerType = 'sync'
}: Props) => {
  const renderSpinner = () => {
    const commonProps = {
      color,
      loading,
      size,
      thickness,
      'aria-label': 'Loading Spinner',
    }

    switch (spinnerType) {
      case 'clip':
        return <ClipLoader {...commonProps} />
      case 'pulse':
        return <PulseLoader {...commonProps} />
      case 'rise':
        return <RiseLoader {...commonProps} />
      case 'rotate':
        return <RotateLoader {...commonProps} />
      case 'scale':
        return <ScaleLoader {...commonProps} />
      case 'circle':
        return <CircleLoader {...commonProps} />
      case 'sync':
      default:
        return <SyncLoader {...commonProps} />
    }
  }

  if (!useUnderlay) {
    return loading ? renderSpinner() : null
  }

  return (
    <Underlay isOpen={loading} bgOpacity={bgOpacity}>
      {renderSpinner()}
    </Underlay>
  )
}

export default Spinner