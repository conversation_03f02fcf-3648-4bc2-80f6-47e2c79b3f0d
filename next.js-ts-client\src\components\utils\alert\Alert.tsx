import styles from './Alert.module.scss'

interface Props {
  variant: 'info' | 'warning' | 'error' | 'success'
  message: string
  textSize?: string
  textAlign?: 'left' | 'right' | 'center' | 'justify'
  highlightWords?: string[] // New prop to pass words for highlighting
}

const Alert = ({ variant, message, textSize, textAlign = 'left', highlightWords = [] }: Props) => {

  const escapeRegExp = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special characters
  }

  const getHighlightedMessage = (text: string) => {
    if (!highlightWords.length) {
      return text
    }

    // Escape each word in the highlightWords array
    const escapedHighlightWords = highlightWords.map(word => escapeRegExp(word))
    const parts = text.split(new RegExp(`(${escapedHighlightWords.join('|')})`, 'gi'))

    return parts.map((part, index) => (
      highlightWords.some(word => word.toLowerCase() === part.toLowerCase())
        ? <span key={index} className={styles.highlight}>{part}</span>
        : part
    ))
  }


  return (
    <div className={`${styles[variant]} ${styles.alert}`}>
      <p style={{
        fontSize: `${textSize}px`,
        textAlign: textAlign
      }}>
        {getHighlightedMessage(message)}
      </p>
    </div>
  )
}

export default Alert
