import styles from './ProfileLoading.module.scss'

export default function ProfileLoading() {
  return (
    <div className={styles.profileLoading}>
      <h1 className={styles.title}></h1>

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}></h2>
        <div className={styles.formGroup}>
          <div className={`${styles.label} ${styles.pulse}`}></div>
          <div className={`${styles.input} ${styles.pulse}`}></div>
        </div>
        <div className={styles.formGroup}>
          <div className={`${styles.label} ${styles.pulse}`}></div>
          <div className={`${styles.input} ${styles.pulse}`}></div>
        </div>
        <div className={styles.formGroup}>
          <div className={`${styles.label} ${styles.pulse}`}></div>
          <div className={`${styles.input} ${styles.pulse}`}></div>
        </div>
      </div>

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}></h2>
        <div className={styles.formGroup}>
          <div className={`${styles.label} ${styles.pulse}`}></div>
          <div className={`${styles.input} ${styles.pulse}`}></div>
        </div>
        <div className={styles.formGroup}>
          <div className={`${styles.label} ${styles.pulse}`}></div>
          <div className={`${styles.input} ${styles.pulse}`}></div>
        </div>
      </div>

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}></h2>
        <div className={styles.addressList}>
          {[1, 2].map((i) => (
            <div key={i} className={styles.addressCard}>
              <div className={`${styles.addressType} ${styles.pulse}`}></div>
              <div className={`${styles.addressLine} ${styles.pulse}`}></div>
              <div className={`${styles.addressLine} ${styles.pulse}`}></div>
              <div className={`${styles.addressLine} ${styles.pulse}`}></div>
              <div className={styles.addressActions}>
                <div className={`${styles.button} ${styles.pulse}`}></div>
                <div className={`${styles.button} ${styles.pulse}`}></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className={styles.section}>
        <div className={`${styles.saveButton} ${styles.pulse}`}></div>
      </div>
    </div>
  )
}
