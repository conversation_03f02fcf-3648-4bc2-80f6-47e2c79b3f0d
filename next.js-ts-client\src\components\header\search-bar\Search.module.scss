@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;
@use 'sass:color';
@use 'sass:map';

.search {
  position: relative;
  width: 100%;

  form {
    @include flexbox(flex-start, stretch);
    width: 100%;

    input {
      flex-grow: 1;
      padding: 0.2rem 0.3rem;
      border: 1px solid #ccc;
      border-radius: 4px 0 0 0;
      font-size: 1rem;

      &:focus {
        outline: none;
      }

      &::selection {
        background-color: $sky-lighter-blue;
        color: $primary-dark-blue;
      }
    }

    button {
      @include btn(#fff, $primary-dark-blue);
      border: none;
      border-radius: 0 4px 4px 0;
      padding: 0.6rem;
      cursor: pointer;
      @include flexbox(center, center);

      i {
        font-size: 1.3rem;
      }
    }
  }

  .search_suggestions {
    position: absolute;
    // top: 100%;
    // left: 0;
    width: 100%;
    z-index: 5;

    .backdrop {
      position: fixed;
      // top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 4;
    }

    .suggestions {
      position: relative;
      background-color: #fff;
      border: 1px solid #ccc;
      border-top: none;
      border-radius: 0 0 4px 4px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      z-index: 6;
      max-height: 300px;
      overflow-y: auto;
      // padding: 0.5rem;
    }

    .category_item {
      position: relative;
      border-bottom: 1px solid color.adjust($sky-light-blue, $lightness: 10%);
      transition: background-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: color.adjust($sky-lighter-blue, $lightness: 8%);
      }

      &.focused {
        background-color: $sky-lighter-blue;
        outline: 2px solid $primary-blue;
        outline-offset: -2px;
      }

      &.expandable {
        .category_header {
          cursor: pointer;
        }
      }

      .category_header {
        @include flexbox(flex-start, center);
        padding: $padding-1 $padding-2;
        min-height: 40px;

        .expand_toggle {
          @include flexbox(center, center);
          width: 24px;
          height: 24px;
          margin-right: $padding-1;
          background: none;
          border: none;
          cursor: pointer;
          border-radius: $border-radius-1;
          color: $primary-lighter-text-color;
          transition: all 0.2s ease;

          &:hover {
            background-color: $sky-light-blue;
            color: $primary-dark-blue;
          }

          &:focus {
            outline: 2px solid $primary-blue;
            outline-offset: 2px;
          }

          svg {
            font-size: 14px;
            transition: transform 0.2s ease;
          }

          &.expanded svg {
            transform: rotate(90deg);
          }
        }

        .category_link {
          @include flexbox(space-between, center);
          flex: 1;
          padding: $padding-1 $padding-2;
          color: $primary-dark-text-color !important; // Force text visibility
          text-decoration: none;
          border-radius: $border-radius-1;
          transition: all 0.2s ease;

          &:hover {
            background-color: $sky-light-blue;
            color: $primary-dark-blue !important; // Force hover text visibility
            text-decoration: underline; // Add underline on hover
          }

          &:focus {
            outline: 2px solid $primary-blue;
            outline-offset: 2px;
          }

          &.parent_category {
            .category_title {
              font-weight: map.get($font-weight, 'medium');
            }
          }

          &.leaf_category {
            .category_title {
              font-weight: map.get($font-weight, 'regular');
            }
          }

          .category_title {
            font-size: $font-size-3;
            color: inherit; // Inherit color from parent link

            // Different styles for hierarchy levels
            &.level_0 {
              font-weight: map.get($font-weight, 'medium');
            }

            &.level_1 {
              font-weight: map.get($font-weight, 'regular');
            }

            &.level_2 {
              font-weight: map.get($font-weight, 'regular');
              font-size: $font-size-1;
            }

            &.level_3 {
              font-weight: map.get($font-weight, 'light');
              font-size: $font-size-1;
            }
          }

          .product_count {
            font-size: $font-size-1;
            color: $primary-lighter-text-color;
            font-weight: map.get($font-weight, 'regular');
            margin-left: $padding-1;
          }
        }
      }

      // Tree-like structure for child categories with navigation lines
      .child_categories {
        position: relative;
        background-color: color.adjust($sky-lighter-blue, $lightness: 5%);
        margin-left: 12px;

        // Add vertical navigation line
        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 0;
          bottom: 0;
          width: 2px;
          background-color: $sky-light-blue;
        }

        .category_item {
          position: relative;
          border-bottom: 1px solid
            color.adjust($sky-light-blue, $lightness: 15%);

          // Add horizontal navigation line for each child
          &::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 20px;
            width: 6px;
            height: 2px;
            background-color: $sky-light-blue;
          }

          &:hover {
            background-color: color.adjust($sky-lighter-blue, $lightness: 3%);
          }

          // Enhanced hover effect for child category links
          .category_header .category_link {
            color: $primary-dark-text-color !important; // Ensure child text is visible

            &:hover {
              text-decoration: underline;
              background-color: $sky-light-blue;
              color: $primary-dark-blue !important;
            }
          }
        }
      }
    }

    .no_suggestions {
      display: block;
      padding: $padding-3;
      color: $primary-lighter-text-color;
      font-style: italic;
      text-align: center;
    }

    // Mobile responsive styles
    @include mobile {
      .category_item {
        .category_header {
          padding: $padding-1 $padding-2;
          min-height: 44px; // Touch-friendly minimum

          // Enhanced touch interactions
          &:active {
            background-color: $sky-light-blue;
            transform: scale(0.98);
            transition: all 0.1s ease;
          }

          .expand_toggle {
            width: 32px;
            height: 32px;
            margin-right: $padding-2;

            // Better touch target
            &:active {
              background-color: $primary-blue;
              color: white;
              transform: scale(0.95);
            }

            svg {
              font-size: 16px;
            }
          }

          .category_link {
            padding: $padding-1;
            min-height: 44px;
            @include flexbox(space-between, center);

            // Touch feedback with underline
            &:active {
              background-color: $sky-light-blue;
              transform: scale(0.98);
              text-decoration: underline;
            }

            &:hover {
              text-decoration: underline; // Ensure underline on mobile hover/touch
            }

            .category_title {
              font-size: $font-size-3;
              line-height: 1.4;

              &.level_2,
              &.level_3 {
                font-size: $font-size-3;
              }
            }

            .product_count {
              font-size: $font-size-3;
            }
          }
        }

        .child_categories {
          margin-left: 12px;

          // Adjust navigation lines for mobile
          &::before {
            left: -6px;
          }

          .category_item {
            &::before {
              left: -6px;
              width: 4px;
            }

            // Smoother animations on mobile
            animation: slideIn 0.2s ease-out;
          }
        }
      }

      // Improved scrolling on mobile
      .suggestions {
        max-height: 60vh;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
      }
    }

    // Animation for mobile category expansion
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}
