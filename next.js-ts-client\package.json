{"name": "next.js-ts-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@paypal/react-paypal-js": "^8.8.3", "@stripe/react-stripe-js": "^3.9.1", "@stripe/stripe-js": "^7.8.0", "@tanstack/react-query": "^5.85.5", "axios": "^1.11.0", "classnames": "^2.5.1", "dompurify": "^3.2.6", "lodash": "^4.17.21", "luxon": "^3.7.1", "next": "15.5.2", "react": "19.1.1", "react-country-state-city": "^1.1.12", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-spinners": "^0.17.0", "sass": "^1.90.0", "use-local-storage-state": "^19.5.0", "zod": "^4.0.17", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@hookform/devtools": "^4.4.0", "@tanstack/react-query-devtools": "^5.87.1", "@types/classnames": "^2.3.0", "@types/dompurify": "^3.0.5", "@types/lodash": "^4.17.20", "@types/luxon": "^3.7.1", "@types/node": "^20", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "eslint": "^9", "eslint-config-next": "15.5.2", "typescript": "^5"}, "overrides": {"@types/react": "19.1.12", "@types/react-dom": "19.1.9"}}