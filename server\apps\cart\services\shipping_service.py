"""
Cart Shipping Service

Wrapper service that integrates with existing shipping services but focuses
on selected cart items only. Handles shipping and packing calculations.
"""

import logging
from typing import Dict, Any, Optional, List
from decimal import Decimal
from django.db import transaction
from django.utils import timezone

from ..models import Cart
from apps.shipping.services.on_demand import OnDemandShippingService
from apps.shipping.services.packing import PackingService
from apps.shipping.services.shipping import ShippingService


class CartShippingService:
    """Service for handling cart shipping calculations focused on selected items"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.on_demand_service = OnDemandShippingService()
        self.packing_service = PackingService()
        self.shipping_service = ShippingService()
    
    def calculate_shipping_for_selected_items(
        self, 
        cart: Cart, 
        destination_address: Optional[Any] = None,
        get_all_options: bool = False
    ) -> Dict[str, Any]:
        """
        Calculate shipping costs for selected cart items only
        
        Args:
            cart: Cart instance
            destination_address: Destination address for shipping calculation
            get_all_options: Whether to return all shipping options or just the best one
            
        Returns:
            Dictionary with shipping calculation results
        """
        try:
            # Use the existing on-demand service but ensure it focuses on selected items
            result = self.on_demand_service.calculate_shipping_for_cart(
                cart=cart,
                destination_address=destination_address,
                get_all_options=get_all_options,
                selected_only=True  # Force selected items only
            )
            
            if result['success']:
                # Update cart with calculated shipping data
                self._update_cart_shipping_fields(cart, result)
                
                self.logger.info(
                    f"Shipping calculated for selected items in cart {cart.id}: "
                    f"${result.get('shipping_cost', 0)}"
                )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating shipping for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to calculate shipping for selected items'
            }
    
    def get_shipping_estimate_for_selected_items(
        self, 
        cart: Cart, 
        destination_zone: str = 'domestic'
    ) -> Dict[str, Any]:
        """
        Get rough shipping estimate for selected items without full calculation
        
        Args:
            cart: Cart instance
            destination_zone: 'domestic' or 'international'
            
        Returns:
            Dictionary with shipping estimate
        """
        try:
            # Get selected items weight
            selected_weight = cart.get_selected_cart_weight()
            
            if selected_weight == 0:
                return {
                    'success': False,
                    'error': 'No selected items',
                    'message': 'Please select items to get shipping estimate'
                }
            
            # Use simple weight-based estimation
            estimated_cost = self._calculate_weight_based_estimate(selected_weight, destination_zone)
            
            return {
                'success': True,
                'estimated_cost': float(estimated_cost),
                'weight': float(selected_weight),
                'zone': destination_zone,
                'note': 'This is a rough estimate based on selected items only. Calculate exact shipping before checkout.'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting shipping estimate for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to get shipping estimate'
            }
    
    def validate_shipping_calculation(self, cart: Cart) -> Dict[str, Any]:
        """
        Validate that shipping has been calculated for selected items
        
        Args:
            cart: Cart instance
            
        Returns:
            Dictionary with validation result
        """
        try:
            # Check if shipping has been calculated recently
            if not cart.last_shipping_calculation:
                return {
                    'valid': False,
                    'error': 'Shipping not calculated',
                    'message': 'Please calculate shipping before proceeding to checkout'
                }
            
            # Check if selected items have changed since last calculation
            # This is a simplified check - in production you might want more sophisticated tracking
            selected_items_count = cart.cart_items.filter(is_selected=True).count()
            if selected_items_count == 0:
                return {
                    'valid': False,
                    'error': 'No selected items',
                    'message': 'Please select items before calculating shipping'
                }
            
            # Check if shipping cost is reasonable
            if cart.shipping_cost < 0:
                return {
                    'valid': False,
                    'error': 'Invalid shipping cost',
                    'message': 'Shipping calculation appears to be invalid'
                }
            
            return {
                'valid': True,
                'message': 'Shipping calculation is valid',
                'shipping_cost': float(cart.shipping_cost),
                'packing_cost': float(cart.packing_cost),
                'calculated_at': cart.last_shipping_calculation
            }
            
        except Exception as e:
            self.logger.error(f"Error validating shipping for cart {cart.id}: {e}")
            return {
                'valid': False,
                'error': str(e),
                'message': 'Failed to validate shipping calculation'
            }
    
    def clear_shipping_calculation(self, cart: Cart) -> Dict[str, Any]:
        """
        Clear shipping calculation data from cart
        
        Args:
            cart: Cart instance
            
        Returns:
            Dictionary with operation result
        """
        try:
            with transaction.atomic():
                cart.shipping_cost = Decimal('0.00')
                cart.packing_cost = Decimal('0.00')
                cart.total_weight = Decimal('0.00')
                cart.total_volume = Decimal('0.0000')
                cart.last_shipping_calculation = None
                cart.packing_details = {}
                cart.save(update_fields=[
                    'shipping_cost', 'packing_cost', 'total_weight', 
                    'total_volume', 'last_shipping_calculation', 'packing_details'
                ])
                
                self.logger.info(f"Shipping calculation cleared for cart {cart.id}")
                
                return {
                    'success': True,
                    'message': 'Shipping calculation cleared'
                }
                
        except Exception as e:
            self.logger.error(f"Error clearing shipping for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to clear shipping calculation'
            }
    
    def _update_cart_shipping_fields(self, cart: Cart, shipping_result: Dict[str, Any]) -> None:
        """Update cart with shipping calculation results"""
        try:
            with transaction.atomic():
                cart.shipping_cost = Decimal(str(shipping_result.get('shipping_cost', 0)))
                cart.packing_cost = Decimal(str(shipping_result.get('packing_cost', 0)))
                cart.total_weight = Decimal(str(shipping_result.get('total_weight', 0)))
                cart.total_volume = Decimal(str(shipping_result.get('total_volume', 0)))
                cart.last_shipping_calculation = timezone.now()
                cart.packing_details = shipping_result.get('packing_details', {})
                
                cart.save(update_fields=[
                    'shipping_cost', 'packing_cost', 'total_weight',
                    'total_volume', 'last_shipping_calculation', 'packing_details'
                ])
                
        except Exception as e:
            self.logger.error(f"Error updating cart shipping fields for cart {cart.id}: {e}")
            raise
    
    def _calculate_weight_based_estimate(self, weight: Decimal, zone: str) -> Decimal:
        """Calculate simple weight-based shipping estimate"""
        weight_grams = float(weight)
        
        # Simple weight brackets
        if weight_grams <= 500:  # 500g
            base_cost = Decimal('10.00')
        elif weight_grams <= 2000:  # 2kg
            base_cost = Decimal('15.00')
        elif weight_grams <= 5000:  # 5kg
            base_cost = Decimal('25.00')
        elif weight_grams <= 10000:  # 10kg
            base_cost = Decimal('35.00')
        else:  # Over 10kg
            base_cost = Decimal('50.00')
        
        # Adjust for international shipping
        if zone == 'international':
            base_cost *= Decimal('2.5')
        
        return base_cost
