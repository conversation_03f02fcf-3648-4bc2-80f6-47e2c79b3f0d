@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

// Skeleton animation
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Base skeleton styling
%skeleton-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: $border-radius-1;
}

.filter_skeleton {
  padding: $padding-4;
  background: white;
  border-radius: $border-radius-3;
}

.filter_section {
  margin-bottom: $padding-5;
  padding-bottom: $padding-4;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.filter_title_skeleton {
  @extend %skeleton-base;
  height: 18px;
  width: 100px;
  margin-bottom: $padding-3;
}

.price_range_skeleton {
  margin-top: $padding-3;

  .range_item {
    @include flexbox(space-between, center);
    margin-bottom: $padding-3;
    padding: $padding-2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: $border-radius-2;
    border: 1px solid #dee2e6;

    .range_label_skeleton {
      @extend %skeleton-base;
      height: 14px;
      width: 30px;
    }

    .range_slider_skeleton {
      @extend %skeleton-base;
      height: 6px;
      flex: 1;
      margin: 0 $padding-2;
    }

    .range_value_skeleton {
      @extend %skeleton-base;
      height: 14px;
      width: 50px;
    }
  }
}

.filter_option_skeleton {
  @include flexbox(flex-start, center);
  margin-bottom: $padding-2;
  padding: $padding-1 $padding-2;
  border-radius: $border-radius-2;
  gap: $padding-2;

  .checkbox_skeleton,
  .radio_skeleton {
    @extend %skeleton-base;
    width: 16px;
    height: 16px;
  }

  .radio_skeleton {
    border-radius: 50%;
  }

  .option_label_skeleton {
    @extend %skeleton-base;
    height: 14px;
    width: 80px;

    &:nth-child(2n) {
      width: 100px;
    }

    &:nth-child(3n) {
      width: 60px;
    }
  }
}

.attribute_filters_skeleton {
  margin-top: $padding-4;

  .attribute_section {
    margin-bottom: $padding-4;
    padding: $padding-3;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: $border-radius-2;
    border: 1px solid #e9ecef;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .attribute_title_skeleton {
    @extend %skeleton-base;
    height: 16px;
    width: 90px;
    margin-bottom: $padding-3;
  }
}

// Mobile responsive adjustments
@media (max-width: $tablet) {
  .filter_skeleton {
    padding: $padding-3;
  }

  .price_range_skeleton {
    .range_item {
      flex-direction: column;
      gap: $padding-2;
      align-items: stretch;

      .range_label_skeleton {
        align-self: flex-start;
      }

      .range_slider_skeleton {
        width: 100%;
        margin: 0;
      }

      .range_value_skeleton {
        align-self: flex-end;
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  %skeleton-base {
    animation: none;
    background: #f0f0f0;
  }
}
