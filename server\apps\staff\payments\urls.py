from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

app_name = 'staff_payments'

# Create router and register viewsets
router = DefaultRouter()

# Payment management
router.register(r'payment-options', views.PaymentOptionStaffViewSet, basename='payment-options')
router.register(r'paypal-orders', views.PayPalOrderStaffViewSet, basename='paypal-orders')
router.register(r'transaction-audits', views.PaymentTransactionAuditViewSet, basename='transaction-audits')
router.register(r'disputes', views.PaymentDisputeViewSet, basename='disputes')

urlpatterns = [
    path('', include(router.urls)),
]
