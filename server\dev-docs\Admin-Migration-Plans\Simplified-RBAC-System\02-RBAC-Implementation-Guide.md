# 02: Enhanced RBAC Implementation Guide (v2.0)

## **Architectural Improvements Overview**

This implementation guide reflects the enhanced RBAC system with the following key improvements:

### **Key Changes in v2.0**

- **Permission-based authorization** instead of hardcoded role checks
- **Custom permissions** for specialized operations
- **Service layer consistency** for all business logic
- **Circular dependency prevention** in staff hierarchy
- **Enhanced audit logging** with comprehensive action types

---

## **Phase 1: Foundation Setup**

### **Step 1.1: Create Staff App**

```bash
# Create the staff app (already done)
python manage.py startapp staff apps/staff
```

### **Step 1.2: Update Settings**

```python
# pc_hardware/settings/common.py

# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... existing apps
    'apps.staff',
]

# Update JWT settings for staff tokens
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=8),  # Extended for staff work
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
})

# Staff-specific settings
STAFF_SESSION_TIMEOUT = 8 * 60 * 60  # 8 hours in seconds
```

### **Step 1.3: Create Enhanced Models**

```python
# apps/staff/authorization/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.exceptions import ValidationError

User = get_user_model()

class StaffProfile(models.Model):
    """
    Extended profile for staff users with organizational information
    """
    DEPARTMENT_CHOICES = [
        ('PRODUCT', 'Product Management'),
        ('ORDER', 'Order Management'),
        ('CUSTOMER', 'Customer Management'),
        ('CONTENT', 'Content Management'),
        ('FINANCE', 'Finance & Analytics'),
        ('ADMIN', 'Administration'),
        ('IT', 'Information Technology'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('ON_LEAVE', 'On Leave'),
        ('TERMINATED', 'Terminated'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='staff_profile')
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=20, choices=DEPARTMENT_CHOICES)
    position_title = models.CharField(max_length=100)
    manager = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='direct_reports')
    hire_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Role(Group):
    """
    Proxy model for Django's Group model to provide role-specific functionality
    """
    class Meta:
        proxy = True
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def get_permissions(self):
        return self.permissions.all()

    def get_permission_codenames(self):
        return list(self.permissions.values_list('codename', flat=True))

    @property
    def member_count(self):
        return self.user_set.filter(is_active=True).count()

class GroupMembership(models.Model):
    """
    Track group assignments with additional metadata
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='staff_group_memberships')
    group = models.ForeignKey(Group, on_delete=models.CASCADE, related_name='staff_memberships')
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='assigned_staff_memberships')
    assigned_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user.email} in {self.group.name}"

class PermissionAudit(models.Model):
    """
    Enhanced audit logging for all permission-related actions
    """
    ACTION_CHOICES = [
        # Group/Role Management
        ('group_created', 'Group Created'),
        ('group_updated', 'Group Updated'),
        ('group_deleted', 'Group Deleted'),
        ('role_created', 'Role Created'),
        ('role_updated', 'Role Updated'),
        ('role_deleted', 'Role Deleted'),

        # User-Group Assignments
        ('user_added_to_group', 'User Added to Group'),
        ('user_removed_from_group', 'User Removed from Group'),
        ('bulk_users_assigned', 'Bulk Users Assigned'),

        # Permission Management
        ('permission_granted', 'Permission Granted'),
        ('permission_revoked', 'Permission Revoked'),
        ('permission_added_to_role', 'Permission Added to Role'),
        ('permission_removed_from_role', 'Permission Removed from Role'),

        # Staff Management
        ('staff_user_created', 'Staff User Created'),
        ('staff_profile_created', 'Staff Profile Created'),
        ('staff_profile_updated', 'Staff Profile Updated'),
        ('staff_status_changed', 'Staff Status Changed'),
        ('user_staff_toggled', 'User Staff Status Toggled'),

        # Security Events
        ('unauthorized_access_attempt', 'Unauthorized Access Attempt'),
        ('permission_check_failed', 'Permission Check Failed'),
        ('suspicious_activity', 'Suspicious Activity'),
    ]
    
    action = models.CharField(max_length=50, choices=ACTION_CHOICES, db_index=True)
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    target_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='permission_audits')
    target_group = models.ForeignKey(Group, on_delete=models.SET_NULL, null=True)
    details = models.JSONField(default=dict, help_text="Additional details about the action")
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['performed_by', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.action} by {self.performed_by} at {self.timestamp}"

class APIAccessLog(models.Model):
    """
    Track API access for monitoring and security
    """
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    endpoint = models.CharField(max_length=255, db_index=True)
    method = models.CharField(max_length=10)
    status_code = models.IntegerField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    response_time = models.FloatField(help_text="Response time in seconds")
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['endpoint', 'timestamp']),
            models.Index(fields=['status_code', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.method} {self.endpoint} - {self.status_code}"
```

### **Step 1.4: Create Custom Permission Classes**

```python
# apps/staff/permissions.py
from rest_framework.permissions import BasePermission
from django.contrib.auth.models import Group
from .services import AuditService

class IsStaffUser(BasePermission):
    """
    Permission class to check if user is staff
    Used for general admin API access
    """
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_staff
        )

class IsSuperUser(BasePermission):
    """
    Permission class to check if user is superuser
    Used for system administration endpoints
    """
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_superuser
        )

class HasGroupPermission(BasePermission):
    """
    Permission class to check if user belongs to required groups
    Usage: Set required_groups on the view
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Superuser has all permissions
        if request.user.is_superuser:
            return True
        
        # Check if view has required_groups attribute
        required_groups = getattr(view, 'required_groups', [])
        if not required_groups:
            return True  # No specific groups required
        
        # Check if user belongs to any of the required groups
        user_groups = request.user.groups.values_list('name', flat=True)
        return any(group in user_groups for group in required_groups)

class HasSpecificPermission(BasePermission):
    """
    Permission class to check if user has specific Django permissions
    Usage: Set required_permissions on the view
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Superuser has all permissions
        if request.user.is_superuser:
            return True
        
        # Check if view has required_permissions attribute
        required_permissions = getattr(view, 'required_permissions', [])
        if not required_permissions:
            return True  # No specific permissions required
        
        # Check if user has any of the required permissions
        return any(request.user.has_perm(perm) for perm in required_permissions)

class CanManageGroups(BasePermission):
    """
    Permission class for group management operations
    Only staff users can manage groups
    """
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_staff
        )

class CanManageUsers(BasePermission):
    """
    Permission class for user management operations
    Only superusers can manage users
    """
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_superuser
        )
```

### **Step 1.5: Create Service Classes**

```python
# apps/staff/services.py
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.cache import cache
from django.utils import timezone
from .models import GroupMembership, PermissionAudit, APIAccessLog
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class GroupService:
    """
    Service class for group-related operations
    Handles group creation, membership management, and permission assignment
    """
    
    def create_group(self, name, permissions=None, created_by=None):
        """
        Create a new group with optional permissions
        """
        group, created = Group.objects.get_or_create(name=name)
        
        if created and permissions:
            # Add permissions to the group
            permission_objects = Permission.objects.filter(codename__in=permissions)
            group.permissions.set(permission_objects)
        
        # Log the action
        AuditService.log_action(
            action='group_created',
            performed_by=created_by,
            target_group=group,
            details={'permissions': permissions or []}
        )
        
        return group, created
    
    def add_user_to_group(self, user, group, assigned_by=None, notes=""):
        """
        Add user to group with metadata tracking
        """
        # Add user to Django group
        user.groups.add(group)
        
        # Create or update GroupMembership record
        membership, created = GroupMembership.objects.get_or_create(
            user=user,
            group=group,
            defaults={
                'assigned_by': assigned_by,
                'notes': notes,
                'is_active': True
            }
        )
        
        if not created and not membership.is_active:
            # Reactivate existing membership
            membership.is_active = True
            membership.assigned_by = assigned_by
            membership.assigned_at = timezone.now()
            membership.notes = notes
            membership.save()
        
        # Clear user permissions cache
        self._clear_user_cache(user)
        
        # Log the action
        AuditService.log_action(
            action='user_added_to_group',
            performed_by=assigned_by,
            target_user=user,
            target_group=group,
            details={'notes': notes}
        )
        
        return membership, created
    
    def remove_user_from_group(self, user, group, removed_by=None):
        """
        Remove user from group
        """
        # Remove from Django group
        user.groups.remove(group)
        
        # Deactivate GroupMembership record (keep for audit trail)
        try:
            membership = GroupMembership.objects.get(user=user, group=group)
            membership.is_active = False
            membership.save()
        except GroupMembership.DoesNotExist:
            pass
        
        # Clear user permissions cache
        self._clear_user_cache(user)
        
        # Log the action
        AuditService.log_action(
            action='user_removed_from_group',
            performed_by=removed_by,
            target_user=user,
            target_group=group
        )
    
    def get_user_groups(self, user):
        """
        Get all active groups for a user with caching
        """
        cache_key = f'user_groups_{user.id}'
        groups = cache.get(cache_key)
        
        if groups is None:
            groups = list(user.groups.values_list('name', flat=True))
            cache.set(cache_key, groups, 300)  # Cache for 5 minutes
        
        return groups
    
    def get_group_permissions(self, group_name):
        """
        Get all permissions for a group with caching
        """
        cache_key = f'group_permissions_{group_name}'
        permissions = cache.get(cache_key)
        
        if permissions is None:
            try:
                group = Group.objects.get(name=group_name)
                permissions = list(group.permissions.values_list('codename', flat=True))
                cache.set(cache_key, permissions, 600)  # Cache for 10 minutes
            except Group.DoesNotExist:
                permissions = []
        
        return permissions
    
    def _clear_user_cache(self, user):
        """
        Clear cached data for a user when their groups change
        """
        cache.delete(f'user_groups_{user.id}')
        cache.delete(f'user_permissions_{user.id}')

class AuditService:
    """
    Service class for audit logging
    Handles logging of all permission-related actions
    """
    
    @staticmethod
    def log_action(action, performed_by=None, target_user=None, target_group=None, 
                   details=None, ip_address=None):
        """
        Log a permission-related action
        """
        try:
            PermissionAudit.objects.create(
                action=action,
                performed_by=performed_by,
                target_user=target_user,
                target_group=target_group,
                details=details or {},
                ip_address=ip_address
            )
            logger.info(f"Audit log created: {action} by {performed_by}")
        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
    
    @staticmethod
    def log_api_access(user, endpoint, method, status_code, ip_address, 
                      user_agent, response_time):
        """
        Log API access for monitoring
        """
        try:
            APIAccessLog.objects.create(
                user=user,
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                ip_address=ip_address,
                user_agent=user_agent,
                response_time=response_time
            )
        except Exception as e:
            logger.error(f"Failed to create API access log: {e}")

class PermissionService:
    """
    Service class for permission checking and management
    """
    
    def user_has_group_permission(self, user, required_groups):
        """
        Check if user belongs to any of the required groups
        """
        if user.is_superuser:
            return True
        
        user_groups = GroupService().get_user_groups(user)
        return any(group in user_groups for group in required_groups)
    
    def user_has_specific_permission(self, user, required_permissions):
        """
        Check if user has any of the required permissions
        """
        if user.is_superuser:
            return True
        
        return any(user.has_perm(perm) for perm in required_permissions)
    
    def get_user_all_permissions(self, user):
        """
        Get all permissions for a user (from groups and direct assignments)
        """
        cache_key = f'user_permissions_{user.id}'
        permissions = cache.get(cache_key)
        
        if permissions is None:
            permissions = list(user.get_all_permissions())
            cache.set(cache_key, permissions, 300)  # Cache for 5 minutes
        
        return permissions
```

### **Step 1.6: Create Middleware for API Logging**

```python
# apps/staff/middleware.py
from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
from .services import AuditService
import time

class StaffAPILoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all staff API access
    Only logs requests to /api/staff/ endpoints
    """

    def process_request(self, request):
        # Only process staff API requests
        if request.path.startswith('/api/staff/'):
            request._start_time = time.time()
        return None
    
    def process_response(self, request, response):
        # Only log staff API requests
        if (request.path.startswith('/api/staff/') and
            hasattr(request, '_start_time') and
            hasattr(request, 'user')):
            
            # Calculate response time
            response_time = time.time() - request._start_time
            
            # Get client IP
            ip_address = self.get_client_ip(request)
            
            # Log the API access
            AuditService.log_api_access(
                user=request.user if request.user.is_authenticated else None,
                endpoint=request.path,
                method=request.method,
                status_code=response.status_code,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                response_time=response_time
            )
        
        return response
    
    def get_client_ip(self, request):
        """
        Get the client IP address from the request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

### **Step 1.7: Update Settings for Middleware**

```python
# pc_hardware/settings/common.py

# Add middleware to MIDDLEWARE list
MIDDLEWARE = [
    # ... existing middleware
    'apps.staff.middleware.StaffAPILoggingMiddleware',
    # ... rest of middleware
]

# Add logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'staff_api.log',
        },
    },
    'loggers': {
        'apps.staff': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### **Step 1.8: Run Migrations**

```bash
# Create and run migrations
python manage.py makemigrations staff
python manage.py migrate
```

---

## **Phase 2: Core App Integration**

### **Step 2.1: Authentication Flow**

**Important**: The staff app does NOT handle authentication. It leverages the existing core app authentication system.

#### **Authentication Flow:**

1. **Login**: Use existing core app endpoint (e.g., `/api/auth/login/`)
2. **Get Token**: Receive JWT token from core authentication
3. **Access Staff APIs**: Use token with staff endpoints
4. **Authorization**: Staff app validates token + staff permissions

#### **Frontend Integration Example:**

```javascript
// 1. Login using core app
const loginResponse = await fetch('/api/auth/login/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
});

const { access, refresh, user } = await loginResponse.json();

// 2. Check if user is staff
if (!user.is_staff) {
    throw new Error('Staff access required');
}

// 3. Get staff-specific data
const staffResponse = await fetch('/api/staff/auth/user/', {
    headers: { 'Authorization': `Bearer ${access}` }
});

const { user: staffUser } = await staffResponse.json();
// staffUser contains groups, permissions, etc.

// 4. Use staff APIs
const groupsResponse = await fetch('/api/staff/groups/', {
    headers: { 'Authorization': `Bearer ${access}` }
});
```

---

## **Phase 3: Enhanced API Implementation**

### **Step 3.1: Create Enhanced Serializers**

```python
# apps/staff/authorization/serializers.py
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from .models import GroupMembership, PermissionAudit
from apps.customers.models import Customer

User = get_user_model()

class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Django Permission model
    Used to display available permissions
    """
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        read_only_fields = ['id', 'name', 'codename', 'content_type']

class GroupSerializer(serializers.ModelSerializer):
    """
    Serializer for Django Group model
    Handles group creation and updates with permissions
    """
    permissions = PermissionSerializer(many=True, read_only=True)
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of permission IDs to assign to this group"
    )
    member_count = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions', 'permission_ids', 'member_count']

    def get_member_count(self, obj):
        """Get the number of active members in this group"""
        return obj.user_set.filter(is_active=True).count()

    def create(self, validated_data):
        """Create group with permissions"""
        permission_ids = validated_data.pop('permission_ids', [])
        group = Group.objects.create(**validated_data)

        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids)
            group.permissions.set(permissions)

        return group

    def update(self, instance, validated_data):
        """Update group and its permissions"""
        permission_ids = validated_data.pop('permission_ids', None)

        # Update group name if provided
        instance.name = validated_data.get('name', instance.name)
        instance.save()

        # Update permissions if provided
        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids)
            instance.permissions.set(permissions)

        return instance

class UserBasicSerializer(serializers.ModelSerializer):
    """
    Basic user serializer for listing and selection
    """
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'is_active', 'is_staff', 'is_superuser', 'full_name', 'date_joined']
        read_only_fields = ['id', 'date_joined']

    def get_full_name(self, obj):
        """Get user's full name from Customer model if available"""
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except:
            return obj.email

class GroupMembershipSerializer(serializers.ModelSerializer):
    """
    Serializer for GroupMembership model
    Handles user-group assignments with metadata
    """
    user = UserBasicSerializer(read_only=True)
    group = GroupSerializer(read_only=True)
    assigned_by_email = serializers.CharField(source='assigned_by.email', read_only=True)

    class Meta:
        model = GroupMembership
        fields = ['id', 'user', 'group', 'assigned_by_email', 'assigned_at', 'is_active', 'notes']
        read_only_fields = ['id', 'assigned_at']

class UserGroupAssignmentSerializer(serializers.Serializer):
    """
    Serializer for assigning/removing users to/from groups
    """
    user_id = serializers.IntegerField()
    group_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_user_id(self, value):
        """Validate that user exists and is staff"""
        try:
            user = User.objects.get(id=value)
            if not user.is_staff:
                raise serializers.ValidationError("Only staff users can be assigned to admin groups")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")

    def validate_group_id(self, value):
        """Validate that group exists"""
        try:
            Group.objects.get(id=value)
            return value
        except Group.DoesNotExist:
            raise serializers.ValidationError("Group not found")

class UserDetailSerializer(serializers.ModelSerializer):
    """
    Detailed user serializer with groups and permissions
    """
    groups = GroupSerializer(many=True, read_only=True)
    user_permissions = PermissionSerializer(many=True, read_only=True)
    all_permissions = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'email', 'is_active', 'is_staff', 'is_superuser',
            'full_name', 'date_joined', 'last_login',
            'groups', 'user_permissions', 'all_permissions'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_all_permissions(self, obj):
        """Get all permissions (from groups and direct assignments)"""
        return list(obj.get_all_permissions())

    def get_full_name(self, obj):
        """Get user's full name from Customer model if available"""
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except:
            return obj.email

class PermissionAuditSerializer(serializers.ModelSerializer):
    """
    Serializer for PermissionAudit model
    Read-only for audit trail viewing
    """
    performed_by_email = serializers.CharField(source='performed_by.email', read_only=True)
    target_user_email = serializers.CharField(source='target_user.email', read_only=True)
    target_group_name = serializers.CharField(source='target_group.name', read_only=True)

    class Meta:
        model = PermissionAudit
        fields = [
            'id', 'action', 'performed_by_email', 'target_user_email',
            'target_group_name', 'details', 'ip_address', 'timestamp'
        ]
        read_only_fields = '__all__'

class AuthUserSerializer(serializers.ModelSerializer):
    """
    Serializer for authenticated user information
    Returns user details with groups and permissions for frontend
    """
    groups = serializers.StringRelatedField(many=True, read_only=True)
    permissions = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'is_staff', 'is_superuser', 'full_name', 'groups', 'permissions']

    def get_permissions(self, obj):
        """Get all user permissions for frontend permission checking"""
        return list(obj.get_all_permissions())

    def get_full_name(self, obj):
        """Get user's full name"""
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except:
            return obj.email
```

### **Step 3.2: Create Authorization Views**

**Note**: Authentication (login/logout) is handled by the existing core app. The staff app only handles authorization.

```python
# apps/staff/auth_views.py
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser

User = get_user_model()

class CurrentUserView(APIView):
    """
    Get current authenticated staff user information with groups and permissions
    Note: Authentication is handled by core app, this only provides staff-specific data
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """Get current staff user information"""
        serializer = AuthUserSerializer(request.user)
        return Response({
            'success': True,
            'user': serializer.data
        })

class UserPermissionsView(APIView):
    """
    Get current user's permissions and groups breakdown
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """Get detailed permissions and groups for current user"""
        user = request.user

        # Get user groups
        groups = list(user.groups.values_list('name', flat=True))

        # Get all permissions
        permissions = list(user.get_all_permissions())

        # Get group permissions breakdown
        group_permissions = {}
        for group in user.groups.all():
            group_permissions[group.name] = list(
                group.permissions.values_list('codename', flat=True)
            )

        return Response({
            'success': True,
            'user_id': user.id,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'groups': groups,
            'permissions': permissions,
            'group_permissions': group_permissions
        })

class CheckPermissionView(APIView):
    """
    Check if current user has specific permission
    """
    permission_classes = [IsStaffUser]

    def post(self, request):
        """Check if user has specific permission"""
        permission = request.data.get('permission')

        if not permission:
            return Response({
                'success': False,
                'error': 'Permission parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        has_permission = request.user.has_perm(permission)

        return Response({
            'success': True,
            'permission': permission,
            'has_permission': has_permission
        })
```

### **Step 3.3: Create Main API Views**

```python
# apps/staff/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.db import transaction
from .models import GroupMembership, PermissionAudit
from .serializers import (
    GroupSerializer, UserBasicSerializer, UserDetailSerializer,
    GroupMembershipSerializer, UserGroupAssignmentSerializer,
    PermissionAuditSerializer, PermissionSerializer
)
from .permissions import IsStaffUser, IsSuperUser, CanManageGroups, CanManageUsers
from .services import GroupService, AuditService

User = get_user_model()

class GroupViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing groups
    Staff users can create, read, update groups
    Only superusers can delete groups
    """
    queryset = Group.objects.all().order_by('name')
    serializer_class = GroupSerializer
    permission_classes = [CanManageGroups]

    def get_client_ip(self, request):
        """Get client IP for audit logging"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def perform_create(self, serializer):
        """Create group with audit logging"""
        group = serializer.save()

        # Log the action
        AuditService.log_action(
            action='group_created',
            performed_by=self.request.user,
            target_group=group,
            details={'permissions': list(group.permissions.values_list('codename', flat=True))},
            ip_address=self.get_client_ip(self.request)
        )

    def perform_update(self, serializer):
        """Update group with audit logging"""
        old_permissions = list(serializer.instance.permissions.values_list('codename', flat=True))
        group = serializer.save()
        new_permissions = list(group.permissions.values_list('codename', flat=True))

        # Log the action
        AuditService.log_action(
            action='group_updated',
            performed_by=self.request.user,
            target_group=group,
            details={
                'old_permissions': old_permissions,
                'new_permissions': new_permissions
            },
            ip_address=self.get_client_ip(self.request)
        )

    def destroy(self, request, *args, **kwargs):
        """Only superusers can delete groups"""
        if not request.user.is_superuser:
            return Response({
                'success': False,
                'error': 'Only superusers can delete groups'
            }, status=status.HTTP_403_FORBIDDEN)

        group = self.get_object()

        # Check if group has members
        if group.user_set.exists():
            return Response({
                'success': False,
                'error': 'Cannot delete group with existing members'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Log the action before deletion
        AuditService.log_action(
            action='group_deleted',
            performed_by=request.user,
            target_group=group,
            details={'group_name': group.name},
            ip_address=self.get_client_ip(request)
        )

        return super().destroy(request, *args, **kwargs)

    @action(detail=True, methods=['get'])
    def members(self, request, pk=None):
        """Get all members of a group"""
        group = self.get_object()
        memberships = GroupMembership.objects.filter(
            group=group,
            is_active=True
        ).select_related('user', 'assigned_by')

        serializer = GroupMembershipSerializer(memberships, many=True)
        return Response({
            'success': True,
            'members': serializer.data
        })

    @action(detail=True, methods=['post'])
    def add_member(self, request, pk=None):
        """Add a user to the group"""
        group = self.get_object()
        serializer = UserGroupAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            user_id = serializer.validated_data['user_id']
            notes = serializer.validated_data.get('notes', '')

            try:
                user = User.objects.get(id=user_id)

                # Check if user is already in the group
                if user.groups.filter(id=group.id).exists():
                    return Response({
                        'success': False,
                        'error': 'User is already a member of this group'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Add user to group
                group_service = GroupService()
                membership, created = group_service.add_user_to_group(
                    user=user,
                    group=group,
                    assigned_by=request.user,
                    notes=notes
                )

                return Response({
                    'success': True,
                    'message': f'User {user.email} added to group {group.name}',
                    'membership': GroupMembershipSerializer(membership).data
                })

            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'User not found'
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def remove_member(self, request, pk=None):
        """Remove a user from the group"""
        group = self.get_object()
        user_id = request.data.get('user_id')

        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)

            # Check if user is in the group
            if not user.groups.filter(id=group.id).exists():
                return Response({
                    'success': False,
                    'error': 'User is not a member of this group'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Remove user from group
            group_service = GroupService()
            group_service.remove_user_from_group(
                user=user,
                group=group,
                removed_by=request.user
            )

            return Response({
                'success': True,
                'message': f'User {user.email} removed from group {group.name}'
            })

        except User.DoesNotExist:
            return Response({
                'success': False,
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)

class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing users
    Staff can view all staff users
    Superusers can view all users
    """
    permission_classes = [IsStaffUser]

    def get_queryset(self):
        """Filter users based on permissions"""
        if self.request.user.is_superuser:
            # Superusers can see all users
            return User.objects.all().order_by('email')
        else:
            # Staff can only see other staff users
            return User.objects.filter(is_staff=True).order_by('email')

    def get_serializer_class(self):
        """Use detailed serializer for retrieve, basic for list"""
        if self.action == 'retrieve':
            return UserDetailSerializer
        return UserBasicSerializer

    @action(detail=True, methods=['get'])
    def groups(self, request, pk=None):
        """Get all groups for a user"""
        user = self.get_object()
        memberships = GroupMembership.objects.filter(
            user=user,
            is_active=True
        ).select_related('group', 'assigned_by')

        serializer = GroupMembershipSerializer(memberships, many=True)
        return Response({
            'success': True,
            'groups': serializer.data
        })

    @action(detail=True, methods=['post'], permission_classes=[CanManageUsers])
    def toggle_staff(self, request, pk=None):
        """Toggle staff status for a user (superuser only)"""
        user = self.get_object()

        if user.is_superuser:
            return Response({
                'success': False,
                'error': 'Cannot modify superuser staff status'
            }, status=status.HTTP_400_BAD_REQUEST)

        user.is_staff = not user.is_staff
        user.save()

        # Log the action
        AuditService.log_action(
            action='user_staff_toggled',
            performed_by=request.user,
            target_user=user,
            details={'is_staff': user.is_staff},
            ip_address=self.get_client_ip(request)
        )

        return Response({
            'success': True,
            'message': f'User {user.email} staff status set to {user.is_staff}',
            'is_staff': user.is_staff
        })

    def get_client_ip(self, request):
        """Get client IP for audit logging"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing available permissions
    Staff users can view all permissions for group assignment
    """
    queryset = Permission.objects.all().order_by('content_type__app_label', 'codename')
    serializer_class = PermissionSerializer
    permission_classes = [IsStaffUser]

    def list(self, request, *args, **kwargs):
        """List permissions grouped by content type"""
        permissions = self.get_queryset()

        # Group permissions by content type
        grouped_permissions = {}
        for permission in permissions:
            app_label = permission.content_type.app_label
            model = permission.content_type.model
            key = f"{app_label}.{model}"

            if key not in grouped_permissions:
                grouped_permissions[key] = {
                    'app_label': app_label,
                    'model': model,
                    'permissions': []
                }

            grouped_permissions[key]['permissions'].append({
                'id': permission.id,
                'name': permission.name,
                'codename': permission.codename
            })

        return Response({
            'success': True,
            'permissions': grouped_permissions
        })

class AuditViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs
    Staff users can view audit logs for monitoring
    """
    queryset = PermissionAudit.objects.all().order_by('-timestamp')
    serializer_class = PermissionAuditSerializer
    permission_classes = [IsStaffUser]

    def get_queryset(self):
        """Filter audit logs based on query parameters"""
        queryset = super().get_queryset()

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(performed_by_id=user_id)

        # Filter by date range
        from_date = self.request.query_params.get('from_date')
        to_date = self.request.query_params.get('to_date')

        if from_date:
            queryset = queryset.filter(timestamp__gte=from_date)
        if to_date:
            queryset = queryset.filter(timestamp__lte=to_date)

        return queryset

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get audit summary statistics"""
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta

        # Get stats for last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)

        recent_audits = PermissionAudit.objects.filter(timestamp__gte=thirty_days_ago)

        # Group by action
        action_stats = recent_audits.values('action').annotate(
            count=Count('action')
        ).order_by('-count')

        # Group by user
        user_stats = recent_audits.values(
            'performed_by__email'
        ).annotate(
            count=Count('performed_by')
        ).order_by('-count')[:10]

        return Response({
            'success': True,
            'summary': {
                'total_actions_30_days': recent_audits.count(),
                'action_breakdown': list(action_stats),
                'top_users': list(user_stats)
            }
        })
```

### **Step 3.4: Create URL Configuration**

```python
# apps/staff/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import GroupViewSet, UserViewSet, PermissionViewSet, AuditViewSet
from .auth_views import CurrentUserView, UserPermissionsView, CheckPermissionView

# Create router for viewsets
router = DefaultRouter()
router.register(r'groups', GroupViewSet, basename='staff-groups')
router.register(r'users', UserViewSet, basename='staff-users')
router.register(r'permissions', PermissionViewSet, basename='staff-permissions')
router.register(r'audit', AuditViewSet, basename='staff-audit')

urlpatterns = [
    # Authorization endpoints (authentication handled by core app)
    path('auth/user/', CurrentUserView.as_view(), name='current-user'),
    path('auth/permissions/', UserPermissionsView.as_view(), name='user-permissions'),
    path('auth/check-permission/', CheckPermissionView.as_view(), name='check-permission'),

    # Include router URLs
    path('', include(router.urls)),
]

# Update main project URLs
# pc_hardware/urls.py (add this line to existing urlpatterns)
urlpatterns = [
    # ... existing patterns
    path('api/staff/', include('apps.staff.urls')),
    # ... rest of patterns
]
```

### **Step 3.5: Create Management Commands for Initial Setup**

```python
# apps/staff/management/__init__.py
# (empty file)

# apps/staff/management/commands/__init__.py
# (empty file)

# apps/staff/management/commands/setup_staff_groups.py
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.products.models import Product, Category, Brand, ProductType
from apps.order.models import Order
from apps.customers.models import Customer
from apps.core.models import User

class Command(BaseCommand):
    help = 'Setup initial groups and permissions for RBAC system'

    def handle(self, *args, **options):
        self.stdout.write('Setting up RBAC groups and permissions...')

        # Define group permissions mapping
        group_permissions = {
            'Product Management Executive (PME)': [
                'products.add_product', 'products.change_product', 'products.delete_product', 'products.view_product',
                'products.add_category', 'products.change_category', 'products.delete_category', 'products.view_category',
                'products.add_brand', 'products.change_brand', 'products.delete_brand', 'products.view_brand',
                'products.add_producttype', 'products.change_producttype', 'products.delete_producttype', 'products.view_producttype',
            ],
            'Product Management Group Member (PMGM)': [
                'products.add_product', 'products.change_product', 'products.view_product',
                'products.change_category', 'products.view_category',
                'products.view_brand', 'products.view_producttype',
            ],
            'Product Catalog Viewer (PCV)': [
                'products.view_product', 'products.view_category', 'products.view_brand', 'products.view_producttype',
            ],
            'Order Management Executive (OME)': [
                'order.add_order', 'order.change_order', 'order.delete_order', 'order.view_order',
                'customers.view_customer',
            ],
            'Order Management Group Member (OMGM)': [
                'order.change_order', 'order.view_order',
                'customers.view_customer',
            ],
            'Order Fulfillment Specialist (OFS)': [
                'order.change_order', 'order.view_order',
            ],
            'Customer Management Executive (CME)': [
                'customers.add_customer', 'customers.change_customer', 'customers.delete_customer', 'customers.view_customer',
                'order.view_order',
            ],
            'Customer Support Representative (CSR)': [
                'customers.change_customer', 'customers.view_customer',
                'order.view_order',
            ],
            'Customer Data Analyst (CDA)': [
                'customers.view_customer', 'order.view_order',
            ],
            'Content Management Executive (CTME)': [
                'products.change_product', 'products.view_product',  # For review management
            ],
            'Content Moderator (CTM)': [
                'products.view_product',  # For review viewing
            ],
            'Finance Manager (FM)': [
                'order.view_order', 'customers.view_customer',
            ],
            'Business Analyst (BA)': [
                'products.view_product', 'products.view_category', 'products.view_brand',
                'order.view_order', 'customers.view_customer',
            ],
        }

        # Create groups and assign permissions
        for group_name, permission_codenames in group_permissions.items():
            group, created = Group.objects.get_or_create(name=group_name)

            if created:
                self.stdout.write(f'Created group: {group_name}')
            else:
                self.stdout.write(f'Group already exists: {group_name}')

            # Clear existing permissions
            group.permissions.clear()

            # Add permissions
            for codename in permission_codenames:
                try:
                    permission = Permission.objects.get(codename=codename)
                    group.permissions.add(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {codename}')
                    )

            self.stdout.write(f'Assigned {group.permissions.count()} permissions to {group_name}')

        self.stdout.write(
            self.style.SUCCESS('Successfully set up RBAC groups and permissions!')
        )

# apps/staff/management/commands/assign_staff_user.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from apps.staff.services import GroupService

User = get_user_model()

class Command(BaseCommand):
    help = 'Assign a user to a group'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='User email')
        parser.add_argument('group_name', type=str, help='Group name')
        parser.add_argument('--notes', type=str, help='Optional notes', default='')

    def handle(self, *args, **options):
        try:
            user = User.objects.get(email=options['email'])
            group = Group.objects.get(name=options['group_name'])

            if not user.is_staff:
                self.stdout.write(
                    self.style.ERROR(f'User {user.email} is not a staff user')
                )
                return

            # Add user to group
            group_service = GroupService()
            membership, created = group_service.add_user_to_group(
                user=user,
                group=group,
                assigned_by=None,  # System assignment
                notes=options['notes']
            )

            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully assigned {user.email} to {group.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'User {user.email} is already in group {group.name}')
                )

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with email {options["email"]} not found')
            )
        except Group.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Group {options["group_name"]} not found')
            )
```

---

## **Phase 4: Testing & Deployment**

### **Step 4.1: Create Test Suite**

```python
# apps/staff/tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from rest_framework.test import APIClient
from rest_framework import status
from .models import GroupMembership
from .services import GroupService

User = get_user_model()

class StaffAPITestCase(TestCase):
    def setUp(self):
        # Create test users
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test group
        self.test_group = Group.objects.create(name='Test Group')

        self.client = APIClient()

    def test_staff_login_success(self):
        """Test successful staff login"""
        response = self.client.post('/api/staff/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('access_token', response.data)
        self.assertIn('user', response.data)

    def test_staff_login_non_staff(self):
        """Test login rejection for non-staff users"""
        response = self.client.post('/api/staff/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertFalse(response.data['success'])

    def test_group_creation(self):
        """Test group creation by staff user"""
        # Login as staff user
        login_response = self.client.post('/api/staff/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        token = login_response.data['access_token']

        # Set authorization header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Create group
        response = self.client.post('/api/staff/groups/', {
            'name': 'New Test Group'
        })

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Group.objects.filter(name='New Test Group').exists())

    def test_add_user_to_group(self):
        """Test adding user to group"""
        # Login as staff user
        login_response = self.client.post('/api/staff/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        token = login_response.data['access_token']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Add user to group
        response = self.client.post(f'/api/staff/groups/{self.test_group.id}/add_member/', {
            'user_id': self.staff_user.id,
            'notes': 'Test assignment'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertTrue(self.staff_user.groups.filter(id=self.test_group.id).exists())

    def test_group_service(self):
        """Test GroupService functionality"""
        group_service = GroupService()

        # Test adding user to group
        membership, created = group_service.add_user_to_group(
            user=self.staff_user,
            group=self.test_group,
            assigned_by=self.superuser,
            notes='Service test'
        )

        self.assertTrue(created)
        self.assertEqual(membership.user, self.staff_user)
        self.assertEqual(membership.group, self.test_group)
        self.assertTrue(membership.is_active)

        # Test getting user groups
        user_groups = group_service.get_user_groups(self.staff_user)
        self.assertIn('Test Group', user_groups)

        # Test removing user from group
        group_service.remove_user_from_group(
            user=self.staff_user,
            group=self.test_group,
            removed_by=self.superuser
        )

        # Check that membership is deactivated
        membership.refresh_from_db()
        self.assertFalse(membership.is_active)
        self.assertFalse(self.staff_user.groups.filter(id=self.test_group.id).exists())
```

### **Step 4.2: Run Setup Commands**

```bash
# Run migrations
python manage.py makemigrations admin_api
python manage.py migrate

# Setup initial groups and permissions
python manage.py setup_staff_groups

# Assign your admin user to a group (example)
python manage.py assign_staff_user <EMAIL> "Product Management Executive (PME)" --notes "Initial admin setup"

# Run tests
python manage.py test apps.staff
```

This completes the comprehensive implementation guide for the simplified RBAC system. The system provides a clean, maintainable solution using Django's built-in Groups and Permissions with a complete API layer for administration.
