@use '../../../src/scss/variables' as *;
@use '../../../src/scss/mixins' as *;

// Mobile-first base styles
.my_account {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;

  .navbar {
    h3 {
      padding: 0.3rem 0;
      font-size: $font-size-5;
      text-align: center;
      font-weight: bold;
    }

    .navbar__links {
      width: 100%;
      @include flexbox(center, center);

      .navbar__links__links {
        @include flexbox(center, center, row);
        flex-wrap: wrap;
        gap: 0.5rem;
        width: 100%;

        a {
          // Add this within the .navbar__links__links a { } block
          @include flexbox(center, center, column);
          padding: 0.5rem;
          width: auto;
          min-width: 80px;
          color: $primary-blue;
          font-weight: bold;
          // font-size: $font-size-5;
          text-align: center;
          gap: 0.2rem;
          letter-spacing: 0.4px;

          // Active link styling
          &.active {
            color: $primary-dark;
            text-decoration: none;
          }

          i {
            font-size: $font-size-4;
          }

          span {
            font-size: $font-size-1;
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    min-width: 0; // Prevents flex item from overflowing
    width: 100%;
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .my_account {
    // background-color: aqua;
    flex-direction: row;
    gap: 2rem;
    margin-top: 2rem;

    .navbar {
      min-width: 250px;

      h3 {
        padding: 0.5rem 0;
        font-size: 23px;
      }

      .navbar__links {
        .navbar__links__links {
          // background-color: aqua;
          @include flexbox(center, center, column);
          flex-wrap: nowrap;
          gap: 0;
          width: auto;

          a {
            // Add this within the @media (min-width: $tablet) block
            @include flexbox(flex-start, center, row);
            column-gap: 10px;
            padding: 0.9rem 1rem;
            width: 100%;
            font-size: $font-size-4;
            text-align: left;
            gap: 0.5rem;

            i {
              @include flexbox(center, center);
              font-size: 20px;
            }

            span {
              font-size: inherit;
            }

            &:hover {
              text-decoration: none;
            }

            // Active link styling for tablet/desktop
            // &.active {
            //   color: $primary-dark;
            //   // background-color: rgba(0, 0, 0, 0.05);
            //   // border-radius: 4px;
            // }
          }
        }
      }
    }

    .content {
      width: auto;
    }
  }
}
