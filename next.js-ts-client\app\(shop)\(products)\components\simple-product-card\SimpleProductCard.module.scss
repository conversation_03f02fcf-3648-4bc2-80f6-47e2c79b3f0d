@use '@/src/scss/variables' as *;
@use '@/src/scss/mixins' as *;
@use 'sass:map';

.product_card {
  width: 100%;
  max-width: 280px;
  border-radius: $border-radius-2;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #ffffff;
  box-shadow: $box-shadow-1;
  border: 1px solid #f0f0f0;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $box-shadow-blue-1;
    border-color: $sky-light-blue;
  }

  a {
    display: block;
    text-decoration: none;
    color: inherit;

    .product_card__image {
      position: relative;
      width: 100%;
      overflow: hidden;
      background-color: #f8f9fa;

      .image_container {
        position: relative;
        width: 100%;
        aspect-ratio: 1;
        overflow: hidden;

        img {
          transition: transform 0.3s ease;
        }
      }

      .discount {
        position: absolute;
        top: 8px;
        left: 8px;
        background: linear-gradient(135deg, $primary-red, #ff4757);
        color: white;
        padding: 4px 8px;
        border-radius: $border-radius-1;
        font-size: $font-size-1;
        font-weight: map.get($font-weight, 'bold');
        z-index: 2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &:hover {
        img {
          transform: scale(1.05);
        }
      }
    }

    .product_card__info {
      padding: 1rem;

      h3 {
        color: $primary-dark-text-color;
        font-size: $font-size-3;
        font-weight: map.get($font-weight, 'medium');
        line-height: 1.4;
        height: 2.8rem; // Limit to 2 lines
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 0.75rem;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-blue;
        }
      }

      .rating {
        margin: 0.5rem 0;
        @include flexbox(flex-start, center);
        gap: 0.5rem;
        font-size: $font-size-2;

        span:nth-child(2) {
          color: $primary-lighter-text-color;
        }

        span:nth-child(3) {
          color: $primary-lighter-text-color;
        }
      }

      .price {
        @include flexbox(flex-start, center);
        gap: 0.5rem;
        margin-top: 0.75rem;

        .current_price {
          font-size: $font-size-4;
          font-weight: map.get($font-weight, 'bold');
          color: $primary-blue;
        }

        .original_price {
          color: $primary-lighter-text-color;
          text-decoration: line-through;
          font-size: $font-size-2;
        }
      }
    }
  }
}

// Mobile responsive design
@media (max-width: $mobile) {
  .product_card {
    max-width: 100%;

    a {
      .product_card__info {
        padding: 0.75rem;

        h3 {
          font-size: $font-size-2;
          height: 2.4rem;
        }

        .price {
          .current_price {
            font-size: $font-size-3;
          }
        }
      }
    }
  }
}

// Tablet responsive design
@media (min-width: $mobile) and (max-width: $tablet) {
  .product_card {
    max-width: 220px;
  }
}

// Desktop responsive design
@media (min-width: $laptop) {
  .product_card {
    max-width: 250px;

    &:hover {
      transform: translateY(-6px);
    }
  }
}