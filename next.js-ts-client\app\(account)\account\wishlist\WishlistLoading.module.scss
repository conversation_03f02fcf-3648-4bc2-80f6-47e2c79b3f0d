@use '../../../../app/../src/scss/variables' as *;
@use '../../../../app/../src/scss/mixins' as *;

// Mobile-first base styles
.wishlistLoading {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  height: 2rem;
  background-size: 200% 100%;
  border-radius: 4px;
  width: 150px;
}

.wishlistItems {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.wishlistItem {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.itemImage {
  width: 100%;
  height: 150px;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.itemDetails {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.itemTitle {
  height: 1.2rem;
  width: 80%;
  margin-bottom: 0.5rem;
  border-radius: 4px;
}

.itemPrice {
  height: 1rem;
  width: 60px;
  margin-bottom: 0.5rem;
  border-radius: 4px;
}

.itemDate {
  height: 0.8rem;
  width: 100px;
  margin-bottom: 1rem;
  border-radius: 4px;
}

.itemActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.actionButton {
  height: 1.8rem;
  flex: 1;
  border-radius: 4px;
}

.pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .wishlistLoading {
    max-width: 1200px;
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
    height: 2.5rem;
    margin-bottom: 2rem;
    width: 200px;
  }

  .wishlistItems {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .wishlistItem {
    padding: 1.5rem;
  }

  .itemImage {
    height: 200px;
  }

  .itemTitle {
    height: 1.5rem;
  }

  .itemPrice {
    height: 1.2rem;
  }

  .itemDate {
    height: 1rem;
  }

  .itemActions {
    flex-direction: row;
  }

  .actionButton {
    height: 2rem;
  }
}
