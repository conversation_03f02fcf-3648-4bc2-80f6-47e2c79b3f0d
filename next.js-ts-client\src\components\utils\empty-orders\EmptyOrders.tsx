import Link from 'next/link'
import { BsBox2 } from 'react-icons/bs'
import styles from './EmptyOrders.module.scss'

interface Props {
  message?: string
  showIcon?: boolean
  actionText?: string
  actionHref?: string
}

const EmptyOrders = ({
  message,
  showIcon = true,
  actionText = "Start Shopping",
  actionHref = "/"
}: Props) => {
  return (
    <div className={styles.empty_orders} role="region" aria-label="Empty orders">
      {showIcon && (
        <div className={styles.icon_section} aria-hidden="true">
          <BsBox2 />
        </div>
      )}

      <div className={styles.content_section}>
        <h2 className={styles.heading}>
          {message ? message : "No orders yet"}
        </h2>
        <p className={styles.description}>
          Start shopping to see your order history and track your purchases here.
        </p>
      </div>

      <div className={styles.action_section}>
        <Link
          href={actionHref}
          className={styles.action_button}
          aria-label={`${actionText} - Browse products to place your first order`}
        >
          {actionText}
        </Link>
      </div>
    </div>
  )
}

export default EmptyOrders