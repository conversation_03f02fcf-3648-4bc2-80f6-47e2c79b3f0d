@use '../../../../app/../src/scss/variables' as *;
@use '../../../../app/../src/scss/mixins' as *;

// Mobile-first base styles
.ordersLoading {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  height: 2rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  width: 150px;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filterItem {
  height: 2rem;
  width: 100%;
  border-radius: 6px;
}

.orderCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.orderHeader {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.orderInfo {
  height: 1.2rem;
  width: 120px;
  border-radius: 4px;
}

.orderStatus {
  height: 1.5rem;
  width: 60px;
  border-radius: 4px;
}

.orderItems {
  margin: 1rem 0;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.8rem;
  background: #f9f9f9;
  border-radius: 6px;
}

.itemImage {
  width: 100%;
  height: 200px;
  border-radius: 4px;
}

.itemDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.itemName {
  height: 1rem;
  width: 100%;
  border-radius: 4px;
}

.itemPrice {
  height: 0.8rem;
  width: 60px;
  border-radius: 4px;
}

.orderFooter {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.totalAmount {
  height: 1.2rem;
  width: 80px;
  border-radius: 4px;
}

.orderActions {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.5rem;
}

.actionButton {
  height: 1.8rem;
  width: 100%;
  border-radius: 4px;
}

.pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Tablet and larger screens
@media (min-width: $tablet) {
  .ordersLoading {
    max-width: 1200px;
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
    height: 2.5rem;
    margin-bottom: 2rem;
    width: 200px;
  }

  .filters {
    flex-direction: row;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .filterItem {
    height: 2.5rem;
    width: 120px;
  }

  .orderCard {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .orderHeader {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .orderInfo {
    height: 1.5rem;
    width: 150px;
  }

  .orderStatus {
    height: 2rem;
    width: 80px;
  }

  .item {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
  }

  .itemImage {
    width: 80px;
    height: 80px;
  }

  .itemDetails {
    width: auto;
  }

  .itemName {
    height: 1.2rem;
    width: 200px;
  }

  .itemPrice {
    height: 1rem;
    width: 80px;
  }

  .orderFooter {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
  }

  .totalAmount {
    height: 1.5rem;
    width: 100px;
  }

  .orderActions {
    flex-direction: row;
    width: auto;
  }

  .actionButton {
    height: 2rem;
    width: 80px;
  }
}
