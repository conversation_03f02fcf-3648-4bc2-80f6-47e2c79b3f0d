import React from 'react'
import styles from './Underlay.module.scss'

interface Props {
  children: React.ReactNode
  isOpen: boolean
  onClose?: () => void
  bgOpacity?: number
  // zIndex?: number
}

const Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (e.target === e.currentTarget && onClose) {
      onClose()
    }
  }

  return isOpen ? (
    <div
      className={styles.underlay}
      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}
      onClick={handleOverlayClick}
    >
      {children}
    </div>
  ) : null
}

export default Underlay