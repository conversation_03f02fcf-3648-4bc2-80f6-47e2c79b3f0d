---
type: "agent_requested"
description: "Styling guide for SCSS and SCSS modules"
---
- Use SCSS modules, reusable mixins, variable, etc to style each page/components. When coding SCSS/CSS, do not redefine default values.
- When coding SCSS/CSS, do not redefine default/inherited values. Instead, apply only what’s needed for the component's layout or behavior.  
- Use BEM (Block Element Modifier) naming within modules if needed, or camelCase for consistency with JS modules. Keep class names descriptive but concise.
- Use a clear ordering in your styles. (Layout > Box Model > Typography > Interaction > State > Media Queries)
- Use SCSS Nesting Carefully. Avoid excessive nesting (limit to 2-3 levels deep). Don't nest purely for scoping – use module scoping instead.
- Use :global Sparingly. Use the :global selector only when necessary. Keep global overrides isolated and documented.
- Abstract repeating patterns (e.g., buttons, cards, spacing, etc.) into reusable mixins or utility classes.
- After the end each task, ensure the global SCSS files (e.g., variables.scss, mixins.scss) are updated.
