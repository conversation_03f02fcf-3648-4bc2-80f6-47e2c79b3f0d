from django.contrib import admin
from .models import Cart, CartItem


class CartItemInline(admin.StackedInline):
    list_display = ['id', 'cart', 'product', 'product_variant', 'quantity', 'extra_data',
                    'created_at', 'updated_at']
    readonly_fields = ['id', 'cart', 'product', 'product_variant', 'quantity', 'extra_data',
                       'created_at', 'updated_at']
    model = CartItem
    extra = 0


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    inlines = [CartItemInline]
    list_display = ['id', 'customer', 'created_at']
    ordering = ['id', 'customer', 'created_at']
    list_filter = ['created_at']
    search_fields = ['customer__first_name', 'customer__last_name']
    readonly_fields = ['id', 'customer']
