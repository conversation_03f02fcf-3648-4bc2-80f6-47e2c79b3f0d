// Unauthorized access page
// Displayed when user lacks required permissions

import React from 'react'
import { Link } from '@tanstack/react-router'
import { FiShield, FiHome, FiArrowLeft } from 'react-icons/fi'
import { Button } from '../../../components/ui/Button'
import { Card } from '../../../components/ui/Card'
import styles from './UnauthorizedPage.module.scss'

export const UnauthorizedPage: React.FC = () => {
  return (
    <div className={styles.container}>
      <Card className={styles.card} padding="lg">
        <div className={styles.content}>
          <div className={styles.icon}>
            <FiShield />
          </div>
          
          <h1 className={styles.title}>Access Denied</h1>
          
          <p className={styles.message}>
            You don't have permission to access this resource. 
            Please contact your administrator if you believe this is an error.
          </p>
          
          <div className={styles.actions}>
            <Button
              as={Link}
              to="/"
              variant="primary"
              className={styles.homeButton}
            >
              <FiHome />
              Go to Dashboard
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.history.back()}
              className={styles.backButton}
            >
              <FiArrowLeft />
              Go Back
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
