import styles from './Pagination.module.scss'

interface Props {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

const Pagination = ({ currentPage, totalPages, onPageChange }: Props) => {
  const pages = []

  if (totalPages <= 10) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1, 2, 3, 4, 5)

    if (currentPage > 6) {
      pages.push('...')
    }

    for (let i = Math.max(6, currentPage - 1); i <= Math.min(currentPage + 1, totalPages - 5); i++) {
      pages.push(i)
    }

    if (currentPage < totalPages - 5) {
      pages.push('...')
    }

    pages.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages)
  }

  return (
    <div className={styles.pagination}>
      <button
        className={styles.page_button}
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </button>

      {pages.map((page, index) =>
        typeof page === 'number' ? (
          <button
            key={index}
            className={`${styles.page_button} ${currentPage === page ? styles.active : ''}`}
            onClick={() => onPageChange(page)}
          >
            {page}
          </button>
        ) : (
          <span key={index} className={styles.dots}>...</span>
        )
      )}

      <button
        className={styles.page_button}
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        Next
      </button>
    </div>
  )
}

export default Pagination
