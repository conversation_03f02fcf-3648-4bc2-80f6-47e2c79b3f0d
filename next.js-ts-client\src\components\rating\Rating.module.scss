@use '../../../src/scss/variables' as *;
@use '../../../src/scss/mixins' as *;


.rating {
  @include flexbox(flex-start, center);
}

.star_rating {
  margin: 0 .2rem;

  div {
    @include flexbox(flex-start, center);
  }
}

.rating_2 {
  div:nth-child(1) {

    i {
      color: $primary-blue;
    }

    p {
      color: $primary-lighter-text-color;
    }

    // background-color: aquamarine;
    @include flexbox(flex-start, center);
    column-gap: 3px;

  }
}