/**
 * Test script to verify Tanstack Query configuration
 * 
 * This script can be used to verify that the query-client.ts configuration
 * is working correctly and that cache settings are applied properly.
 * 
 * Usage: Import and run this script in a development environment or
 * add it as a utility function in your development tools.
 */

import { queryClient } from './query-client'

/**
 * Test function to verify query configuration
 * This function logs the current configuration for different entity types
 */
export function testQueryConfiguration() {
  console.log('=== Tanstack Query Configuration Test ===\n')

  // Test global defaults
  const globalDefaults = queryClient.getDefaultOptions()
  console.log('🌍 Global Default Options:')
  console.log('  staleTime:', globalDefaults.queries?.staleTime, 'ms')
  console.log('  gcTime:', globalDefaults.queries?.gcTime, 'ms')
  console.log('  refetchOnWindowFocus:', globalDefaults.queries?.refetchOnWindowFocus)
  console.log('  retry:', globalDefaults.queries?.retry)
  console.log('  networkMode:', globalDefaults.queries?.networkMode)
  console.log('')

  // Test entity-specific configurations
  const testEntities = [
    'products',
    'cart', 
    'simple_cart',
    'customer_details',
    'wishlist_items',
    'orders',
    'order_items',
    'categories',
    'brands'
  ]

  console.log('📋 Entity-Specific Configurations:')
  testEntities.forEach(entity => {
    try {
      // Create a mock query to test the configuration
      const queryCache = queryClient.getQueryCache()
      const query = queryCache.build({
        queryKey: [entity],
        queryFn: () => Promise.resolve('test'),
        options: {} // This will pick up the defaults
      })
      
      const options = query.options
      console.log(`  ${entity}:`)
      console.log(`    staleTime: ${options.staleTime} ms (${options.staleTime ? Math.round(options.staleTime / 1000 / 60) : 0} minutes)`)
      console.log(`    gcTime: ${options.gcTime} ms (${options.gcTime ? Math.round(options.gcTime / 1000 / 60) : 0} minutes)`)
      console.log('')
    } catch (error) {
      console.log(`  ${entity}: Configuration not found or error occurred`)
      console.log('')
    }
  })

  console.log('✅ Configuration test completed!')
  console.log('')
  console.log('💡 Expected behavior:')
  console.log('  - Cart data should always be fresh (staleTime: 0)')
  console.log('  - Products should be cached for 10 minutes')
  console.log('  - Customer details should be cached for 5 minutes')
  console.log('  - Wishlist items should be cached for 2 minutes')
  console.log('  - No refetch on window focus for better UX')
  console.log('  - Smart retry logic for failed requests')
}

/**
 * Function to test cache utilities
 */
export function testCacheUtilities() {
  console.log('=== Cache Utilities Test ===\n')

  // Test queryUtils
  console.log('🔧 Testing queryUtils...')
  try {
    // Test that queryUtils exists and has expected methods
    const utils = ['invalidateEntity', 'removeEntity', 'getQueryData', 'setQueryData', 'prefetchQuery', 'clear']
    utils.forEach(method => {
      if (typeof (queryClient as any).queryUtils?.[method] === 'function') {
        console.log(`  ✅ ${method} method exists`)
      } else {
        console.log(`  ❌ ${method} method missing`)
      }
    })
  } catch (error) {
    console.log('  ❌ Error testing queryUtils:', error)
  }

  console.log('')

  // Test cacheUtils
  console.log('🛒 Testing cacheUtils...')
  try {
    // Test that cacheUtils exists and has expected methods
    const utils = [
      'updateProduct', 'updateCart', 'updateSimpleCart', 'addToWishlist', 
      'removeFromWishlist', 'updateCustomer', 'updateCustomerAddresses', 
      'updateOrder', 'updateOrderItems'
    ]
    utils.forEach(method => {
      if (typeof (queryClient as any).cacheUtils?.[method] === 'function') {
        console.log(`  ✅ ${method} method exists`)
      } else {
        console.log(`  ❌ ${method} method missing`)
      }
    })
  } catch (error) {
    console.log('  ❌ Error testing cacheUtils:', error)
  }

  console.log('')
  console.log('✅ Cache utilities test completed!')
}

/**
 * Run all tests
 */
export function runAllTests() {
  testQueryConfiguration()
  console.log(''.repeat(50))
  testCacheUtilities()
}

// Export for use in development
if (typeof window !== 'undefined') {
  // Add to window for easy access in browser console during development
  ;(window as any).testQueryConfig = {
    testQueryConfiguration,
    testCacheUtilities,
    runAllTests
  }
  console.log('🚀 Query configuration test functions added to window.testQueryConfig')
  console.log('   Usage: window.testQueryConfig.runAllTests()')
}
