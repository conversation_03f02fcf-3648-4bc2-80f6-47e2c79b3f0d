# Orders domain URLs for staff operations
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    StaffOrderViewSet, OrderStatusHistoryViewSet,
    OrderAssignmentViewSet, OrderNoteViewSet,
    BulkOrderOperationViewSet, OrderDocumentViewSet
)

router = DefaultRouter()
router.register(r'orders', StaffOrderViewSet, basename='staff-orders')
router.register(r'status-history', OrderStatusHistoryViewSet, basename='staff-order-status-history')
router.register(r'assignments', OrderAssignmentViewSet, basename='staff-order-assignments')
router.register(r'notes', OrderNoteViewSet, basename='staff-order-notes')
router.register(r'bulk-operations', BulkOrderOperationViewSet, basename='bulk-operations')
router.register(r'documents', OrderDocumentViewSet, basename='order-documents')

app_name = 'orders'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
]
