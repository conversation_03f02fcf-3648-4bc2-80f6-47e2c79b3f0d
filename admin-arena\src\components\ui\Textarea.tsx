// Textarea component with consistent styling
// Supports error states, different sizes, and form integration

import React, { forwardRef } from 'react'
import styles from './Textarea.module.scss'

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
  size?: 'sm' | 'md' | 'lg'
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className = '', error, size = 'md', resize = 'vertical', ...props }, ref) => {
    return (
      <div className={styles.container}>
        <textarea
          ref={ref}
          className={`
            ${styles.textarea} 
            ${styles[size]} 
            ${styles[`resize-${resize}`]}
            ${error ? styles.error : ''} 
            ${className}
          `.trim()}
          {...props}
        />
        {error && (
          <span className={styles.errorMessage}>
            {error}
          </span>
        )}
      </div>
    )
  }
)

Textarea.displayName = 'Textarea'
