import Link from 'next/link'
import styles from './not-found.module.scss'

export default function NotFound() {
  return (
    <div className={`container ${styles.not_found_container}`}>
      <div className={styles.not_found_content}>
        <h1>Product Not Found</h1>
        <p>
          Sorry, we couldn&apos;t find the product you&apos;re looking for. It may have been removed,
          renamed, or is temporarily unavailable.
        </p>

        <div className={styles.suggestions}>
          <h3>What you can do:</h3>
          <ul>
            <li>Check the URL for any typos</li>
            <li>Browse our product categories</li>
            <li>Use the search function to find similar products</li>
            <li>Contact our support team if you need assistance</li>
          </ul>
        </div>

        <div className={styles.actions}>
          <Link href="/products" className={styles.browse_button}>
            Browse All Products
          </Link>
          <Link href="/" className={styles.home_button}>
            Go to Homepage
          </Link>
        </div>
      </div>
    </div>
  )
}