import { AttributeValue } from "./types"


export interface Category {
  id: number
  title: string
  slug: string
  level: number
  parent: number | null
  // is_active: boolean
  // lft?: number
  // rght?: number
  // tree_id: number
  children?: Category[]
}

interface Reviews {
  id: number
  title: string
  description: string
  rating: number
  customer: {
    id: number
    first_name: string
    last_name: string
  }
}

export interface ProductImageShape {
  id: number
  image: string
  alternative_text: string
  order: number
}

export interface ProductVariant {
  id: number
  order: number
  price: number
  price_label_attr_title: string
  price_label: string
  sku: string
  stock_qty: number
  is_active: boolean
  product_image: ProductImageShape[]
  attribute_value: AttributeValue[]
}

interface SelectableAttributeValue {
  id: number
  is_active: boolean
  product_variant: number
  attribute_value: {
    id: number
    attribute_value: string
    attribute: {
      id: number
      title: string
    }
    selectable: boolean
  }
}

export interface OptionSelectorValue {
  value_id: number
  value_text: string
}

export interface OptionSelector {
  attribute_id: number
  attribute_title: string
  values: OptionSelectorValue[]
}

export interface ProductShape {
  id: number
  title: string
  slug: string
  description: string
  is_digital: boolean
  is_active: boolean
  product_type: number
  category: Category
  average_rating: number | null
  reviews: Reviews[]
  product_variant: ProductVariant[]
  attribute_value: AttributeValue[]
  selectable_attribute_values: SelectableAttributeValue[]
  option_selectors: OptionSelector[]
}

