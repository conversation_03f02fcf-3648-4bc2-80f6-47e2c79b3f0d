import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import styles from './SimpleProductCard.module.scss'
import { findFirstAvailableImage, calculateDiscount, priceWithTrickyDiscount } from '../utils/product-utils'
import { ProductShape } from '@/src/types/product-types'
import LimitTitleLength from '@/src/components/utils/TextLimit'
import { SimpleRating } from '@/src/components/rating/SimpleRating'

interface Props {
  product: ProductShape
}

const SimpleProductCard: React.FC<Props> = ({ product }) => {
  const { title, slug, product_variant } = product
  const discountPercentage = calculateDiscount(product_variant[0]?.price)

  // Find the first available image across all variants
  const imageUrl = findFirstAvailableImage(product, process.env.NEXT_PUBLIC_CLOUDINARY_URL || 'https://res.cloudinary.com/dev-kani')

  return (
    <div className={styles.product_card}>
      <Link href={`/product/${slug}`}>
        <div className={styles.product_card__image}>
          <div className={styles.image_container}>
            <Image
              src={imageUrl || ''}
              alt={title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{ objectFit: 'cover' }}
              priority={false}
            />
          </div>
          {discountPercentage > 0 && (
            <div className={styles.discount}>-{discountPercentage}%</div>
          )}
        </div>
        <div className={styles.product_card__info}>
          <h3><LimitTitleLength title={title} maxLength={38} /></h3>
          <div className={styles.rating}>
            <SimpleRating product={product} />
            <span>|</span>
            <span>50 sold</span>
          </div>
          <div className={styles.price}>
            <span className={styles.current_price}>${product_variant[0]?.price}</span>
            {discountPercentage > 0 && (
              <span className={styles.original_price}>
                ${priceWithTrickyDiscount(product_variant[0]?.price)}
              </span>
            )}
          </div>
        </div>
      </Link>
    </div>
  )
}

export default SimpleProductCard