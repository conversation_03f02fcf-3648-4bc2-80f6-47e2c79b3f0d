// Product service for API interactions
// Handles all product-related API calls

import APIClient from './api-client'
import {
  Product,
  ProductFilters,
  PaginatedResponse,
  Category,
  Brand,
  ProductType,
  ProductVariant,
  Attribute,
  AttributeValue,
  AttributeValueFormData,
  AttributeValueCreateData,
  AttributeValueBulkCreateData,
  ProductImage,
  ProductImageCreateData,
  ProductCreateWithVariantsData,
  ProductCreateWithVariantsResponse,
  BrandProductType,
  BrandProductTypeBulkAssociate,
  ProductTypeAttribute,
  ProductTypeAttributeAssociation
} from '../types/api-types'


// API endpoints
const ENDPOINTS = {
  PRODUCTS: '/api/staff/products/products/',
  CATEGORIES: '/api/staff/products/categories/',
  BRANDS: '/api/staff/products/brands/',
  TYPES: '/api/staff/products/product-types/',
  VARIANTS: '/api/staff/products/variants/',
  ATTRIBUTES: '/api/staff/products/attributes/',
  ATTRIBUTE_VALUES: '/api/staff/products/attribute-values/',
  IMAGES: '/api/staff/products/images/',
  BRAND_PRODUCT_TYPES: '/api/staff/products/brand-product-types/',
  VARIANT_ATTRIBUTE_VALUES: '/api/staff/products/variant-attribute-values/',
  ASSOCIATIONS: '/api/staff/products/associations/',
} as const

class ProductServiceClass {
  private productsClient = new APIClient<Product>(ENDPOINTS.PRODUCTS)
  private categoriesClient = new APIClient<Category>(ENDPOINTS.CATEGORIES)
  private brandsClient = new APIClient<Brand>(ENDPOINTS.BRANDS)
  private typesClient = new APIClient<ProductType>(ENDPOINTS.TYPES)
  private variantsClient = new APIClient<ProductVariant>(ENDPOINTS.VARIANTS)
  private attributesClient = new APIClient<Attribute>(ENDPOINTS.ATTRIBUTES)
  private attributeValuesClient = new APIClient<AttributeValue>(ENDPOINTS.ATTRIBUTE_VALUES)
  private imagesClient = new APIClient<ProductImage>(ENDPOINTS.IMAGES)
  private brandProductTypesClient = new APIClient<any>(ENDPOINTS.BRAND_PRODUCT_TYPES)

  /**
   * Get paginated products list with filters
   */
  getProducts = async (filters?: ProductFilters): Promise<PaginatedResponse<Product>> => {
    const params = new URLSearchParams()

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }

    return this.productsClient.getAll({
      params,
    })
  }

  /**
   * Get single product by ID
   */
  getProduct = async (id: number): Promise<Product> => {
    const client = new APIClient<Product>(`${ENDPOINTS.PRODUCTS}${id}/`)
    return client.get()
  }

  /**
   * Get full product details including variants, images, and attribute values
   */
  getFullProductDetails = async (id: number): Promise<Product> => {
    const client = new APIClient<Product>(`${ENDPOINTS.PRODUCTS}${id}/`)
    return client.get()
  }

  /**
   * Create new product
   */
  createProduct = async (productData: Partial<Product>): Promise<Product> => {
    return this.productsClient.post(productData)
  }

  /**
   * Update existing product
   */
  updateProduct = async (id: number, productData: Partial<Product>): Promise<Product> => {
    const client = new APIClient<Product>(`${ENDPOINTS.PRODUCTS}${id}/`)
    return client.patch(productData)
  }

  /**
   * Delete product
   */
  deleteProduct = async (id: number): Promise<void> => {
    const client = new APIClient<Product>(`${ENDPOINTS.PRODUCTS}${id}/`)
    return client.delete()
  }

  /**
   * Bulk operations on products
   */
  bulkOperation = async (data: {
    action: 'activate' | 'deactivate' | 'delete' | 'update_category' | 'update_brand'
    productIds: number[]
    payload?: Record<string, any>
  }): Promise<{ success: boolean; message: string }> => {
    const client = new APIClient<{ success: boolean; message: string }>(`${ENDPOINTS.PRODUCTS}bulk/`)
    return client.post(data)
  }

  /**
   * Get product categories (hierarchical)
   */
  getCategories = async (): Promise<Category[]> => {
    const response = await this.categoriesClient.getAll()
    // Check if response is paginated or direct array
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Get single category
   */
  getCategory = async (id: number): Promise<Category> => {
    const client = new APIClient<Category>(`${ENDPOINTS.CATEGORIES}${id}/`)
    return client.get()
  }

  /**
   * Create new category
   */
  createCategory = async (categoryData: Partial<Category>): Promise<Category> => {
    return this.categoriesClient.post(categoryData)
  }

  /**
   * Update category
   */
  updateCategory = async (id: number, categoryData: Partial<Category>): Promise<Category> => {
    const client = new APIClient<Category>(`${ENDPOINTS.CATEGORIES}${id}/`)
    return client.patch(categoryData)
  }

  /**
   * Delete category
   */
  deleteCategory = async (id: number): Promise<void> => {
    const client = new APIClient<Category>(`${ENDPOINTS.CATEGORIES}${id}/`)
    return client.delete()
  }

  /**
   * Get product brands
   */
  getBrands = async (): Promise<Brand[]> => {
    const response = await this.brandsClient.getAll()
    // Check if response is paginated or direct array
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Get single brand
   */
  getBrand = async (id: number): Promise<Brand> => {
    const client = new APIClient<Brand>(`${ENDPOINTS.BRANDS}${id}/`)
    return client.get()
  }

  /**
   * Create new brand
   */
  createBrand = async (brandData: Partial<Brand>): Promise<Brand> => {
    return this.brandsClient.post(brandData)
  }

  /**
   * Update brand
   */
  updateBrand = async (id: number, brandData: Partial<Brand>): Promise<Brand> => {
    const client = new APIClient<Brand>(`${ENDPOINTS.BRANDS}${id}/`)
    return client.patch(brandData)
  }

  /**
   * Delete brand
   */
  deleteBrand = async (id: number): Promise<void> => {
    const client = new APIClient<Brand>(`${ENDPOINTS.BRANDS}${id}/`)
    return client.delete()
  }

  /**
   * Get product types
   */
  getProductTypes = async (): Promise<ProductType[]> => {
    const response = await this.typesClient.getAll()
    // Check if response is paginated or direct array
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Get single product type
   */
  getProductType = async (id: number): Promise<ProductType> => {
    const client = new APIClient<ProductType>(`${ENDPOINTS.TYPES}${id}/`)
    return client.get()
  }

  /**
   * Create new product type
   */
  createProductType = async (typeData: Partial<ProductType>): Promise<ProductType> => {
    return this.typesClient.post(typeData)
  }

  /**
   * Update product type
   */
  updateProductType = async (id: number, typeData: Partial<ProductType>): Promise<ProductType> => {
    const client = new APIClient<ProductType>(`${ENDPOINTS.TYPES}${id}/`)
    return client.patch(typeData)
  }

  /**
   * Delete product type
   */
  deleteProductType = async (id: number): Promise<void> => {
    const client = new APIClient<ProductType>(`${ENDPOINTS.TYPES}${id}/`)
    return client.delete()
  }

  /**
   * Get product variants for a product
   */
  getProductVariants = async (productId: number): Promise<ProductVariant[]> => {
    const response = await this.variantsClient.getAll({
      params: { product: productId },
    })
    return response.results
  }

  /**
   * Get single variant
   */
  getVariant = async (id: number): Promise<ProductVariant> => {
    const client = new APIClient<ProductVariant>(`${ENDPOINTS.VARIANTS}${id}/`)
    return client.get()
  }

  /**
   * Create new variant
   */
  createVariant = async (variantData: Partial<ProductVariant>): Promise<ProductVariant> => {
    return this.variantsClient.post(variantData)
  }

  /**
   * Update variant
   */
  updateVariant = async (id: number, variantData: Partial<ProductVariant>): Promise<ProductVariant> => {
    const client = new APIClient<ProductVariant>(`${ENDPOINTS.VARIANTS}${id}/`)
    return client.patch(variantData)
  }

  /**
   * Delete variant
   */
  deleteVariant = async (id: number): Promise<void> => {
    const client = new APIClient<ProductVariant>(`${ENDPOINTS.VARIANTS}${id}/`)
    return client.delete()
  }

  /**
   * Get attributes
   */
  getAttributes = async (): Promise<Attribute[]> => {
    const response = await this.attributesClient.getAll()
    // Check if response is paginated or direct array
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Get single attribute
   */
  getAttribute = async (id: number): Promise<Attribute> => {
    const client = new APIClient<Attribute>(`${ENDPOINTS.ATTRIBUTES}${id}/`)
    return client.get()
  }

  /**
   * Create new attribute
   */
  createAttribute = async (attributeData: Partial<Attribute>): Promise<Attribute> => {
    return this.attributesClient.post(attributeData)
  }

  /**
   * Update attribute
   */
  updateAttribute = async (id: number, attributeData: Partial<Attribute>): Promise<Attribute> => {
    const client = new APIClient<Attribute>(`${ENDPOINTS.ATTRIBUTES}${id}/`)
    return client.patch(attributeData)
  }

  /**
   * Delete attribute
   */
  deleteAttribute = async (id: number): Promise<void> => {
    const client = new APIClient<Attribute>(`${ENDPOINTS.ATTRIBUTES}${id}/`)
    return client.delete()
  }

  /**
   * Get product types associated with a brand
   */
  getBrandProductTypes = async (brandId: number): Promise<number[]> => {
    const response = await this.brandProductTypesClient.getAll({ params: { brand: brandId } })
    // The API returns a list of objects: { brand: number, product_type: number }
    // We want to return just the product_type IDs
    if (Array.isArray(response)) {
      return response.map((item: any) => item.product_type)
    }
    if (Array.isArray(response.results)) {
      return response.results.map((item: any) => item.product_type)
    }
    return []
  }

  /**
   * Set product types for a brand (replaces all associations)
   */
  setBrandProductTypes = async (brandId: number, productTypeIds: number[]): Promise<void> => {
    await this.brandProductTypesClient.post({ brand: brandId, product_type: productTypeIds })
  }

  // Brand-Product Type Association Methods
  /**
   * Get all brand-product type associations
   */
  getBrandProductTypeAssociations = async (): Promise<BrandProductType[]> => {
    const response = await this.brandProductTypesClient.getAll()
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Bulk associate brands with product types
   */
  bulkAssociateBrandProductTypes = async (data: BrandProductTypeBulkAssociate): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.BRAND_PRODUCT_TYPES}bulk_associate/`)
    return client.post(data)
  }

  /**
   * Delete brand-product type association
   */
  deleteBrandProductTypeAssociation = async (id: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.BRAND_PRODUCT_TYPES}${id}/`)
    return client.delete()
  }

  /**
   * Associate multiple brands with a product type
   */
  associateBrandsWithProductType = async (productTypeId: number, brandIds: number[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.TYPES}${productTypeId}/associate_brands/`)
    return client.post({ brand_ids: brandIds })
  }

  /**
   * Remove multiple brands from a product type
   */
  removeBrandsFromProductType = async (productTypeId: number, brandIds: number[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.TYPES}${productTypeId}/remove_brands/`)
    return client.post({ brand_ids: brandIds })
  }

  /**
   * Get brands associated with a product type
   */
  getProductTypeBrands = async (productTypeId: number): Promise<BrandProductType[]> => {
    const client = new APIClient<BrandProductType[]>(`${ENDPOINTS.TYPES}${productTypeId}/brands/`)
    return client.get()
  }

  // Product Type-Attribute Association Methods
  /**
   * Get attributes associated with a product type
   */
  getProductTypeAttributes = async (productTypeId: number): Promise<ProductTypeAttribute[]> => {
    const client = new APIClient<ProductTypeAttribute[]>(`${ENDPOINTS.TYPES}${productTypeId}/attributes/`)
    const response = await client.get()
    return Array.isArray(response) ? response : response.results || []
  }

  /**
   * Associate attributes with a product type
   */
  associateProductTypeAttributes = async (
    productTypeId: number,
    attributes: ProductTypeAttributeAssociation[]
  ): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.TYPES}${productTypeId}/associate_attributes/`)
    return client.post({ attributes })
  }

  /**
   * Update product type attribute association
   */
  updateProductTypeAttribute = async (
    productTypeId: number,
    attributeId: number,
    data: Partial<ProductTypeAttributeAssociation>
  ): Promise<ProductTypeAttribute> => {
    const client = new APIClient<ProductTypeAttribute>(
      `${ENDPOINTS.TYPES}${productTypeId}/attributes/${attributeId}/`
    )
    return client.patch(data)
  }

  /**
   * Remove attribute from product type
   */
  removeProductTypeAttribute = async (productTypeId: number, attributeId: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.TYPES}${productTypeId}/attributes/${attributeId}/`)
    return client.delete()
  }

  // Association Management Methods
  /**
   * Update product type attribute association
   */
  updateProductTypeAttributeAssociation = async (
    associationId: number,
    data: Partial<ProductTypeAttributeAssociation>
  ): Promise<ProductTypeAttribute> => {
    const client = new APIClient<ProductTypeAttribute>(`${ENDPOINTS.ASSOCIATIONS}${associationId}/`)
    return client.patch(data)
  }

  /**
   * Delete product type attribute association
   */
  deleteProductTypeAttributeAssociation = async (associationId: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.ASSOCIATIONS}${associationId}/`)
    return client.delete()
  }

  // Attribute Value Methods
  /**
   * Get attribute values for a specific attribute
   */
  getAttributeValues = async (attributeId?: number): Promise<AttributeValue[]> => {
    const params = attributeId ? { attribute: attributeId } : {}
    const response = await this.attributeValuesClient.getAll({ params })
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Get single attribute value
   */
  getAttributeValue = async (id: number): Promise<AttributeValue> => {
    const client = new APIClient<AttributeValue>(`${ENDPOINTS.ATTRIBUTE_VALUES}${id}/`)
    return client.get()
  }

  /**
   * Create new attribute value
   */
  createAttributeValue = async (data: AttributeValueCreateData): Promise<AttributeValue> => {
    return this.attributeValuesClient.post(data)
  }

  /**
   * Update attribute value
   */
  updateAttributeValue = async (id: number, data: Partial<AttributeValueFormData>): Promise<AttributeValue> => {
    const client = new APIClient<AttributeValue>(`${ENDPOINTS.ATTRIBUTE_VALUES}${id}/`)
    return client.patch(data)
  }

  /**
   * Delete attribute value
   */
  deleteAttributeValue = async (id: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.ATTRIBUTE_VALUES}${id}/`)
    return client.delete()
  }

  /**
   * Bulk create attribute values
   */
  bulkCreateAttributeValues = async (data: AttributeValueBulkCreateData): Promise<AttributeValue[]> => {
    const client = new APIClient<AttributeValue[]>(`${ENDPOINTS.ATTRIBUTE_VALUES}bulk_create/`)
    const response = await client.post(data)
    return Array.isArray(response) ? response : response.values || []
  }

  // Variant Attribute Value Methods
  /**
   * Get variant attribute values for a specific variant
   */
  getVariantAttributeValues = async (variantId: number): Promise<any[]> => {
    const response = await new APIClient<any>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}`).getAll({
      params: { product_variant: variantId },
    })
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Create variant attribute value association
   */
  createVariantAttributeValue = async (data: { product_variant: number; attribute_value: number; is_active?: boolean }): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}`)
    return client.post(data)
  }

  /**
   * Update variant attribute value association
   */
  updateVariantAttributeValue = async (id: number, data: { is_active?: boolean }): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}${id}/`)
    return client.patch(data)
  }

  /**
   * Delete variant attribute value association
   */
  deleteVariantAttributeValue = async (id: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}${id}/`)
    return client.delete()
  }

  /**
   * Bulk associate attribute values with a variant
   */
  bulkAssociateVariantAttributeValues = async (data: { product_variant_id: number; attribute_value_ids: number[] }): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}bulk_associate/`)
    return client.post(data)
  }

  /**
   * Bulk update status of variant attribute value associations
   */
  bulkUpdateVariantAttributeValueStatus = async (data: { association_ids: number[]; is_active: boolean }): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}bulk_update_status/`)
    return client.patch(data)
  }

  // Product Creation with Variants Methods
  /**
   * Create product with variants in one request
   */
  createProductWithVariants = async (data: ProductCreateWithVariantsData): Promise<ProductCreateWithVariantsResponse> => {
    const client = new APIClient<ProductCreateWithVariantsResponse>(`${ENDPOINTS.PRODUCTS}create_with_variants/`)
    return client.post(data)
  }

  // Product Image Methods
  /**
   * Get images for a product variant
   */
  getProductImages = async (variantId: number): Promise<ProductImage[]> => {
    const params = { product_variant: variantId }
    const response = await this.imagesClient.getAll({ params })
    return Array.isArray(response) ? response : response.results
  }

  /**
   * Upload image to product variant
   */
  uploadProductImage = async (data: ProductImageCreateData): Promise<ProductImage> => {
    const formData = new FormData()
    formData.append('image', data.image)
    formData.append('alternative_text', data.alternative_text)
    formData.append('product_variant', data.product_variant.toString())

    const client = new APIClient<ProductImage>(ENDPOINTS.IMAGES)
    return client.post(formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * Update product image
   */
  updateProductImage = async (id: number, data: Partial<ProductImageCreateData>): Promise<ProductImage> => {
    const client = new APIClient<ProductImage>(`${ENDPOINTS.IMAGES}${id}/`)

    if (data.image) {
      const formData = new FormData()
      if (data.image) formData.append('image', data.image)
      if (data.alternative_text) formData.append('alternative_text', data.alternative_text)
      if (data.product_variant) formData.append('product_variant', data.product_variant.toString())

      return client.patch(formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      return client.patch(data)
    }
  }

  /**
   * Delete product image
   */
  deleteProductImage = async (id: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.IMAGES}${id}/`)
    return client.delete()
  }

  /**
   * Individual image reorder
   */
  reorderProductImage = async (imageId: number, newOrder: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.IMAGES}${imageId}/reorder/`)
    return client.patch({ new_order: newOrder })
  }

  /**
   * Drag-and-drop reorder product images
   */
  reorderProductImagesDragDrop = async (productVariantId: number, orderedIds: number[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.IMAGES}reorder_drag_drop/`)
    return client.patch({ product_variant_id: productVariantId, ordered_ids: orderedIds })
  }

  /**
   * Get attribute values by product type
   */
  getAttributeValuesByProductType = async (productTypeId: number): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.TYPES}${productTypeId}/attribute_values/`)
    return client.get()
  }

  /**
   * Get variant attribute values for a specific variant
   */
  getVariantAttributeValuesByVariant = async (variantId: number): Promise<any> => {
    const client = new APIClient<any>(`${ENDPOINTS.VARIANTS}${variantId}/attribute_values/`)
    return client.get()
  }

  /**
   * Individual variant reorder
   */
  reorderProductVariant = async (variantId: number, newOrder: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANTS}${variantId}/reorder/`)
    return client.patch({ new_order: newOrder })
  }

  /**
   * Drag-and-drop reorder product variants
   */
  reorderProductVariantsDragDrop = async (productId: number, orderedIds: number[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANTS}reorder_drag_drop/`)
    return client.patch({ product_id: productId, ordered_ids: orderedIds })
  }

  /**
   * Bulk update product variants order
   */
  bulkUpdateProductVariantsOrder = async (orderUpdates: { id: number; order: number }[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANTS}bulk_update_order/`)
    return client.patch({ order_updates: orderUpdates })
  }

  /**
   * Individual variant attribute value reorder
   */
  reorderVariantAttributeValue = async (associationId: number, newOrder: number): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}${associationId}/reorder/`)
    return client.patch({ new_order: newOrder })
  }

  /**
   * Drag-and-drop reorder variant attribute values
   */
  reorderVariantAttributeValuesDragDrop = async (productVariantId: number, orderedIds: number[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}reorder_drag_drop/`)
    return client.patch({ product_variant_id: productVariantId, ordered_ids: orderedIds })
  }

  /**
   * Bulk update variant attribute values order
   */
  bulkUpdateVariantAttributeValuesOrder = async (orderUpdates: { id: number; order: number }[]): Promise<void> => {
    const client = new APIClient<void>(`${ENDPOINTS.VARIANT_ATTRIBUTE_VALUES}bulk_update_order/`)
    return client.patch({ order_updates: orderUpdates })
  }
}

export const ProductService = new ProductServiceClass()
