import { useF<PERSON>, SubmitHand<PERSON> } from 'react-hook-form'
import { AxiosError } from 'axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import AuthLayout from '../AuthLayout'
import { restPasswordVerifySchema } from '@/src/schemas/schemas'
import Alert from '@/src/components/utils/alert/Alert'
import { ErrorResponse } from '@/src/types/types'
import { useTogglePasswordVisibility } from '@/src/hooks/other-hooks'
import { useResetPasswordConfirm } from '@/src/hooks/auth-hooks'


export type VerifyDetailShape = z.infer<typeof restPasswordVerifySchema>

interface Props {
  emailOrPhone: string
  onVerificationSuccess: () => void
}

const VerifyResetCode = ({ emailOrPhone, onVerificationSuccess }: Props) => {
  const { isVisible: isPasswordVisible, toggleVisibility: togglePasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isRePasswordVisible, toggleVisibility: toggleRePasswordVisibility } = useTogglePasswordVisibility()

  const { mutation } = useResetPasswordConfirm()
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyDetailShape>({
    resolver: zodResolver(restPasswordVerifySchema)
  })

  const onSubmit: SubmitHandler<VerifyDetailShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: onVerificationSuccess,
    })
  }

  return (
    <AuthLayout
      title='Verify Reset Code'
      error={mutation.error as AxiosError<ErrorResponse> | null}
    >
      {!mutation.error && (
        <Alert
          variant="success"
          message={`Verification code sent to ${emailOrPhone}. 
          Please check your ${emailOrPhone?.startsWith('+') ? 'phone' : 'email'} and enter the code below.`}
          highlightWords={[`${emailOrPhone}`, "phone", "email"]}
          textAlign='center'
        />
      )}
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <input type="hidden" {...register("email_or_phone")} value={emailOrPhone} />

        <div className='form_group'>
          <label className='form_label'>Enter Verification Code:</label>
          <input className='form_input' type="number" {...register("code", { valueAsNumber: true })} />
          {errors.code && <p className='form_error'>{errors.code.message}</p>}
        </div>

        <div className='form_group'>
          <label className='form_label'>New Password:</label>
          <section className='password__container'>
            <input
              className='form_input'
              type={isPasswordVisible ? "text" : "password"} {...register("new_password")} />
            <span onClick={togglePasswordVisibility}>
              <i>{isPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
            </span>
          </section>
          {errors.new_password && <p className='form_error'>{errors.new_password.message}</p>}
        </div>

        <div className='form_group'>
          <label className='form_label'>Confirm Password:</label>
          <section className='password__container'>
            <input
              className='form_input'
              type={isRePasswordVisible ? "text" : "password"}
              {...register("confirm_password")}
            />
            <span onClick={toggleRePasswordVisibility}>
              <i>{isRePasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
            </span>
          </section>
          {errors.confirm_password && <p className='form_error'>{errors.confirm_password.message}</p>}
        </div>

        <div>
          <button type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_svg} alt="Loading..." className='loading_svg' />
              'Submitting...'
            ) : 'Submit'}
          </button>
        </div>
      </form>
    </AuthLayout>
  )
}

export default VerifyResetCode
