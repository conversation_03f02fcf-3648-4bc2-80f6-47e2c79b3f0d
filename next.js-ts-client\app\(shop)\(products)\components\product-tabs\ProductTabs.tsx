'use client'

import { useState } from 'react'
import { ProductShape, ProductVariant } from '../../../../../src/types/product-types'
import Specifications from '../../../../../src/components/utils/specs/Specifications'
import Reviews from '../../../../../src/components/reviews/Reviews'
import styles from './ProductTabs.module.scss'

interface ProductTabsProps {
  product: ProductShape
  selectedVariant: ProductVariant | null
}

export default function ProductTabs({ product, selectedVariant }: ProductTabsProps) {
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description')

  return (
    <section className={styles.product_tabs}>
      <div className={styles.tabs_header}>
        <button
          className={activeTab === 'description' ? styles.active : ''}
          onClick={() => setActiveTab('description')}
        >
          Description
        </button>
        <button
          className={activeTab === 'specifications' ? styles.active : ''}
          onClick={() => setActiveTab('specifications')}
        >
          Specifications
        </button>
        <button
          className={activeTab === 'reviews' ? styles.active : ''}
          onClick={() => setActiveTab('reviews')}
        >
          Reviews {product?.reviews.length > 0 && `(${product.reviews.length})`}
        </button>
      </div>

      <div className={styles.tabs_content}>
        {activeTab === 'description' && (
          <div className={styles.product_description}>
            <h3>About this item:</h3>
            {product?.description && (
              <p>{product.description}</p>
            )}
          </div>
        )}

        {activeTab === 'specifications' && (
          <div className={styles.specifications}>
            <h3>Product information:</h3>
            <Specifications
              selectedVariant={selectedVariant}
            />
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className={styles.reviews}>
            <h3>Customer reviews:</h3>
            {product?.reviews.length === 0 ? (
              <p>No reviews yet</p>
            ) : (
              <Reviews reviews={product?.reviews} slug={product.slug} />
            )}
          </div>
        )}
      </div>
    </section>
  )
}