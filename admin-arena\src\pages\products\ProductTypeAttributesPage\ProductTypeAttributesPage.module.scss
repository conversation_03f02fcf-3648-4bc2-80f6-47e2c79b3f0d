@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: $spacing-6;
}

.titleSection {
  .title {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: 0 0 $spacing-2 0;
  }

  .subtitle {
    font-size: $font-size-base;
    color: $gray-600;
    margin: 0;
  }
}

.selectionCard {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-6;

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }
}

.selectionForm {
  .formGroup {
    display: flex;
    flex-direction: column;
    gap: $spacing-3;
  }

  .label {
    font-size: $font-size-sm;
    color: $gray-600;
    margin: 0;
  }
}

.associationsContainer {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.existingCard,
.newCard {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;

  h4 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }
}

.newCardHeader {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;
}

.attributesList,
.newAttributesList {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.attributeRow,
.newAttributeRow {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: $spacing-4;
  align-items: center;
  padding: $spacing-4;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  background: $gray-50;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-3;
  }
}

.newAttributeRow {
  grid-template-columns: 2fr 1fr auto;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.attributeInfo {
  @include flex-center;
  justify-content: flex-start;
  gap: $spacing-2;
}

.attributeSelect {
  min-width: 200px;

  @include mobile-only {
    min-width: 100%;
  }
}

.attributeSettings {
  display: flex;
  gap: $spacing-4;
  align-items: center;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-2;
  }
}

.settingItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-1;
  min-width: 80px;

  label {
    font-size: $font-size-xs;
    color: $gray-600;
    text-align: center;
    white-space: nowrap;
  }

  @include mobile-only {
    flex-direction: row;
    align-items: center;
    min-width: auto;
    gap: $spacing-2;
  }
}

.attributeActions {
  @include flex-center;
  gap: $spacing-2;
}

.noAssociations,
.noNewAttributes {
  text-align: center;
  color: $gray-500;
  font-style: italic;
  padding: $spacing-8;
  background: $gray-50;
  border-radius: $border-radius-md;
  border: 2px dashed $gray-300;
}

.saveActions {
  @include flex-center;
  justify-content: flex-end;
  margin-top: $spacing-4;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

// React Select custom styles
:global(.react-select__control) {
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  min-height: 42px;
}

:global(.react-select__control:hover) {
  border-color: $gray-400;
}

:global(.react-select__control--is-focused) {
  border-color: $primary-500;
  box-shadow: 0 0 0 1px $primary-500;
}

:global(.react-select__value-container) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__placeholder) {
  color: $gray-500;
}

:global(.react-select__single-value) {
  color: $gray-900;
}

:global(.react-select__option) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-50;
  color: $primary-900;
}

:global(.react-select__option--is-selected) {
  background-color: $primary-500;
  color: $white;
}

:global(.react-select__menu) {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  box-shadow: $shadow-lg;
  z-index: 9999;
}

:global(.react-select__menu-list) {
  padding: $spacing-1;
}

// Loading and disabled states
.attributeRow:has(.switch:disabled) {
  opacity: 0.6;
}

.newAttributeRow:has(.react-select__control--is-disabled) {
  opacity: 0.6;
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }

  .attributeRow,
  .newAttributeRow {
    padding: $spacing-3;
  }

  .attributeSettings {
    width: 100%;
    justify-content: space-between;
  }

  .settingItem {
    flex: 1;
  }
}