{% load i18n admin_urls static admin_modify %}
<div class="js-inline-admin-formset inline-group" id="{{ inline_admin_formset.formset.prefix }}-group"
     data-inline-type="tabular"
     data-inline-formset="{{ inline_admin_formset.inline_formset_data }}">
  <div class="tabular inline-related {% if forloop.last %}last-related{% endif %}">
    {{ inline_admin_formset.formset.management_form }}
    <fieldset class="module {{ inline_admin_formset.classes }}">
      <h2>{{ inline_admin_formset.opts.verbose_name_plural|capfirst }}</h2>
      {{ inline_admin_formset.formset.non_form_errors }}
      <table>
        <thead>
        <tr>
          <th class="original"></th>
          {% for field in inline_admin_formset.fields %}
            {% if not field.widget.is_hidden %}
              <th{% if field.required %} class="required"{% endif %}>
                {{ field.label|capfirst }}
                {% if field.help_text %}
                  <img src="{% static "admin/img/icon-unknown.svg" %}"
                       class="help help-tooltip"
                       width="10"
                       height="10"
                       alt="{{ field.help_text|striptags }}"
                       title="{{ field.help_text|striptags }}"
                       role="tooltip"
                       aria-label="{{ field.help_text|striptags }}">
                {% endif %}
              </th>
            {% endif %}
          {% endfor %}
          <th>{% trans "Actions" %}</th>
        </tr>
        </thead>

        <tbody>
        {% for inline_admin_form in inline_admin_formset %}
          {% if inline_admin_form.form.non_field_errors %}
            <tr class="row-form-errors">
              <td colspan="{{ inline_admin_form|cell_count }}">{{ inline_admin_form.form.non_field_errors }}</td>
            </tr>
          {% endif %}

          <tr class="form-row {% if inline_admin_form.original or inline_admin_form.show_url %}has_original{% endif %}{% if forloop.last and inline_admin_formset.has_add_permission %} empty-form{% endif %}"
              id="{{ inline_admin_formset.formset.prefix }}-{% if not forloop.last %}{{ forloop.counter0 }}{% else %}empty{% endif %}">
            <td class="original">
              {% if inline_admin_form.original or inline_admin_form.show_url %}
                <p>
                  {% if inline_admin_form.original %}
                    {{ inline_admin_form.original }}
                    {% if inline_admin_form.model_admin.show_change_link and inline_admin_form.model_admin.has_registered_model and inline_admin_form.original.pk %}
                      <a href="{% url inline_admin_form.model_admin.opts|admin_urlname:'change' inline_admin_form.original.pk|admin_urlquote %}"
                         class="{% if inline_admin_formset.has_change_permission %}inlinechangelink{% else %}inlineviewlink{% endif %}">
                        {% if inline_admin_formset.has_change_permission %}{% trans "Change" %}{% else %}{% trans "View" %}{% endif %}
                      </a>
                    {% endif %}
                  {% endif %}
                  {% if inline_admin_form.show_url %}
                    <a href="{{ inline_admin_form.absolute_url }}">{% trans "View on site" %}</a>
                  {% endif %}
                </p>
              {% endif %}

              {% if inline_admin_form.needs_explicit_pk_field %}{{ inline_admin_form.pk_field.field }}{% endif %}
              {% if inline_admin_form.fk_field %}{{ inline_admin_form.fk_field.field }}{% endif %}

              {% spaceless %}
              {% for fieldset in inline_admin_form %}
                {% for line in fieldset %}
                  {% for field in line %}
                    {% if field.field.is_hidden %} {{ field.field }} {% endif %}
                  {% endfor %}
                {% endfor %}
              {% endfor %}
              {% endspaceless %}
            </td>

            {% for fieldset in inline_admin_form %}
              {% for line in fieldset %}
                {% for field in line %}
                  {% if not field.field.is_hidden %}
                    <td{% if field.field.name %} class="field-{{ field.field.name }}"{% endif %}>
                      {% if field.is_readonly %}
                        <p>{{ field.contents }}</p>
                      {% else %}
                        {{ field.field.errors.as_ul }}
                        {{ field.field }}
                      {% endif %}
                    </td>
                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endfor %}

            <td class="custom-actions">
              {% if inline_admin_form.original and inline_admin_form.original.id %}
                <a href="#"
                   class="delete-inline-image"
                   data-image-id="{{ inline_admin_form.original.id }}"
                   style="color: white; background-color: #ba2121; padding: 5px 10px; border-radius: 4px; text-decoration: none; display: inline-block; font-weight: bold;">
                  Delete Image
                </a>
              {% endif %}
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </fieldset>
  </div>
</div>

<style>
  .delete-checkbox-column {
    display: none !important;
  }

  .delete-inline-image {
    transition: background-color 0.3s;
  }

  .delete-inline-image:hover {
    background-color: #a41515 !important;
  }
</style>

<script type="text/javascript">
(function($) {
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  $(document).ready(function() {
    console.log("Custom inline template loaded");

    // More accurate DELETE checkbox hiding
    $('input[name$="-DELETE"]').closest('td').addClass('delete-checkbox-column').hide();

    $('.field-product_variant').hide();

    $('.delete-inline-image').click(function(e) {
      e.preventDefault();
      var imageId = $(this).data('image-id');
      var $row = $(this).closest('tr');

      if (!imageId) {
        alert("Cannot delete image: missing ID.");
        return;
      }

      console.log("Delete button clicked for image ID:", imageId);

      if (confirm('Are you sure you want to delete this image?')) {
        const csrftoken = getCookie('csrftoken');

        $.ajax({
          url: '/api/admin/delete-product-image/' + imageId + '/',
          type: 'POST',
          data: {},
          headers: {
            'X-CSRFToken': csrftoken
          },
          success: function(response) {
            console.log("Delete successful:", response);
            $row.fadeOut(function() {
              $(this).remove();
            });
          },
          error: function(xhr, textStatus, errorThrown) {
            console.error("Delete failed:", xhr);
            console.error("Status:", textStatus);
            console.error("Error:", errorThrown);
            console.error("Response:", xhr.responseText);

            var errorMessage = 'Error deleting image';
            if (xhr.responseJSON && xhr.responseJSON.error) {
              errorMessage += ': ' + xhr.responseJSON.error;
            } else if (xhr.status === 404) {
              errorMessage += ': Image not found';
            } else if (xhr.status === 403) {
              errorMessage += ': Permission denied';
            } else {
              errorMessage += ': ' + xhr.statusText;
            }

            alert(errorMessage);
          }
        });
      }
    });
  });
})(django.jQuery);
</script>
