/* Sortable Tabular Inline JavaScript - Now using SortableJS for robust drag & drop */

(function ($) {
    'use strict'

    // Helper to update order fields after sorting
    function updateOrderFields($tbody) {
        $tbody.find('tr:not(.empty-form)').each(function (index) {
            var $row = $(this)
            var $orderField = $row.find('input[name$="-order"]')
            if ($orderField.length > 0) {
                $orderField.val(index + 1)
                // Also update any visible order display
                var $orderDisplay = $row.find('.field-order')
                if ($orderDisplay.length > 0) {
                    $orderDisplay.find('input').val(index + 1)
                }
            }
        })
    }

    $(document).ready(function () {
        // Wait for Django admin to fully load
        setTimeout(function () {
            $('.inline-group .tabular').each(function () {
                var $tabular = $(this)
                var $tbody = $tabular.find('tbody')
                if ($tbody.find('.drag-handle').length > 0 && typeof Sortable !== 'undefined') {
                    $tabular.addClass('sortable-tabular-inline')
                    // Initialize SortableJS
                    Sortable.create($tbody[0], {
                        handle: '.drag-handle',
                        animation: 150,
                        ghostClass: 'drag-over',
                        onEnd: function (evt) {
                            updateOrderFields($tbody)
                            // Optional: add visual feedback
                            var $row = $($tbody[0].children[evt.newIndex])
                            $row.addClass('sortable-success')
                            setTimeout(function () {
                                $row.removeClass('sortable-success')
                            }, 1000)
                        },
                        filter: '.empty-form',
                        draggable: 'tr:not(.empty-form)'
                    })
                    // Initial order update
                    updateOrderFields($tbody)
                }
            })
        }, 500)
    })

    // Handle dynamic form addition (when "Add another" is clicked)
    $(document).on('formset:added', function (event, $row) {
        var $inlineGroup = $row.closest('.inline-group')
        var $tbody = $inlineGroup.find('tbody')
        if ($inlineGroup.hasClass('sortable-tabular-inline')) {
            updateOrderFields($tbody)
        }
    })

    // Handle form deletion
    $(document).on('formset:removed', function (event, $row) {
        var $inlineGroup = $row.closest('.inline-group')
        var $tbody = $inlineGroup.find('tbody')
        if ($inlineGroup.hasClass('sortable-tabular-inline')) {
            setTimeout(function () {
                updateOrderFields($tbody)
            }, 100)
        }
    })

    // Add visual feedback for drag handle hover
    $(document).on('mouseenter', '.drag-handle', function () {
        $(this).closest('tr').addClass('drag-hover')
    })
    $(document).on('mouseleave', '.drag-handle', function () {
        $(this).closest('tr').removeClass('drag-hover')
    })

})(django.jQuery || jQuery)

// CSS for drag hover effect
if (!document.getElementById('sortable-hover-styles')) {
    var style = document.createElement('style')
    style.id = 'sortable-hover-styles'
    style.textContent = `
        .drag-hover {
            background-color: #f8f9fa !important;
        }
        .drag-hover .drag-handle {
            background-color: #e9ecef !important;
            border-radius: 3px;
        }
    `
    document.head.appendChild(style)
}
