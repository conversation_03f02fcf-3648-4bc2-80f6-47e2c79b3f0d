import { ProductShape } from "../../types/product-types"
import styles from './Rating.module.scss'
import StarRating from "../utils/StarRating"


interface Props {
  product: ProductShape
  color?: string
}

export const Rating = ({ product, color = "#000" }: Props) => {

  return (
    <section className={styles.rating}>
      <p>{product?.average_rating ? `(${product.average_rating})` : "(No reviews yet)"}</p>
      {product?.average_rating !== null && product?.average_rating !== undefined && product?.average_rating > 0 && (
        <>
          <div className={styles.star_rating}>
            <StarRating rating={product.average_rating} color={color} />
          </div>
          <p>{product?.reviews?.length ? `${product.reviews.length} reviews` : "No reviews"}</p>
        </>
      )}
    </section>
  )
}
