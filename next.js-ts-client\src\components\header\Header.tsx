'use client'

import { useLogout } from '@/src/hooks/auth-hooks'
import { useSimpleCart } from '@/src/hooks/cart-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { useWishlist } from '@/src/hooks/wishlist-hooks'
import authStore from '@/src/stores/auth-store'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { BsBox2 } from 'react-icons/bs'
import { FaCaretDown, FaCartPlus, FaHeart } from 'react-icons/fa'
import { MdOutlineAccountCircle } from 'react-icons/md'
import { RiLogoutCircleRLine } from 'react-icons/ri'
import { CartItemShape } from '../../types/store-types'
import Logo from '../utils/logo/Logo'
import Navbar from './navbar/Navbar'
import Search from './search-bar/Search'
import styles from './Header.module.scss'

const Header = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { data } = useSimpleCart()
  const { isLoggedIn } = authStore()

  const { mutation } = useLogout()
  const { data: wishlistItems } = useWishlist(1, isLoggedIn)
  const { data: customerData } = useCustomerDetails(isLoggedIn)

  const handleLinkClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    if ((event.target as HTMLElement).tagName === 'A') {
      setIsOpen(false)
    }
  }

  const handleLogout = () => {
    mutation.mutate()
  }

  const cartItemsQuantity = (cart_items: CartItemShape[]) => {
    let item_qty = 0
    if (cart_items) {
      cart_items.forEach((item) => {
        item_qty += item.quantity
      })
    }
    return item_qty
  }
  useEffect(() => {}, [customerData, isLoggedIn])
  return (
    <>
      <div className={styles.header}>
        <section className={`${styles.header__top} container`}>
          {/* Logo section */}
          <section className={styles.header__logo}>
            <Logo />
          </section>

          {/* Search section */}
          <section className={styles.header__search}>
            <Search />
          </section>

          {/* Wishlist, Cart, Signup, Dropdown section */}
          <section className={styles.header__end}>
            <Link
              href='/account/wishlist'
              title='wishlist'
              className={styles.wishlist}
            >
              <p className={styles.header__badge}>
                <span>{wishlistItems?.count || 0}</span>
              </p>
              <i className={styles.header__icon}>
                <FaHeart />
              </i>
              {/* <Link href='/account/wishlist/'>Wishlist {`|`}</Link> */}
            </Link>

            <Link href='/checkout/cart' className={styles.cart} title='cart'>
              <p className={styles.header__badge}>
                <span>{cartItemsQuantity(data?.cart_items || [])}</span>
              </p>
              <i className={styles.header__icon}>
                <FaCartPlus />
              </i>
              {/* <Link href='/cart/'>Cart {`|`}</Link> */}
            </Link>

            <div
              className={styles.header__sign_in}
              onMouseEnter={() => isLoggedIn && setIsOpen(true)}
              onMouseLeave={() => isLoggedIn && setIsOpen(false)}
            >
              <div
                className={`${styles.header__login} ${
                  !isLoggedIn ? styles.header__login_links : ''
                }`}
              >
                <p>
                  {!isLoggedIn && (
                    <>
                      <Link href='/login/'>Sign In</Link>
                      {' | '}
                      <Link href='/register/initiate/'>Sign Up</Link>
                    </>
                  )}
                  {isLoggedIn && customerData?.first_name && (
                    <>Hello, {customerData.first_name}</>
                  )}
                  {isLoggedIn && !customerData?.first_name && (
                    <Link
                      className={styles.warning_link}
                      href='/account/profile'
                    >
                      !Complete your profile
                    </Link>
                  )}
                </p>
                {isLoggedIn && (
                  <i>
                    <FaCaretDown />
                  </i>
                )}
              </div>
              {isOpen && isLoggedIn && (
                <div
                  className={`${styles.dropdown_container} ${
                    isOpen ? styles.active : ''
                  }`}
                  onClick={handleLinkClick}
                >
                  <div className={styles.dropdown}>
                    <div className={styles.dropdown_header}>
                      <h4>
                        Welcome back, {customerData?.first_name || 'User'}
                      </h4>
                    </div>
                    <div className={styles.dropdown_menu}>
                      <div className={styles.menu_item}>
                        <i>
                          <MdOutlineAccountCircle />
                        </i>
                        <Link href='/account/profile'>My Account</Link>
                      </div>
                      <div className={styles.menu_item}>
                        <i>
                          <BsBox2 />
                        </i>
                        <Link href='/account/orders'>My Orders</Link>
                      </div>
                      <div className={styles.menu_item}>
                        <i>
                          <BsBox2 />
                        </i>
                        <Link href='/account/wishlist'>My WishList</Link>
                      </div>
                      {/* <div className={styles.divider}></div>
                      <div className={styles.menu_item}>
                        <i><BsBox2 /></i>
                        <Link href='/'>My Reviews</Link>
                      </div> */}
                    </div>
                    <div className={styles.dropdown_footer}>
                      <div className={styles.menu_item} onClick={handleLogout}>
                        <i>
                          <RiLogoutCircleRLine />
                        </i>
                        <span>Sign Out</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </section>
        </section>
      </div>
      <div className={styles.header__bottom_nav}>
        <section className={`${styles.header__bottom} container`}>
          <Navbar />
        </section>
      </div>
    </>
  )
}

export default Header
