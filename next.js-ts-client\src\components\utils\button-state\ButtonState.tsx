'use client'

import React from 'react'
import Spinner from '@/src/components/utils/spinner/Spinner'
import styles from './ButtonState.module.scss'

interface ButtonStateProps {
  isLoading: boolean
  loadingText?: string
  buttonText: string
  spinnerSize?: number
  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'
  spinnerColor?: string
  spinnerThickness?: number
  children?: React.ReactNode
}

const ButtonState: React.FC<ButtonStateProps> = ({
  isLoading,
  loadingText = 'Loading...',
  buttonText,
  spinnerSize = 20,
  spinnerColor = '#ffffff',
  spinnerType,
  spinnerThickness = 2,
}) => {
  return (
    <>
      {isLoading ? (
        <span className={styles.loadingSpan}>
          <Spinner
            loading={isLoading}
            size={spinnerSize}
            color={spinnerColor}
            spinnerType={spinnerType}
            thickness={spinnerThickness}
          />
          <span>{loadingText}</span>
        </span>
      ) : (
        buttonText
      )}
    </>
  )
}

export default ButtonState