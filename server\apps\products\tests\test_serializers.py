# Test serializers - field validation, custom logic, representation
import pytest
from decimal import Decimal
from model_bakery import baker
from apps.products.serializers import (
    CategorySerializer, ProductSerializer, ProductDetailSerializer,
    ProductListSerializer, ReviewSerializer, ProductVariantSerializer
)
from apps.products.models import (
    Category, Product, ProductVariant, Review, ProductType, Brand,
    Attribute, AttributeValue, ProductImage
)
from apps.customers.models import Customer


@pytest.mark.django_db
class TestCategorySerializer:
    """Test CategorySerializer validation and representation"""
    
    def test_category_serialization(self):
        """Test category serialization includes all expected fields"""
        parent = baker.make(Category, title="Electronics")
        category = baker.make(Category, title="Laptops", parent=parent)
        
        serializer = CategorySerializer(category)
        data = serializer.data
        
        assert 'id' in data
        assert 'title' in data
        assert 'slug' in data
        assert 'level' in data
        assert 'parent' in data
        assert data['title'] == "Laptops"
        assert data['parent'] == parent.id
    
    def test_category_deserialization_valid_data(self):
        """Test category deserialization with valid data"""
        parent = baker.make(Category)
        data = {
            'title': 'New Category',
            'parent': parent.id
        }
        
        serializer = CategorySerializer(data=data)
        
        assert serializer.is_valid()
        category = serializer.save()
        assert category.title == 'New Category'
        assert category.parent == parent
    
    def test_category_deserialization_invalid_data(self):
        """Test category deserialization with invalid data"""
        data = {
            'title': '',  # Empty title should be invalid
        }
        
        serializer = CategorySerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'title' in serializer.errors
    
    def test_category_title_max_length_validation(self):
        """Test category title max length validation"""
        data = {
            'title': 'x' * 51,  # Exceeds max_length of 50
        }
        
        serializer = CategorySerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'title' in serializer.errors
    
    def test_category_update(self):
        """Test updating category through serializer"""
        category = baker.make(Category, title="Old Title")
        data = {'title': 'Updated Title'}
        
        serializer = CategorySerializer(category, data=data, partial=True)
        
        assert serializer.is_valid()
        updated_category = serializer.save()
        assert updated_category.title == 'Updated Title'


@pytest.mark.django_db
class TestProductSerializer:
    """Test ProductSerializer validation and representation"""
    
    def test_product_serialization(self):
        """Test product serialization includes expected fields"""
        brand = baker.make(Brand)
        product_type = baker.make(ProductType)
        product = baker.make(Product, brand=brand, product_type=product_type)
        
        serializer = ProductSerializer(product)
        data = serializer.data
        
        expected_fields = ['id', 'title', 'slug', 'brand', 'product_type', 'average_rating']
        for field in expected_fields:
            assert field in data
    
    def test_product_average_rating_read_only(self):
        """Test that average_rating field is read-only"""
        product = baker.make(Product)
        data = {
            'title': 'Test Product',
            'average_rating': 5.0  # This should be ignored
        }
        
        serializer = ProductSerializer(product, data=data, partial=True)
        
        assert serializer.is_valid()
        # average_rating should not be in validated_data
        assert 'average_rating' not in serializer.validated_data


@pytest.mark.django_db
class TestProductDetailSerializer:
    """Test ProductDetailSerializer validation and representation"""
    
    def test_product_detail_serialization(self):
        """Test product detail serialization includes all fields"""
        category = baker.make(Category)
        brand = baker.make(Brand)
        product_type = baker.make(ProductType)
        product = baker.make(
            Product, 
            category=category, 
            brand=brand, 
            product_type=product_type
        )
        
        # Create related objects
        variant = baker.make(ProductVariant, product=product)
        baker.make(ProductImage, product_variant=variant)
        customer = baker.make(Customer)
        baker.make(Review, product=product, customer=customer)
        
        serializer = ProductDetailSerializer(product)
        data = serializer.data
        
        expected_fields = [
            'id', 'title', 'slug', 'category', 'brand', 'product_type',
            'description', 'is_digital', 'is_active', 'average_rating',
            'product_variant', 'option_selectors', 'reviews'
        ]
        
        for field in expected_fields:
            assert field in data
        
        # Check nested serialization
        assert isinstance(data['product_variant'], list)
        assert isinstance(data['reviews'], list)
        assert isinstance(data['category'], dict)
    
    def test_product_detail_option_selectors_method(self):
        """Test option_selectors SerializerMethodField"""
        product_type = baker.make(ProductType)
        product = baker.make(Product, product_type=product_type)
        
        # Create option selector attribute
        attribute = baker.make(Attribute, title="Size")
        from apps.products.models import ProductTypeAttribute
        baker.make(ProductTypeAttribute, 
                  product_type=product_type, 
                  attribute=attribute, 
                  is_option_selector=True)
        
        serializer = ProductDetailSerializer(product)
        data = serializer.data
        
        assert 'option_selectors' in data
        # The method should return option selector attributes


@pytest.mark.django_db
class TestProductListSerializer:
    """Test ProductListSerializer validation and representation"""
    
    def test_product_list_serialization(self):
        """Test product list serialization includes expected fields"""
        product_type = baker.make(ProductType)
        product = baker.make(Product, product_type=product_type)
        variant = baker.make(ProductVariant, product=product)
        baker.make(ProductImage, product_variant=variant)
        
        serializer = ProductListSerializer(product)
        data = serializer.data
        
        expected_fields = ['id', 'title', 'slug', 'average_rating', 'product_variant', 'product_type']
        for field in expected_fields:
            assert field in data
        
        # Check that product_variant is properly nested
        assert isinstance(data['product_variant'], list)
        if data['product_variant']:
            variant_data = data['product_variant'][0]
            assert 'id' in variant_data
            assert 'price' in variant_data
            assert 'product_image' in variant_data


@pytest.mark.django_db
class TestProductVariantSerializer:
    """Test ProductVariantSerializer validation and representation"""
    
    def test_product_variant_serialization(self):
        """Test product variant serialization"""
        product = baker.make(Product)
        variant = baker.make(ProductVariant, product=product, price=Decimal('99.99'))
        baker.make(ProductImage, product_variant=variant)
        
        serializer = ProductVariantSerializer(variant)
        data = serializer.data
        
        # Check that price is properly serialized as decimal, not string
        assert isinstance(data['price'], (int, float, Decimal))
        assert data['price'] == 99.99
    
    def test_product_variant_price_validation(self):
        """Test product variant price validation"""
        product = baker.make(Product)
        data = {
            'product': product.id,
            'price': '0.50',  # Below minimum of 1
            'sku': 'TEST-SKU',
            'stock_qty': 10,
            'weight': '100.00'
        }
        
        serializer = ProductVariantSerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'price' in serializer.errors
    
    def test_product_variant_sku_required(self):
        """Test that SKU is required"""
        product = baker.make(Product)
        data = {
            'product': product.id,
            'price': '99.99',
            # 'sku': missing
            'stock_qty': 10,
            'weight': '100.00'
        }
        
        serializer = ProductVariantSerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'sku' in serializer.errors


@pytest.mark.django_db
class TestReviewSerializer:
    """Test ReviewSerializer validation and representation"""
    
    def test_review_serialization(self):
        """Test review serialization includes all fields"""
        customer = baker.make(Customer)
        product = baker.make(Product)
        review = baker.make(Review, customer=customer, product=product, rating=5)
        
        serializer = ReviewSerializer(review)
        data = serializer.data
        
        expected_fields = ['id', 'title', 'description', 'rating', 'posted_at', 'customer']
        for field in expected_fields:
            assert field in data
        
        assert data['rating'] == 5
    
    def test_review_rating_validation_minimum(self):
        """Test review rating minimum validation"""
        customer = baker.make(Customer)
        product = baker.make(Product)
        data = {
            'title': 'Bad Review',
            'description': 'Terrible product',
            'rating': 0,  # Below minimum of 1
            'customer': customer.id,
            'product': product.id
        }
        
        serializer = ReviewSerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'rating' in serializer.errors
    
    def test_review_rating_validation_maximum(self):
        """Test review rating maximum validation"""
        customer = baker.make(Customer)
        product = baker.make(Product)
        data = {
            'title': 'Impossible Review',
            'description': 'Too good to be true',
            'rating': 6,  # Above maximum of 5
            'customer': customer.id,
            'product': product.id
        }
        
        serializer = ReviewSerializer(data=data)
        
        assert not serializer.is_valid()
        assert 'rating' in serializer.errors
    
    def test_review_valid_rating_range(self):
        """Test review with valid rating range"""
        customer = baker.make(Customer)
        product = baker.make(Product)
        
        for rating in [1, 2, 3, 4, 5]:
            data = {
                'title': f'Review {rating}',
                'description': f'Rating {rating} review',
                'rating': rating,
                'customer': customer.id,
                'product': product.id
            }
            
            serializer = ReviewSerializer(data=data)
            assert serializer.is_valid(), f"Rating {rating} should be valid"
    
    def test_review_required_fields(self):
        """Test that required fields are enforced"""
        data = {
            'title': 'Incomplete Review',
            # Missing required fields: description, rating, customer, product
        }
        
        serializer = ReviewSerializer(data=data)
        
        assert not serializer.is_valid()
        required_fields = ['description', 'rating', 'customer']
        for field in required_fields:
            assert field in serializer.errors
    
    def test_review_with_context(self):
        """Test review serializer with context (product from URL)"""
        customer = baker.make(Customer)
        product = baker.make(Product)
        
        data = {
            'title': 'Great Product',
            'description': 'Love it!',
            'rating': 5,
            'customer': customer.id
        }
        
        # Simulate context passed from view
        context = {'product': product}
        serializer = ReviewSerializer(data=data, context=context)
        
        assert serializer.is_valid()
        review = serializer.save()
        assert review.product == product
