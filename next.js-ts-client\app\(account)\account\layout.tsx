'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import classNames from 'classnames'
import { IoMdPerson } from 'react-icons/io'
import { FaBoxArchive } from 'react-icons/fa6'
import { PiListHeartFill } from 'react-icons/pi'
import styles from './Layout.module.scss'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'

const AccountLayout = ({ children }: { children: React.ReactNode }) => {
  const { data: customerData } = useCustomerDetails()
  const pathname = usePathname()

  return (
    <div className={`${styles.my_account} container`}>
      <section className={styles.navbar}>
        <h3>Hello, {customerData?.first_name || 'User'}</h3>
        <div className={styles.navbar__links}>
          <div className={styles.navbar__links__links}>
            <Link
              href='/account/profile'
              className={classNames({
                [styles.active]: pathname === '/account/profile',
              })}
            >
              <i>
                <IoMdPerson />
              </i>
              <span>My Profile</span>
            </Link>
            <Link
              href='/account/orders'
              className={classNames({
                [styles.active]: pathname === '/account/orders',
              })}
            >
              <i>
                <FaBoxArchive />
              </i>
              <span>My Orders</span>
            </Link>
            <Link
              href='/account/wishlist'
              className={classNames({
                [styles.active]: pathname === '/account/wishlist',
              })}
            >
              <i>
                <PiListHeartFill />
              </i>
              <span>My WishList</span>
            </Link>
          </div>
        </div>
      </section>
      <div className={styles.content}>{children}</div>
    </div>
  )
}

export default AccountLayout
