import { AddressFormInputs } from '../components/account/addresses/ManageAddresses'
import { ProductImageShape } from './product-types'
import { CustomerShape, ExtraData } from './store-types'

export interface OrderedItemShape {
  id: number
  product: {
    id: number
    title: string
    slug: string
  }
  product_variant: {
    id: number
    price: number
    price_label: {
      id: number
      attribute_value: string
    }
    product_image: ProductImageShape[]
  }
  extra_data: ExtraData
  quantity: number
  total_price: number
}

export interface OrderShape {
  id: number
  customer: CustomerShape
  order_status: 'Pending' | 'Processing' | 'Dispatched' | 'Delivered'
  payment_method: {
    id: number
    name: string
    slug: string
  }
  ordered_items: OrderedItemShape[]
  payment_status: string
  placed_at: string
  selected_address: AddressFormInputs
  total: number
  payment_intent_id: string
}

export interface createOrderShape {
  cart_id: string
  delivery_status: 'Pending' | 'Processing' | 'Dispatched' | 'Delivered'
  selected_address: number
  payment_method: number
  selected_cart_item_ids?: number[]
}
