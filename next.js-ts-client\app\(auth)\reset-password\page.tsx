'use client'

import Al<PERSON> from '@/src/components/utils/alert/Alert'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import AuthLayout from '../AuthLayout'
import PasswordResetRequest, { ResetRequestShape } from './PasswordResetReq'
import VerifyResetCode from './PasswordResetVerify'

const PasswordReset = () => {
  const router = useRouter()

  const [verificationStep, setVerificationStep] = useState(false)
  const [emailOrPhone, setEmailOrPhone] = useState<ResetRequestShape | null>(null)
  const [verificationSuccess, setVerificationSuccess] = useState(false)

  const handleRequestSuccess = (email_or_phone: ResetRequestShape) => {
    setEmailOrPhone(email_or_phone)
    setVerificationStep(true)
  }

  const handleVerificationSuccess = () => {
    setEmailOrPhone(null)
    setVerificationSuccess(true)
    setTimeout(() => {
      router.push('/login')
    }, 3000)
  }

  return (
    <>
      {verificationSuccess ? (
        <AuthLayout
          title='Password Reset Success'
          error={null}
        >
          <Alert variant='success' message='Password reset successful. You can now login with your new password.' />
          <button
            // className={`empty_btn ${styles.login_btn}`}
            onClick={() => router.push('/login')}
          >Login
          </button>
        </AuthLayout>
      ) : (
        <div>
          {!verificationStep ? (
            <PasswordResetRequest
              onRequestSuccess={handleRequestSuccess}
            />
          ) : (
            <VerifyResetCode
              emailOrPhone={emailOrPhone!.email_or_phone}
              onVerificationSuccess={handleVerificationSuccess} />
          )}
        </div>
      )}
    </>
  )
}

export default PasswordReset
