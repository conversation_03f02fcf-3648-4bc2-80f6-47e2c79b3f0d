// Orders route with permission-based access
// Requires order management permissions

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../components/auth/AuthGuard'
import { OrdersListPage } from '../pages/orders/OrdersListPage'

export const Route = createFileRoute('/orders')({
  component: OrdersRoute,
})

function OrdersRoute() {
  return (
    <AuthGuard permission="staff.view_orderproxy">
      <OrdersListPage />
    </AuthGuard>
  )
}
