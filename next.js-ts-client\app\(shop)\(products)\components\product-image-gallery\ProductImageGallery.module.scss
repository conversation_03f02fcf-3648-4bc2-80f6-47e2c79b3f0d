@use 'sass:color';
@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.product_images {
  display: grid;
  grid-template-columns: 12% 88%;

  @media (max-width: $tablet) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
  }
}

.product_images__list {
  @include flexbox(center, flex-start, column);
  gap: 0.5rem;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: $padding-1;
  width: 100%;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: $border-radius-1;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: $border-radius-1;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #aaa;
  }

  @media (max-width: $tablet) {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    max-height: none;
    padding: 0.5rem;
    margin: 0.5rem 0;
    order: 2;
    justify-content: flex-start;
    width: 100%;

    &::-webkit-scrollbar {
      height: 4px;
      width: auto;
    }
  }
}

.thumbnail {
  width: 60px;
  height: 60px;
  border: 1px solid #ddd;
  border-radius: $border-radius-1;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin: 0 2px;

  &:hover {
    border-color: $primary-blue;
    transform: scale(1.05);
  }

  &.active {
    border: 2px solid $primary-blue;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: $tablet) {
    margin: 0 4px;
  }
}

.product_image {
  margin: 0 auto;
  width: 100%;
  position: relative;

  @media (max-width: $tablet) {
    order: 1;
  }
}

.image_container {
  width: 100%;
  height: 500px; // Fixed height to prevent page jumping
  @include flexbox(center, center);
  overflow: hidden;
  border: 1px solid #eee;
  border-radius: $border-radius-2;
  position: relative; // For absolute positioning of navigation buttons

  &:hover .image_navigation {
    opacity: 1;
  }

  img {
  }

  // NextJS Image component styles are handled inline
}

/* Base styles for the rendered img element (Next.js Image outputs an <img>) */
.image_container img {
  object-fit: contain;
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 500px;
  transition: transform 0.2s ease;
  transform-origin: var(--zoom-origin, center);
}

/* Applied when zoom is active */
.zoomed_image {
  transform: scale(2);
}

/* Cursor when zoom is enabled */
.zoomed_container {
  cursor: zoom-in;
}

.image_navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  @include flexbox(space-between, center, row);
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;

  .nav_button {
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    @include flexbox(center, center);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    pointer-events: auto;
    margin: 0 $padding-1;

    &:hover {
      background-color: rgba(255, 255, 255, 0.9);
      transform: scale(1.1);
    }

    svg {
      font-size: $font-size-3;
      color: $primary-dark-text-color;
    }
  }
}
