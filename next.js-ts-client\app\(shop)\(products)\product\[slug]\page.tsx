import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { ProductService } from '../../../../../src/lib/product-service'
import {
  getDefaultVariant,
  findFirstAvailableImage,
} from '../../components/utils/product-utils'
import ProductDetailsClient from '../../components/product-details-client/ProductDetailsClient'
import StreamErrorBoundary from '../../../../../src/components/error-boundary/StreamErrorBoundary'
import styles from './ProductDetail.module.scss'

interface ProductDetailPageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: ProductDetailPageProps): Promise<Metadata> {
  try {
    const { slug } = await params
    const product = await ProductService.getProductBySlug(slug)
    const defaultVariant = getDefaultVariant(product)
    const imageUrl = findFirstAvailableImage(
      product,
      process.env.NEXT_PUBLIC_CLOUDINARY_URL || ''
    )

    return {
      title: `${product.title} | Picky Store`,
      description:
        product.description || `Buy ${product.title} at the best price`,
      openGraph: {
        title: product.title,
        description:
          product.description || `Buy ${product.title} at the best price`,
        images: imageUrl ? [{ url: imageUrl, alt: product.title }] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: product.title,
        description:
          product.description || `Buy ${product.title} at the best price`,
        images: imageUrl ? [imageUrl] : [],
      },
      other: {
        'product:price:amount': defaultVariant?.price?.toString() || '0',
        'product:price:currency': 'USD',
        'product:availability':
          defaultVariant?.stock_qty && defaultVariant.stock_qty > 0
            ? 'in stock'
            : 'out of stock',
      },
    }
  } catch {
    return {
      title: 'Product Not Found | Picky Store',
      description: 'The requested product could not be found.',
    }
  }
}

export default async function ProductDetailPage({
  params,
}: ProductDetailPageProps) {
  let product

  try {
    const { slug } = await params
    product = await ProductService.getProductBySlug(slug)
  } catch {
    // If product is not found, show 404
    notFound()
  }

  const defaultVariant = getDefaultVariant(product)
  const initialImage = findFirstAvailableImage(
    product,
    process.env.NEXT_PUBLIC_CLOUDINARY_URL || ''
  )

  return (
    <div className='container'>
      {/* Server-rendered breadcrumbs */}
      <div className={styles.breadcrumbs}>
        <Link href='/'>Home</Link> &gt;{' '}
        {product.category && (
          <>
            <Link href={`/products/category/${product.category.slug}` as const}>
              {product.category.title}
            </Link>{' '}
            &gt;{' '}
          </>
        )}
        <span>{product.title}</span>
      </div>

      {/* Client component for interactive functionality */}
      <StreamErrorBoundary>
        <ProductDetailsClient
          product={product}
          defaultVariant={defaultVariant}
          initialImage={initialImage || 'placeholder'}
        />
      </StreamErrorBoundary>
    </div>
  )
}
