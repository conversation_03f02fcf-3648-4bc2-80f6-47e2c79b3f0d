import styles from './loading.module.scss'

export default function Loading() {
  return (
    <div className={`container ${styles.loading_container}`}>
      {/* Breadcrumb skeleton */}
      <div className={styles.breadcrumb_skeleton}>
        <div className={styles.skeleton_item}></div>
        <span>&gt;</span>
        <div className={styles.skeleton_item}></div>
        <span>&gt;</span>
        <div className={styles.skeleton_item}></div>
      </div>

      <section className={styles.product_details_skeleton}>
        {/* Image gallery skeleton */}
        <div className={styles.image_section}>
          <div className={styles.thumbnails}>
            {[...Array(4)].map((_, i) => (
              <div key={i} className={styles.thumbnail_skeleton}></div>
            ))}
          </div>
          <div className={styles.main_image_skeleton}></div>
        </div>

        {/* Product info skeleton */}
        <div className={styles.info_section}>
          <div className={styles.title_skeleton}></div>
          <div className={styles.rating_skeleton}></div>

          <div className={styles.price_section}>
            <div className={styles.price_skeleton}></div>
            <div className={styles.original_price_skeleton}></div>
          </div>

          <div className={styles.variants_section}>
            <div className={styles.variant_title_skeleton}></div>
            <div className={styles.variants_skeleton}>
              {[...Array(3)].map((_, i) => (
                <div key={i} className={styles.variant_skeleton}></div>
              ))}
            </div>
          </div>

          <div className={styles.quantity_section}>
            <div className={styles.quantity_label_skeleton}></div>
            <div className={styles.quantity_controls_skeleton}></div>
          </div>

          <div className={styles.buttons_section}>
            <div className={styles.button_skeleton}></div>
            <div className={styles.button_skeleton}></div>
          </div>
        </div>
      </section>

      {/* Tabs skeleton */}
      <section className={styles.tabs_skeleton}>
        <div className={styles.tabs_header_skeleton}>
          {[...Array(3)].map((_, i) => (
            <div key={i} className={styles.tab_skeleton}></div>
          ))}
        </div>
        <div className={styles.tab_content_skeleton}>
          {[...Array(4)].map((_, i) => (
            <div key={i} className={styles.content_line_skeleton}></div>
          ))}
        </div>
      </section>
    </div>
  )
}