from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import logging
from ..shipping import ShippingRate
from ..packing import PackingResult


class BaseCarrier(ABC):
    """Abstract base class for shipping carriers"""
    
    def __init__(self, config):
        """Initialize carrier with configuration"""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.code}")
    
    @abstractmethod
    def get_shipping_rate(self, packing_result: PackingResult, 
                         destination_address) -> Optional[ShippingRate]:
        """
        Calculate shipping rate for given packing result and destination
        
        Args:
            packing_result: Result of packing calculation
            destination_address: Destination address object
            
        Returns:
            ShippingRate object or None if calculation fails
        """
        pass
    
    @abstractmethod
    def create_shipment(self, order, packing_result: PackingResult) -> Dict[str, Any]:
        """
        Create shipment with carrier
        
        Args:
            order: Order object
            packing_result: Result of packing calculation
            
        Returns:
            Dictionary with shipment details including tracking number
        """
        pass
    
    @abstractmethod
    def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """
        Track shipment status
        
        Args:
            tracking_number: Tracking number from carrier
            
        Returns:
            Dictionary with tracking information
        """
        pass
    
    def test_connection(self) -> bool:
        """
        Test connection to carrier API
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Default implementation - subclasses should override
            return True
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
    
    def validate_address(self, address) -> Dict[str, Any]:
        """
        Validate shipping address with carrier
        
        Args:
            address: Address object to validate
            
        Returns:
            Dictionary with validation results
        """
        # Default implementation - subclasses can override
        return {
            'valid': True,
            'normalized_address': address,
            'suggestions': []
        }
    
    def get_service_options(self) -> list:
        """
        Get available service options for this carrier
        
        Returns:
            List of service dictionaries
        """
        services = []
        for service in self.config.services.filter(is_active=True):
            services.append({
                'id': service.id,
                'name': service.service_name,
                'code': service.service_code,
                'estimated_days': service.estimated_days,
                'max_days': service.max_days,
                'supports_insurance': service.supports_insurance,
                'supports_signature': service.supports_signature
            })
        return services
    
    def calculate_base_rate(self, weight_grams: float, destination_country: str = 'US') -> float:
        """
        Calculate base shipping rate - to be overridden by subclasses
        
        Args:
            weight_grams: Total weight in grams
            destination_country: Destination country code
            
        Returns:
            Base rate as float
        """
        # Simple fallback calculation
        if weight_grams <= 250:
            return 8.50
        elif weight_grams <= 500:
            return 10.50
        elif weight_grams <= 1000:
            return 12.50
        elif weight_grams <= 2000:
            return 15.50
        elif weight_grams <= 5000:
            return 19.50
        else:
            return 25.50
    
    def apply_distance_modifier(self, base_rate: float, destination_country: str) -> float:
        """
        Apply distance-based rate modifier
        
        Args:
            base_rate: Base shipping rate
            destination_country: Destination country code
            
        Returns:
            Modified rate
        """
        # Default implementation - no modifier
        return base_rate
    
    def apply_service_modifier(self, base_rate: float, service_code: str) -> float:
        """
        Apply service-specific rate modifier
        
        Args:
            base_rate: Base shipping rate
            service_code: Service code
            
        Returns:
            Modified rate
        """
        # Find service and apply multiplier
        try:
            service = self.config.services.get(service_code=service_code, is_active=True)
            return base_rate * float(service.cost_multiplier)
        except:
            return base_rate
    
    def get_estimated_delivery_days(self, destination_country: str, service_code: str = None) -> int:
        """
        Get estimated delivery days
        
        Args:
            destination_country: Destination country code
            service_code: Optional service code
            
        Returns:
            Estimated delivery days
        """
        if service_code:
            try:
                service = self.config.services.get(service_code=service_code, is_active=True)
                return service.estimated_days
            except:
                pass
        
        # Default estimation based on destination
        if destination_country in ['US', 'CA']:
            return 3
        elif destination_country in ['NO', 'SE', 'DK', 'FI']:
            return 2
        elif destination_country in ['DE', 'NL', 'BE', 'FR', 'GB']:
            return 5
        else:
            return 7
    
    def format_error_response(self, error_message: str) -> Dict[str, Any]:
        """
        Format error response consistently
        
        Args:
            error_message: Error message
            
        Returns:
            Formatted error dictionary
        """
        return {
            'success': False,
            'error': error_message,
            'carrier': self.config.title,
            'timestamp': str(timezone.now()) if 'timezone' in globals() else None
        }
