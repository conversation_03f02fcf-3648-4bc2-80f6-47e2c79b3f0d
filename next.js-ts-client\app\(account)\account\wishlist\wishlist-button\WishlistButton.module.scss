@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

// Mobile-first base styles
.wishlist__button {
  @include btn(#fff, $primary-blue);
  background: none !important;
  border: 2px solid $primary-blue;
  height: 40px;
  padding: 0 5px !important;
  margin: 0;
  max-width: 100% !important;
  width: 100%;

  i {
    font-size: 24px;
    color: $primary-blue;
    @include flexbox(center, center);
    width: 100%;
    height: 100%;
  }

  &:hover {
    border-color: darken($lighten-blue, 15%);

    i {
      color: darken($lighten-blue, 15%);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Larger screens (above mobile)
@media (min-width: 451px) {
  .wishlist__button {
    max-width: 50px !important;
    width: auto;
  }
}
