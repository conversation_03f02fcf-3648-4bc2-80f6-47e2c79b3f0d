@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

// Skeleton animation
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Base skeleton styling
%skeleton-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: $border-radius-1;
}

.product_list_skeleton_container {
  @include flexbox(center, center, column);
  min-height: 60vh;
  padding: 1rem 0;
}

// Mobile controls skeleton
.mobile_controls_skeleton {
  @include flexbox(space-between, center);
  width: 100%;
  padding: $padding-3 $padding-4;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: $border-radius-3;
}

.control_button_skeleton {
  @extend %skeleton-base;
  height: 36px;
  width: 100px;
}

// Filters skeleton
.filters_skeleton {
  display: none;
  width: 100%;
  margin-bottom: 1rem;
  background: white;
  border-radius: $border-radius-3;
  border: 1px solid #f0f0f0;
  padding: $padding-4;

  &.show {
    display: block;
  }
}

.filter_skeleton {
  .filter_section {
    margin-bottom: $padding-5;
    padding-bottom: $padding-4;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
}

.filter_title_skeleton {
  @extend %skeleton-base;
  height: 20px;
  width: 120px;
  margin-bottom: $padding-3;
}

.price_range_skeleton {
  margin-top: $padding-3;

  .range_label_skeleton {
    @extend %skeleton-base;
    height: 16px;
    width: 60px;
    margin-bottom: $padding-2;
  }

  .range_slider_skeleton {
    @extend %skeleton-base;
    height: 6px;
    width: 100%;
    margin-bottom: $padding-3;
  }
}

.filter_option_skeleton {
  @include flexbox(flex-start, center);
  margin-bottom: $padding-2;
  gap: $padding-2;

  .checkbox_skeleton {
    @extend %skeleton-base;
    width: 16px;
    height: 16px;
    border-radius: $border-radius-1;
  }

  .option_label_skeleton {
    @extend %skeleton-base;
    height: 16px;
    width: 80px;
  }
}

// Product list wrapper skeleton
.product_list_wrapper_skeleton {
  width: 100%;
  flex: 1;
}

// Sorting skeleton
.sorting_wrapper {
  display: none;
  margin: 0.5rem 1rem 1rem 0;
  text-align: right;

  &.show {
    display: block;
  }
}

.sorting_skeleton {
  @include flexbox(flex-end, center);
}

.sort_select_skeleton {
  @extend %skeleton-base;
  height: 36px;
  width: 200px;
}

// Product list skeleton
.product_list_skeleton {
  margin: 1rem 0 2rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
  list-style-type: none;
  padding: 0;
  justify-items: center;

  li {
    width: 100%;
    max-width: 280px;
  }
}

// Product card skeleton
.product_card_skeleton {
  width: 100%;
  max-width: 280px;
  border-radius: $border-radius-2;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
}

.image_skeleton {
  @extend %skeleton-base;
  width: 100%;
  aspect-ratio: 1;
}

.info_skeleton {
  padding: 1rem;
}

.title_skeleton {
  @extend %skeleton-base;
  height: 20px;
  width: 85%;
  margin-bottom: 0.75rem;
}

.rating_skeleton {
  @extend %skeleton-base;
  height: 16px;
  width: 120px;
  margin-bottom: 0.75rem;
}

.price_skeleton {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
  margin-top: 0.75rem;

  .current_price_skeleton {
    @extend %skeleton-base;
    height: 18px;
    width: 60px;
  }

  .original_price_skeleton {
    @extend %skeleton-base;
    height: 14px;
    width: 50px;
  }
}

// Pagination skeleton
.pagination_skeleton {
  @include flexbox(center, center);
  gap: 0.5rem;
  margin-top: 2rem;
}

.pagination_button_skeleton {
  @extend %skeleton-base;
  height: 36px;
  width: 80px;
}

.pagination_number_skeleton {
  @extend %skeleton-base;
  height: 36px;
  width: 36px;
}

// Tablet styles
@media (width > $tablet) {
  .product_list_skeleton {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 2rem;
    padding: 0 1rem;
  }

  .mobile_controls_skeleton {
    padding: $padding-4 $padding-5;
  }
}

// Laptop and desktop styles
@media (width > $laptop) {
  .product_list_skeleton_container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
    padding: 2rem 0;
  }

  .product_list_wrapper_skeleton {
    .product_list_skeleton {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 2rem;
      padding: 0;
      justify-items: start;
    }
  }

  .mobile_controls_skeleton {
    display: none;
  }

  .filters_skeleton {
    display: block !important;
    width: 100%;
    margin-bottom: 0;
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
  }

  .sorting_wrapper {
    display: block !important;
    text-align: right;
    margin: 0 0 1.5rem 0;
  }
}

// Large monitor styles
@media (width > $monitor) {
  .product_list_skeleton_container {
    grid-template-columns: 320px 1fr;
    gap: 3rem;
  }

  .product_list_wrapper_skeleton {
    .product_list_skeleton {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 2.5rem;
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  %skeleton-base {
    animation: none;
    background: #f0f0f0;
  }
}
