'use client'

import Alert from '@/src/components/utils/alert/Alert'
import ButtonState from '@/src/components/utils/button-state/ButtonState'
import { useLogin } from '@/src/hooks/auth-hooks'
import { useTogglePasswordVisibility } from '@/src/hooks/other-hooks'
import { loginSchema } from '@/src/schemas/schemas'
import authStore from '@/src/stores/auth-store'
import { ErrorResponse } from '@/src/types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import { z } from 'zod'
import AuthLayout from '../AuthLayout'
import styles from './Login.module.scss'

export type LoginUserShape = z.infer<typeof loginSchema>

const Login = () => {
  const router = useRouter()
  const { isVisible, toggleVisibility } = useTogglePasswordVisibility()
  const { mutation } = useLogin()
  const { isLoggedIn } = authStore()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginUserShape>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit: SubmitHandler<LoginUserShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        // reset()
        // setTimeout(() => {
        //   navigate('/')
        //   // navigate('/checkout/')
        // }, 4000)
      },
    })
  }

  // Redirect immediately if user is already logged in
  useEffect(() => {
    if (isLoggedIn) {
      router.push('/')
    }
  }, [isLoggedIn, router])

  // Return null to prevent rendering anything if user is already logged in
  if (isLoggedIn) {
    return null
  }

  // for testing, making the loading true
  // mutation.isPending = true

  return (
    <AuthLayout
      title='Login'
      error={mutation.error as AxiosError<ErrorResponse> | null}
    >
      {mutation.isSuccess ? (
        <div>
          <Alert
            variant='success'
            message='Login successful.'
            textAlign='center'
          />
          <div className={styles.login_nav}>
            <h3>Navigate me to:</h3>
            <div>
              {/* <Link href='/checkout'>Continue Checkout</Link> */}
              <Link href='/account/profile'>Update Profile</Link>
              {/* <Link href='/cart'>Shopping Cart</Link> */}
            </div>
          </div>
        </div>
      ) : (
        <div>
          {/* {mutation.error && <Alert variant="error" message={`Login failed. Please try again later.`} />} */}
          <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
            <div>
              <label htmlFor='username'>Email or phone number:</label>
              <input
                placeholder={` Eg: +***********`}
                type='text'
                id='username'
                {...register('username')}
                disabled={mutation.isPending}
              />
              {errors.username && <p>{errors.username.message}</p>}
            </div>
            <div>
              <div className={styles.password__reset}>
                <label htmlFor='password'>Password:</label>
                <Link href='/reset-password'>Forget password?</Link>
              </div>
              <section className='password__container'>
                <input
                  type={isVisible ? 'text' : 'password'}
                  id='password'
                  {...register('password')}
                  disabled={mutation.isPending}
                />
                <span onClick={toggleVisibility}>
                  <i>{isVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.password && <p>{errors.password.message}</p>}
            </div>
            <button
              className={styles.login_btn}
              type='submit'
              disabled={mutation.isPending}
            >
              <ButtonState
                isLoading={mutation.isPending}
                loadingText='Logging in...'
                buttonText='Login'
                spinnerSize={16}
                spinnerColor='#fff'
                spinnerType='clip'
              />
            </button>
            <p className={styles.login_or_register}>
              Don&apos;t have an account yet?{' '}
              <Link href='/register/initiate'>Register</Link>
            </p>
          </form>
        </div>
      )}
    </AuthLayout>
  )
}

export default Login
