@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.summary_container {
  background-color: $sky-lighter-blue;
  border: 1px solid #d1d5db;
  border-radius: $border-radius-2;
  padding: $padding-4;
  margin-top: $padding-3;
  box-shadow: $box-shadow-1;
}

.summary_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $padding-3;
  padding-bottom: $padding-2;
  border-bottom: 1px solid #d1d5db;

  h3 {
    margin: 0;
    font-size: $font-size-5;
    font-weight: map-get($font-weight, 'medium');
    color: $primary-dark-text-color;
  }

  .item_count {
    font-size: $font-size-2;
    color: $primary-lighter-text-color;
    background-color: #f9fafb;
    padding: $padding-1 $padding-2;
    border-radius: $border-radius-1;
  }
}

.summary_details {
  position: relative;
}

.summary_row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $padding-2 0;
  border-bottom: 1px solid rgba(#d1d5db, 0.5);

  &:last-child {
    border-bottom: none;
  }

  span {
    font-size: $font-size-3;

    &:first-child {
      color: $primary-lighter-text-color;
    }
  }

  .price {
    font-weight: map-get($font-weight, 'medium');
    color: $primary-blue;
    font-size: $font-size-3;
  }
}

// Price summary section styles (from PriceSummary)
.price_summary_section {
  margin-top: $padding-3;

  .summary_row {
    @include flexbox(space-between, center);
    width: 100%;
    font-size: 1.2rem;
    padding: $padding-2 0;
    border-bottom: 1px solid rgba(#d1d5db, 0.5);

    &:last-child {
      border-bottom: none;
    }

    span:first-child {
      font-weight: map-get($font-weight, 'medium');
      color: $primary-dark-text-color;

      @include flexbox(flex-start, center);
      column-gap: 2px;
    }

    span:last-child {
      font-weight: map-get($font-weight, 'medium');
      color: $primary-blue;
    }
  }

  .total_row {
    border-top: 1px solid $primary-blue;
    padding-top: 1rem;
    margin-top: 0.5rem;

    span {
      font-size: 1.3rem !important;
    }
  }
}

.divider {
  height: 1px;
  background-color: #d1d5db;
  margin: $padding-3 0 $padding-2 0;
}

// Checkout button styles (from PriceSummary)
.checkout_section {
  margin-top: $padding-3;
  @include flexbox(center, center);
}

.checkout_button {
  @include btn(#fff, $lighten-blue);
  width: 100%;
  padding: 1rem 0;
  font-weight: map-get($font-weight, 'medium');
  text-transform: uppercase;
  letter-spacing: 0.7px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: darken($lighten-blue, 10%);
    color: darken(#fff, 15%);
  }
}

.comparison_section {
  margin-top: $padding-3;

  .comparison_header {
    margin-bottom: $padding-2;

    span {
      font-size: $font-size-2;
      font-weight: map-get($font-weight, 'medium');
      color: $primary-lighter-text-color;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .comparison_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $padding-1 0;
    font-size: $font-size-2;

    .savings {
      color: $primary-green;
      font-weight: map-get($font-weight, 'medium');
    }
  }
}

.no_selection {
  text-align: center;
  padding: $padding-5 $padding-4;
  color: $primary-lighter-text-color;

  p {
    margin: 0 0 $padding-1 0;
    font-size: $font-size-3;
    font-weight: map-get($font-weight, 'medium');
  }

  span {
    font-size: $font-size-2;
    color: #9ca3af;
  }
}

.loading_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(white, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-2;

  span {
    font-size: $font-size-2;
    color: $primary-lighter-text-color;
    font-style: italic;
  }
}

// Mobile responsiveness
@media (max-width: $mobile) {
  .summary_container {
    padding: $padding-3;
    margin-top: $padding-2;
  }

  .summary_header {
    flex-direction: column;
    align-items: flex-start;
    gap: $padding-1;

    h3 {
      font-size: $font-size-3;
    }

    .item_count {
      font-size: $font-size-1;
    }
  }

  .summary_row {
    padding: $padding-1 0;

    span {
      font-size: $font-size-2;
    }
  }

  .comparison_row {
    font-size: $font-size-1;
  }

  .price_summary_section {
    .summary_row {
      font-size: 1rem;
      padding: $padding-1 0;

      span {
        font-size: $font-size-2;
      }
    }

    .total_row {
      span {
        font-size: 1.1rem !important;
      }
    }
  }

  .checkout_button {
    padding: $padding-3 0;
    font-size: $font-size-2;
  }
}
