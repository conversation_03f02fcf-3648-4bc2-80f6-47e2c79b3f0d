// Error boundary styles
// Clean error display with helpful actions

@use '../../scss/variables' as vars;
@use '../../scss/mixins' as mixins;

.errorBoundary {
  @include mixins.flex-center;
  min-height: 100vh;
  padding: vars.$spacing-8;
  background-color: vars.$gray-50;
}

.errorContent {
  @include mixins.card;
  max-width: 600px;
  padding: vars.$spacing-8;
  text-align: center;
}

.errorIcon {
  @include mixins.flex-center;
  width: vars.$spacing-16;
  height: vars.$spacing-16;
  margin: 0 auto vars.$spacing-6 auto;
  background-color: vars.$error-100;
  border-radius: vars.$border-radius-full;
  color: vars.$error-600;
  
  svg {
    width: vars.$spacing-8;
    height: vars.$spacing-8;
  }
}

.errorTitle {
  font-size: vars.$font-size-2xl;
  font-weight: vars.$font-weight-bold;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-4 0;
}

.errorMessage {
  font-size: vars.$font-size-base;
  color: vars.$gray-600;
  line-height: vars.$line-height-relaxed;
  margin: 0 0 vars.$spacing-6 0;
}

.errorDetails {
  text-align: left;
  margin: vars.$spacing-6 0;
  border: 1px solid vars.$gray-200;
  border-radius: vars.$border-radius;
  
  summary {
    padding: vars.$spacing-3 vars.$spacing-4;
    background-color: vars.$gray-50;
    cursor: pointer;
    font-weight: vars.$font-weight-medium;
    border-radius: vars.$border-radius vars.$border-radius 0 0;
    
    &:hover {
      background-color: vars.$gray-100;
    }
  }
}

.errorStack {
  padding: vars.$spacing-4;
  
  h3 {
    font-size: vars.$font-size-sm;
    font-weight: vars.$font-weight-semibold;
    color: vars.$gray-700;
    margin: 0 0 vars.$spacing-2 0;
    
    &:not(:first-child) {
      margin-top: vars.$spacing-4;
    }
  }
  
  pre {
    background-color: vars.$gray-900;
    color: vars.$gray-100;
    padding: vars.$spacing-3;
    border-radius: vars.$border-radius;
    font-size: vars.$font-size-xs;
    font-family: vars.$font-family-mono;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
  }
}

.errorActions {
  @include mixins.flex-center;
  gap: vars.$spacing-3;
  
  @include mixins.mobile-only {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}

// Responsive adjustments
@include mixins.responsive(sm) {
  .errorBoundary {
    padding: vars.$spacing-12;
  }
  
  .errorContent {
    padding: vars.$spacing-12;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .errorContent {
    border: 2px solid vars.$gray-900;
  }
  
  .errorIcon {
    border: 2px solid vars.$error-600;
  }
}
