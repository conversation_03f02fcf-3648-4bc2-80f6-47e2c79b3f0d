// Admin layout styles
// Responsive layout with sidebar and main content area

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.layout {
  display: flex;
  min-height: 100vh;
  background-color: $gray-50;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: $sidebar-width;
  transition: margin-left 0.3s ease;
  min-width: 0; // Prevent flex item from overflowing

  &.sidebarCollapsed {
    margin-left: $sidebar-collapsed-width;
  }

  @include mobile-only {
    margin-left: 0;

    &.sidebarCollapsed {
      margin-left: 0;
    }
  }
}

.content {
  flex: 1;
  background-color: $gray-50;
  min-height: 0; // Allow content to shrink

  // Add padding for mobile
  @include mobile-only {
    padding-top: $header-height;
  }
}

// Responsive adjustments
@include responsive(lg) {
  .main {
    margin-left: $sidebar-width;

    &.sidebarCollapsed {
      margin-left: $sidebar-collapsed-width;
    }
  }
}

// Print styles
@media print {
  .layout {
    display: block;
  }

  .main {
    margin-left: 0;
  }
}