'use client'

import OrdersListView from '@/app/(account)/account/orders/components/orders-card/OrdersListView'
import OrdersTableView from '@/app/(account)/account/orders/components/orders-table/OrdersTableView'
import Alert from '@/src/components/utils/alert/Alert'
import EmptyOrders from '@/src/components/utils/empty-orders/EmptyOrders'
import Pagination from '@/src/components/utils/pagination/Pagination'
import { ITEMS_PER_PAGE } from '@/src/constants/constants'
import { useDeleteOrder, useGetAllOrders } from '@/src/hooks/order-hooks'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import useLocalStorageState from 'use-local-storage-state'
import OrdersLoading from './loading'
import styles from './OrderList.module.scss'
import ViewToggle from './components/toggle-view/ViewToggle'

const OrderList = () => {
  const [viewMode, setViewMode] = useLocalStorageState<'list' | 'table'>(
    'table'
  )
  const [activeIcon, setActiveIcon] = useLocalStorageState<'list' | 'table'>(
    'table'
  )
  const searchParams = useSearchParams()
  const page = parseInt(searchParams.get('page') || '1', 10)

  const {
    isLoading,
    data: orders,
    error,
  } = useGetAllOrders(page, searchParams.toString())
  const { deleteOrder } = useDeleteOrder()

  useEffect(() => {
    setActiveIcon(viewMode)
  }, [viewMode, setActiveIcon])

  const toggleView = (mode: 'list' | 'table') => {
    setViewMode(mode)
  }

  const handlePageChange = (newPage: number) => {
    const newSearchParams = new URLSearchParams(searchParams.toString())
    newSearchParams.set('page', newPage.toString())
    window.history.pushState(null, '', `?${newSearchParams.toString()}`)
  }

  if (error) {
    return <Alert variant='error' message={error.message} />
  }

  if (isLoading) {
    return <OrdersLoading />
  }

  if (!orders || orders.results.length === 0) {
    return (
      <EmptyOrders
        message='You have not placed any orders yet'
        actionText='Continue Shopping'
      />
    )
  }

  return (
    <>
      <h3 className='title'>Order List</h3>
      {deleteOrder.isError && (
        <>
          <Alert variant='error' message='Error deleting order' />
          <Alert
            variant='info'
            message='You can delete an order if it is in PENDING State'
          ></Alert>
        </>
      )}
      <div className='container'>
        <ViewToggle activeIcon={activeIcon ?? 'table'} onToggle={toggleView} />
        {viewMode === 'list' ? (
          <OrdersListView orders={orders.results} />
        ) : (
          <OrdersTableView orders={orders.results} />
        )}
        {orders.count > 0 && (
          <Pagination
            currentPage={page}
            totalPages={Math.ceil(orders.count / ITEMS_PER_PAGE)}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </>
  )
}

export default OrderList
