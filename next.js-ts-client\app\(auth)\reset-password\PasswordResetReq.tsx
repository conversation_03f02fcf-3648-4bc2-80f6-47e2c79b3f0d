import { useF<PERSON>, SubmitHandler } from 'react-hook-form'
import { AxiosError } from 'axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import AuthLayout from '../AuthLayout'
import { resetPasswordInitSchema } from '@/src/schemas/schemas'
import Alert from '@/src/components/utils/alert/Alert'
import { ErrorResponse } from '@/src/types/types'
import { useInitResetPassword } from '@/src/hooks/auth-hooks'


export type ResetRequestShape = z.infer<typeof resetPasswordInitSchema>

interface Props {
  onRequestSuccess: (email_or_phone: ResetRequestShape) => void
}

const PasswordResetRequest = ({ onRequestSuccess }: Props) => {
  const { mutation } = useInitResetPassword()
  const { register, handleSubmit, formState: { errors } } = useForm<ResetRequestShape>({
    resolver: zodResolver(resetPasswordInitSchema)
  })

  const onSubmit: SubmitHandler<ResetRequestShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: (res) => {
        onRequestSuccess({ email_or_phone: res.email_or_phone })
      }
    })
  }

  return (
    <AuthLayout
      title='Reset Password'
      error={mutation.error as AxiosError<ErrorResponse> | null}
    >
      {!mutation.error && (
        <Alert
          variant="info"
          message="Enter your verified email or phone number to receive a verification code."
        />
      )}
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <div className='form_group'>
          <label className='form_label'>Enter your registered email or phone:</label>
          <input
            disabled={mutation.isPending}
            type="text"
            placeholder='Eg: +94789999999'
            className='form_input'
            {...register("email_or_phone")}
          />
          {errors.email_or_phone && <p className='form_error'>{errors.email_or_phone.message}</p>}
        </div>
        <div>
          <button type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_svg} alt="Loading..." className='loading_svg' />
              'Submitting...'
            ) : 'Submit'}
          </button>
        </div>
      </form>
    </AuthLayout>
  )
}

export default PasswordResetRequest
