import styles from './WishlistLoading.module.scss'

export default function WishlistLoading() {
  return (
    <div className={styles.wishlistLoading}>
      <h1 className={styles.title}></h1>

      <div className={styles.wishlistItems}>
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className={styles.wishlistItem}>
            <div className={`${styles.itemImage} ${styles.pulse}`}></div>
            <div className={styles.itemDetails}>
              <div className={`${styles.itemTitle} ${styles.pulse}`}></div>
              <div className={`${styles.itemPrice} ${styles.pulse}`}></div>
              <div className={`${styles.itemDate} ${styles.pulse}`}></div>
              <div className={styles.itemActions}>
                <div className={`${styles.viewButton} ${styles.pulse}`}></div>
                <div className={`${styles.deleteButton} ${styles.pulse}`}></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
