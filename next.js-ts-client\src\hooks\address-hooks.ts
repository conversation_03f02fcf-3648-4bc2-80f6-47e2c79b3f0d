import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AddressFormInputs } from '../components/account/addresses/ManageAddresses'
import APIClient from '../lib/api-client'
import { CUSTOMER_ADDRESSES, CUSTOMER_DETAILS } from '../constants/constants'


interface AddingAddress extends AddressFormInputs {
  customer: number
}

export interface PartialAddressInputs {
  id?: number,
  full_name?: string,
  street_name?: string,
  address_line_1?: string,
  address_line_2?: string,
  postal_code?: string,
  city_or_village?: string,
  state_or_region?: string,
  country?: string,
  country_code?: string,
}

export const useAddresses = () => {

  const apiClient = new APIClient<AddressFormInputs[]>('/customers/addresses/')

  return useQuery({
    queryKey: [CUSTOMER_ADDRESSES],
    queryFn: () => apiClient.get(),

    // keepPreviousData: true,
    // refetchOnWindowFocus: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
  })
}

export const useCreateAddress = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/customers/addresses/`)

  const createAddress = useMutation({
    mutationFn: (addressData: AddingAddress) => apiClient.post(addressData),

    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { createAddress }
}

export const useUpdateAddress = (addressId: number) => {
  const queryClient = useQueryClient()
  console.log(addressId)

  const apiClient = new APIClient(`/customers/addresses/${addressId}/`)

  const updateAddress = useMutation({
    mutationFn: (addressDetails: PartialAddressInputs) => apiClient.patch(addressDetails),

    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { updateAddress }
}

export const useDeleteAddress = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient('/customers/addresses/')

  const deleteAddress = useMutation({
    mutationFn: (addressId: number) => apiClient.delete(addressId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { deleteAddress }
}

