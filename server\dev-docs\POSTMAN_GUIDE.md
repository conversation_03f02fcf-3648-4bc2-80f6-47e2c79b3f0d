
## **Postman Organization Guide**

### **1. Workspace Structure**

```
Picky Store Admin API
├── 🔐 Authentication
├── 👑 SuperAdmin Collection
├── 📦 Product Manager Collection
├── 📝 Order Manager Collection
├── 👥 Customer Manager Collection
├── 🛡️ Content Moderator Collection
├── 📊 Analytics Collection
└── 🔧 System Admin Collection
```

### **2. Role-Based Collections**

#### **🔐 Authentication Collection**

```
├── Admin Login
├── Token Refresh
├── Token Validation
├── Logout
└── Role Verification
```

#### **👑 SuperAdmin Collection**

```
├── 📦 All Product Operations
├── 📝 All Order Operations
├── 👥 All Customer Operations
├── 🛡️ All Content Operations
├── 👤 User Management
├── 🔑 Role Management
├── 📊 System Analytics
└── 🔧 System Management
```

#### **📦 Product Manager Collection**

```
├── Products/
│   ├── List Products
│   ├── Create Product
│   ├── Update Product
│   ├── Delete Product
│   ├── Bulk Update Products
│   └── Export Products
├── Categories/
│   ├── List Categories
│   ├── Create Category
│   ├── Update Category
│   ├── Delete Category
│   └── Reorder Categories
├── Brands/
│   ├── List Brands
│   ├── Create Brand
│   ├── Update Brand
│   └── Delete Brand
└── Product Types/
    ├── List Product Types
    ├── Create Product Type
    ├── Update Product Type
    └── Assign Attributes
```

#### **📝 Order Manager Collection**

```
├── Orders/
│   ├── List Orders
│   ├── Get Order Details
│   ├── Update Order Status
│   ├── Bulk Update Orders
│   └── Export Orders
├── Order Analytics/
│   ├── Sales Analytics
│   ├── Order Status Analytics
│   └── Performance Metrics
└── Shipping/
    ├── Update Shipping Status
    └── Shipping Analytics
```

#### **👥 Customer Manager Collection**

```
├── Customers/
│   ├── List Customers
│   ├── Get Customer Details
│   ├── Update Customer
│   └── Customer Order History
├── Customer Analytics/
│   ├── Customer Metrics
│   ├── Customer Behavior
│   └── Customer Segmentation
└── Addresses/
    ├── List Customer Addresses
    └── Update Address
```

#### **🛡️ Content Moderator Collection**

```
├── Reviews/
│   ├── List Reviews (Pending)
│   ├── Approve Review
│   ├── Reject Review
│   ├── Delete Review
│   └── Review Analytics
└── Users/
    ├── List Users
    ├── Update User Status
    └── User Activity
```

### **3. Environment Variables**

```json
{
  "base_url": "http://localhost:8000/api/admin",
  "auth_token": "{{admin_access_token}}",
  "refresh_token": "{{admin_refresh_token}}",
  "user_role": "{{current_user_role}}",
  "user_id": "{{current_user_id}}"
}
```

### **4. Pre-request Scripts (Global)**

```javascript
// Auto-refresh token if expired
if (pm.globals.get("token_expires_at") < Date.now()) {
    // Refresh token logic
}

// Add role-based headers
pm.request.headers.add({
    key: "X-User-Role",
    value: pm.environment.get("user_role")
});
```

### **5. Test Scripts (Per Collection)**

```javascript
// Role-based response validation
pm.test("User has required permissions", function () {
    const response = pm.response.json();
    pm.expect(response.meta.user_permissions).to.include("products.view");
});

// Status code validation
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// Response structure validation
pm.test("Response has required structure", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property("success");
    pm.expect(response).to.have.property("data");
    pm.expect(response).to.have.property("meta");
});
```
