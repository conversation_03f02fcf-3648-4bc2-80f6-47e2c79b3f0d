---
description: Routing rules for TanStack Router
globs:
  - src/routes/**/*.tsx
  - src/pages/**
alwaysApply: true
---

### Routing rules (TanStack Router)

Do
- Define routes under `src/routes/*` using file-based routing. Export `Route` via `createFileRoute()` or `createRootRoute()`.
- Compose global providers/layout in `routes/__root.tsx` only.
- Use `AuthGuard` for protected pages; prefer declarative guards in route components.
- Co-locate page UI in `src/pages/...` and import into route files.
- Use `Link`/`router.navigate` with typed `to` targets; avoid hardcoded string paths when types are available.
- Keep route loaders light; prefer TanStack Query in components/hooks for caching.

Avoid
- Editing `src/routeTree.gen.ts`.
- Duplicating layout providers across child routes; lift to `__root`.
- Mutating global state in route files; use stores/hooks.

Patterns
- Public route:
```ts
// src/routes/login.tsx
export const Route = createFileRoute('/login')({ component: LoginPage })
```
- Protected usage:
```tsx
<AuthGuard permission="staff.view_productproxy">
  <ProductsPage />
</AuthGuard>
```

Reference
- TanStack Router overview — https://tanstack.com/router/latest/docs/framework/react/overview
