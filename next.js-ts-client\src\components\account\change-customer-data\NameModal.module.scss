@use '../../../scss/variables' as * ;
@use '../../../scss/mixins' as * ;

.modal_content {
  background-color: #fff;
  padding: 2rem;
  border-radius: $border-radius-2;
  max-width: 500px;
  width: 100%;

  h3 {
    font-size: $font-size-4;
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
  }

  form {
    @include flexbox(flex-start, stretch, column);
    width: 100%;

    > div:not(.phoneInputContainer) {
      margin-bottom: 1rem;

      label {
        font-weight: bold;
        color: $primary-dark-text-color;
        display: block;
        margin-bottom: 0.5rem;
      }

      input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid $primary-lighter-text-color;
        border-radius: 3px;

        &:focus {
          outline: 2px solid $lighten-blue;
          border: none;
        }
      }

      p {
        color: $error-red;
        font-size: 14px;
        margin-top: 0.5rem;
        text-align: center;
      }
    }
  }
}

.modal_buttons {
  @include flexbox(space-between, center);
  margin-top: 1rem;

  button {
    @include btn($primary-blue, #fff);
    padding: .5rem 1rem;
    border: 1px solid $lighten-blue;
    transition: all 0.3s ease;

    &:hover {
      border: 1px solid $primary-dark-blue;
      color: $primary-dark-blue;
    }
  }
}

.phoneInputContainer {
  @include flexbox(flex-start, center);
  /* Ensure the flag and input are vertically aligned */
}

.phoneInputFlag {
  @include flexbox(center, center);
  height: 100%;
  /* Ensure the flag dropdown takes full height of the input */
}

.phoneInputField {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  box-sizing: border-box;
  /* Ensures padding doesn't affect width */
}
