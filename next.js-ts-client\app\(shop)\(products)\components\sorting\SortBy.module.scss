@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.sorting {
  @include flexbox(flex-end, center);
  
  select {
    color: $primary-dark-text-color;
    padding: $padding-2 $padding-4;
    border: 2px solid $sky-light-blue;
    box-shadow: $box-shadow-1;
    border-radius: $border-radius-2;
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    font-size: $font-size-2;
    font-weight: map-get($font-weight, 'medium');
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;

    &:hover {
      border-color: $primary-blue;
      box-shadow: $box-shadow-blue-1;
      background: white;
    }

    &:focus {
      outline: none;
      border-color: $primary-blue;
      box-shadow: $box-shadow-blue-1;
    }

    option {
      color: $primary-dark-text-color;
      background: white;
      padding: $padding-1;
      
      &:hover {
        background: $sky-lighter-blue;
      }
    }
  }
}

// Mobile responsive
@media (max-width: $tablet) {
  .sorting {
    @include flexbox(center, center);
    
    select {
      width: 100%;
      min-width: auto;
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .sorting select {
    transition: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sorting select {
    border: 2px solid black;
    background: white;
    
    &:hover,
    &:focus {
      border: 3px solid black;
    }
  }
}
