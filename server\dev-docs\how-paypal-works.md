# PayPal Payment Integration Guide

This guide explains how the PayPal payment system works in this e-commerce project, covering both frontend and backend
interactions.

## Overview

The PayPal payment flow consists of these main steps:

1. Order Creation in PayPal
2. Payment Authorization by Customer
3. Payment Capture
4. Order Status Update

### 1. Frontend Implementation

#### Initial Setup

- The payment interface is rendered in `Order.tsx` component
- PayPal buttons are displayed when `order.payment_method.slug === 'paypal'`
- The `PayPalCheckout.tsx` component handles PayPal-specific logic

#### General Component Structure of `'react-paypal-js'`

```typescript
<PayPalProvider> // PayPalScriptProvider
  <PayPalButtons
    createOrder={createOrder}
    onApprove={onApprove}
    onError={handleError}
  />
</PayPalProvider>
```

### 2. Backend Endpoints

#### The following API endpoints handle PayPal operations

```python
urlpatterns = [
    path('create-paypal-order/', CreatePayPalOrderView.as_view()),
    path('capture-paypal-order/', CapturePayPalOrderView.as_view()),
    path('paypal/webhook/', PayPalWebhookView.as_view()),
]
```

### 3. Payment Flow Steps

#### Step 1: Create PayPal Order

- **Frontend** : User clicks PayPal button
- **Backend** :
  - `CreatePayPalOrderView` receives request with order_id
  - Creates PayPal order via PayPal API
  - Stores PayPal order details in database (with `PayPalOrder`)
  - Returns PayPal order ID (`paypal_order_id`) to frontend

#### Step 2: User Authorization

- PayPal popup opens
- User logs into PayPal account
- User reviews and approves payment

#### Step 3: Payment Capture

- **Frontend** : `onApprove` callback triggered
- **Backend** :
  - `CapturePayPalOrderView` receives PayPal order ID
  - Captures payment through PayPal API
  - Updates order status to "Paid"
  - Returns success response

### 4. Data Models

```python
class PayPalOrder(models.Model):
    order = models.OneToOneField('order.Order', on_delete=models.CASCADE)
    paypal_order_id = models.CharField(max_length=100, unique=True)
    status = models.CharField(max_length=20, default='CREATED')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### 5. Error Handling

- Frontend displays error alerts using `Alert` component
- Backend returns appropriate HTTP status codes and error messages
- Transaction rollback on failure using `transaction.atomic()`

### 6. Webhook Integration (Not actively used)

- PayPal sends webhook notifications for payment events
- `PayPalWebhookView` handles incoming webhooks
- Verifies webhook signature
- Updates order status based on event type

## Configuration

```bash
PAYPAL_CLIENT_ID = getenv('PAYPAL_CLIENT_ID')
PAYPAL_CLIENT_SECRET = getenv('PAYPAL_CLIENT_SECRET')
PAYPAL_BASE_URL = getenv('PAYPAL_BASE_URL')
PAYPAL_WEBHOOK_ID = getenv('PAYPAL_WEBHOOK_ID')
```

## Testing

1. Set up PayPal sandbox account
2. Configure webhook endpoints using ngrok for local testing
3. Use PayPal sandbox credentials
4. Test various payment scenarios (success, failure, cancellation)

## Common Issues

1. Webhook verification failures
   - Check webhook signature configuration
   - Verify webhook ID in settings
2. Payment status not updating
   - Check transaction atomic blocks
   - Verify webhook reception
   - Check database locks

## Resources

- [PayPal API Documentation](https://developer.paypal.com/docs/api/overview/)
- [PayPal React Components](https://paypal.github.io/react-paypal-js/)
- [Django PayPal Integration](https://developer.paypal.com/docs/checkout/standard/integrate/)
