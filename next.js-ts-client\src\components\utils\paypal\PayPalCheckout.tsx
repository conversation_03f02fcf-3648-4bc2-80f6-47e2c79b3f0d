import { PayPalButtons } from "@paypal/react-paypal-js"
import Alert from "../alert/Alert"
import PayPalProvider from "./PayPalProvider"
import { useQueryClient } from "@tanstack/react-query"
import { getErrorMessage } from "../getErrorMessage"
import { AxiosError } from "axios"
import { ErrorResponse } from "../../../types/types"
import { useCaptureOrder, useCreatePayPalOrder } from "@/src/hooks/checkout-hooks"
import { CACHE_KEY_ORDER_ITEMS } from "@/src/constants/constants"


interface Props {
  orderId: number
  amount: number
}

const PayPalCheckout = ({ orderId, amount }: Props) => {
  const queryClient = useQueryClient()

  const createPayPalOrder = useCreatePayPalOrder({ orderId, amount })
  const captureOrder = useCaptureOrder()

  return (
    <PayPalProvider>
      <div>
        {createPayPalOrder.isError &&
          <Alert variant="error" message={getErrorMessage(createPayPalOrder.error as AxiosError<ErrorResponse>)} />}
        {captureOrder.isError &&
          <Alert variant="error" message={getErrorMessage(captureOrder.error as AxiosError<ErrorResponse>)} />}

        <PayPalButtons
          createOrder={async () => {
            try {
              const response = await createPayPalOrder.mutateAsync()
              return response.id
            } catch (error) {
              console.error('Error creating PayPal order:', error)
              throw error
            }
          }}
          onApprove={async (data) => {
            try {
              const result = await captureOrder.mutateAsync({
                paypal_order_id: data.orderID
              })
              if (result.status === 'COMPLETED') {
                queryClient.invalidateQueries({ queryKey: [CACHE_KEY_ORDER_ITEMS] })
              }
            } catch (error) {
              console.error('Error capturing order:', error)
              throw error
            }
          }}
        />
      </div>
    </PayPalProvider>
  )
}

export default PayPalCheckout
