import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface OrderStoreShape {
  orderId: number | null
  setOrderId: (id: number | null) => void
  // Define other state properties and actions if you uncomment the code for order and customer
  // order: Order;
  // setOrder: (newOrder: Partial<Order>) => void;
  // clearOrder: () => void;
  // customer: Customer;
}

const orderStore = create<OrderStoreShape>()(
  persist(
    (set) => ({
      // order: {
      //   orderedProducts: [],
      //   subtotal: 0
      // },
      // setOrder: (newOrder) => set((state) => ({ order: { ...state.order, ...newOrder } })),
      // clearOrder: () => set({ order: { orderedProducts: [], subtotal: 0 } }),
      // customer: {},
      orderId: null,
      setOrderId: (id) => set({ orderId: id }),
    }),
    {
      name: 'order_store',
      // storage: createJSONStorage(() => sessionStorage) // Specify the storage engine (localStorage or sessionStorage)
    }
  )
)

export default orderStore

