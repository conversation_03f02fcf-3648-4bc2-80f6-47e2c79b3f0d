// Loading spinner component for async operations
// Provides consistent loading states across the application

import React from 'react'
import styles from './LoadingSpinner.module.scss'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = '',
}) => {
  const spinnerClasses = [
    styles.spinner,
    styles[size],
    styles[color],
    className,
  ]
    .filter(Boolean)
    .join(' ')

  return (
    <div className={spinnerClasses} role="status" aria-label="Loading">
      <div className={styles.circle}></div>
    </div>
  )
}

interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  message?: string
  className?: string
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  message = 'Loading...',
  className = '',
}) => {
  return (
    <div className={`${styles.container} ${className}`}>
      {children}
      {isLoading && (
        <div className={styles.overlay}>
          <div className={styles.overlayContent}>
            <LoadingSpinner size="lg" color="white" />
            {message && <p className={styles.message}>{message}</p>}
          </div>
        </div>
      )}
    </div>
  )
}

interface PageLoadingProps {
  message?: string
  className?: string
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = 'Loading...',
  className = '',
}) => {
  return (
    <div className={`${styles.pageLoading} ${className}`}>
      <LoadingSpinner size="xl" />
      <p className={styles.pageMessage}>{message}</p>
    </div>
  )
}

interface ButtonLoadingProps {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  isLoading,
  children,
  loadingText,
}) => {
  if (isLoading) {
    return (
      <span className={styles.buttonLoading}>
        <LoadingSpinner size="sm" color="white" />
        {loadingText && <span className={styles.buttonText}>{loadingText}</span>}
      </span>
    )
  }

  return <>{children}</>
}
