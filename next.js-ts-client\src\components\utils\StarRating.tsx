import { FaStar, FaRegStar, FaStarHalfAlt } from "react-icons/fa"

interface Props {
  rating: number
  color?: string
}

const StarRating = ({ rating, color }: Props) => {
  return (
    rating < 2 ? (
      <div>
        <i><FaStar style={{ color }} /></i>
        <i><FaStarHalfAlt style={{ color }} /></i>
        <i><FaRegStar style={{ color }} /></i>
        <i><FaRegStar style={{ color }} /></i>
        <i><FaRegStar style={{ color }} /></i>
      </div>
    ) : rating < 4 ? (
      <div>
        <i><FaStar style={{ color }} /></i>
        <i><FaStar style={{ color }} /></i>
        <i><FaStarHalfAlt style={{ color }} /></i>
        <i><FaRegStar style={{ color }} /></i>
        <i><FaRegStar style={{ color }} /></i>
      </div>
    ) : (
      <div>
        <i><FaStar style={{ color }} /></i>
        <i><FaStar style={{ color }} /></i>
        <i><FaStar style={{ color }} /></i>
        <i><FaStar style={{ color }} /></i>
        <i><FaStarHalfAlt style={{ color }} /></i>
      </div>
    )
  )
}

export default StarRating