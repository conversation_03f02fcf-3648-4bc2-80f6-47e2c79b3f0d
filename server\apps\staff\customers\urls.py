from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from . import views

app_name = 'staff_customers'

# Create router and register viewsets
router = DefaultRouter()

# Customer management
router.register(r'customers', views.CustomerStaffViewSet, basename='customers')
router.register(r'addresses', views.AddressStaffViewSet, basename='addresses')

urlpatterns = [
    path('', include(router.urls)),
]
