// Edit product route
// Protected route for editing existing products

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../../components/auth/AuthGuard'
import { EditProductSections } from '../../../pages/products/EditProductSections'

export const Route = createFileRoute('/products/$productId/edit')({
  component: EditProductRoute,
})

function EditProductRoute() {
  return (
    <AuthGuard permission="staff.change_productproxy">
      <EditProductSections />
    </AuthGuard>
  )
}
