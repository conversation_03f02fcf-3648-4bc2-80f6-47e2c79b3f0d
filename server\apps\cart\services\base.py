"""
Base Service Class

Provides common functionality and error handling patterns for all cart services.
"""

import logging
from typing import Dict, Any, Optional
from django.core.exceptions import ValidationError
from django.db import DatabaseError, transaction


class BaseCartService:
    """Base class for cart services with consistent error handling"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__module__)
    
    def handle_service_error(self, operation: str, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Standardized error handling for service operations
        
        Args:
            operation: Description of the operation that failed
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            Standardized error response dictionary
        """
        error_context = context or {}
        error_message = str(error)
        
        # Log the error with context
        self.logger.error(
            f"Service operation failed: {operation}. Error: {error_message}. Context: {error_context}"
        )
        
        # Determine error type and create appropriate response
        if isinstance(error, ValidationError):
            return {
                'success': False,
                'error_type': 'validation_error',
                'error': error_message,
                'message': f'Validation failed for {operation}',
                'context': error_context
            }
        elif isinstance(error, DatabaseError):
            return {
                'success': False,
                'error_type': 'database_error',
                'error': 'Database operation failed',
                'message': f'Database error during {operation}',
                'context': error_context
            }
        else:
            return {
                'success': False,
                'error_type': 'service_error',
                'error': error_message,
                'message': f'Service error during {operation}',
                'context': error_context
            }
    
    def execute_with_error_handling(self, operation: str, func, *args, **kwargs) -> Dict[str, Any]:
        """
        Execute a function with standardized error handling
        
        Args:
            operation: Description of the operation
            func: Function to execute
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result dictionary with success/error information
        """
        try:
            result = func(*args, **kwargs)
            
            # If result is already a dict with success key, return as-is
            if isinstance(result, dict) and 'success' in result:
                return result
            
            # Otherwise, wrap in success response
            return {
                'success': True,
                'result': result,
                'message': f'{operation} completed successfully'
            }
            
        except Exception as e:
            return self.handle_service_error(operation, e, {
                'function': func.__name__,
                'args': str(args)[:200],  # Limit args length for logging
                'kwargs': str(kwargs)[:200]
            })
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: list) -> Dict[str, Any]:
        """
        Validate that required fields are present in data
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            
        Returns:
            Validation result dictionary
        """
        missing_fields = []
        
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            return {
                'valid': False,
                'errors': [f'Missing required field: {field}' for field in missing_fields],
                'missing_fields': missing_fields
            }
        
        return {
            'valid': True,
            'message': 'All required fields are present'
        }
    
    def log_operation(self, operation: str, context: Optional[Dict[str, Any]] = None, level: str = 'info'):
        """
        Log service operations with consistent format
        
        Args:
            operation: Description of the operation
            context: Additional context information
            level: Log level (info, warning, error, debug)
        """
        log_message = f"Service operation: {operation}"
        if context:
            log_message += f". Context: {context}"
        
        log_method = getattr(self.logger, level, self.logger.info)
        log_method(log_message)
    
    def create_success_response(self, message: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create standardized success response
        
        Args:
            message: Success message
            data: Optional data to include in response
            
        Returns:
            Standardized success response dictionary
        """
        response = {
            'success': True,
            'message': message
        }
        
        if data:
            response.update(data)
        
        return response
    
    def create_error_response(self, message: str, error: Optional[str] = None, 
                            error_type: str = 'service_error') -> Dict[str, Any]:
        """
        Create standardized error response
        
        Args:
            message: Error message
            error: Detailed error information
            error_type: Type of error
            
        Returns:
            Standardized error response dictionary
        """
        response = {
            'success': False,
            'message': message,
            'error_type': error_type
        }
        
        if error:
            response['error'] = error
        
        return response


class ServiceTransaction:
    """Context manager for service operations with transaction support"""
    
    def __init__(self, service: BaseCartService, operation: str):
        self.service = service
        self.operation = operation
        self.transaction = None
    
    def __enter__(self):
        self.service.log_operation(f"Starting {self.operation}")
        self.transaction = transaction.atomic()
        self.transaction.__enter__()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            if exc_type is None:
                self.service.log_operation(f"Completed {self.operation}")
            else:
                self.service.log_operation(
                    f"Failed {self.operation}: {exc_val}", 
                    level='error'
                )
        finally:
            self.transaction.__exit__(exc_type, exc_val, exc_tb)
