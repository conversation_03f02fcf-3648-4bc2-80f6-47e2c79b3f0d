// Badge component for status indicators and labels
// Provides consistent styling for various badge types

import React from 'react'
import styles from './Badge.module.scss'

interface BadgeProps {
  children: React.ReactNode
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  dot?: boolean
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  dot = false,
}) => {
  const badgeClasses = [
    styles.badge,
    styles[variant],
    styles[size],
    dot && styles.dot,
    className,
  ]
    .filter(Boolean)
    .join(' ')

  return <span className={badgeClasses}>{children}</span>
}

// Status-specific badge components for common use cases
interface StatusBadgeProps {
  status: string
  className?: string
}

export const OrderStatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className = '',
}) => {
  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'warning'
      case 'processing':
        return 'info'
      case 'shipped':
        return 'primary'
      case 'delivered':
        return 'success'
      case 'cancelled':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      {status}
    </Badge>
  )
}

export const ProductStatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className = '',
}) => {
  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'error'
      case 'draft':
        return 'warning'
      case 'archived':
        return 'secondary'
      default:
        return 'default'
    }
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      {status}
    </Badge>
  )
}

export const StockStatusBadge: React.FC<{ quantity: number; className?: string }> = ({
  quantity,
  className = '',
}) => {
  const getVariant = (qty: number) => {
    if (qty === 0) return 'error'
    if (qty < 10) return 'warning'
    return 'success'
  }

  const getLabel = (qty: number) => {
    if (qty === 0) return 'Out of Stock'
    if (qty < 10) return 'Low Stock'
    return 'In Stock'
  }

  return (
    <Badge variant={getVariant(quantity)} className={className}>
      {getLabel(quantity)}
    </Badge>
  )
}
