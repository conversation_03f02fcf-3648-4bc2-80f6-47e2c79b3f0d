"""
Cart Services Package

This package contains service layer classes that encapsulate business logic
for cart operations, following the service layer pattern.

Services:
- CartCalculationService: Handles all cart-related calculations
- CartSelectionService: Manages cart item selection operations
- CartValidationService: Validates cart operations and business rules
- CartShippingService: Integrates with shipping services for selected items
"""

from .base import BaseCartService, ServiceTransaction
from .cart_service import CartCalculationService
from .selection_service import CartSelectionService
from .validation_service import CartValidationService
from .shipping_service import CartShippingService

__all__ = [
    'BaseCartService',
    'ServiceTransaction',
    'CartCalculationService',
    'CartSelectionService',
    'CartValidationService',
    'CartShippingService',
]
