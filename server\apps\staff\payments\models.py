from django.db import models
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from apps.payments.models import PaymentOption, PayPalOrder


class PaymentOptionProxy(PaymentOption):
    """
    Proxy model for staff-specific payment option operations
    Provides additional methods and behavior for staff payment management
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Payment Option"
        verbose_name_plural = "Staff Payment Options"
        permissions = [
            ("manage_payment_options", "Can manage payment options"),
            ("payment_analytics", "Can view payment analytics"),
            ("payment_monitoring", "Can monitor payment transactions"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'is_active': self.is_active,
            'usage_count': self.get_usage_count(),
            'success_rate': self.get_success_rate(),
            'total_processed': self.get_total_processed(),
        }

    def get_usage_count(self):
        """Get number of orders using this payment method"""
        from apps.order.models import Order
        return Order.objects.filter(payment_method=self).count()

    def get_success_rate(self):
        """Get success rate for this payment method"""
        from apps.order.models import Order
        total_orders = Order.objects.filter(payment_method=self).count()
        if total_orders == 0:
            return 0.0
        
        successful_orders = Order.objects.filter(
            payment_method=self,
            payment_status='Paid'
        ).count()
        
        return (successful_orders / total_orders) * 100

    def get_total_processed(self):
        """Get total amount processed through this payment method"""
        from apps.order.models import Order
        total = Order.objects.filter(
            payment_method=self,
            payment_status='Paid'
        ).aggregate(total=Sum('total'))['total']
        
        return total or 0

    def get_recent_transactions(self, days=30):
        """Get recent transactions for this payment method"""
        from apps.order.models import Order
        cutoff_date = timezone.now() - timedelta(days=days)
        
        return Order.objects.filter(
            payment_method=self,
            placed_at__gte=cutoff_date
        ).order_by('-placed_at')[:20]


class PayPalOrderProxy(PayPalOrder):
    """
    Proxy model for staff-specific PayPal order operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff PayPal Order"
        verbose_name_plural = "Staff PayPal Orders"
        permissions = [
            ("view_paypal_transactions", "Can view PayPal transactions"),
            ("manage_paypal_disputes", "Can manage PayPal disputes"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'order_id': self.order.id,
            'paypal_order_id': self.paypal_order_id,
            'status': self.status,
            'amount': float(self.order.total) if self.order else 0,
            'customer_email': self.order.customer.user.email if self.order and self.order.customer else None,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'processing_time': self.get_processing_time(),
        }

    def get_processing_time(self):
        """Get processing time in seconds"""
        if self.status == 'COMPLETED':
            return (self.updated_at - self.created_at).total_seconds()
        return None

    def is_disputed(self):
        """Check if this transaction has any disputes"""
        # This would integrate with PayPal's dispute API
        # For now, return False as placeholder
        return False

    def get_dispute_info(self):
        """Get dispute information if any"""
        # This would integrate with PayPal's dispute API
        return {
            'has_dispute': False,
            'dispute_status': None,
            'dispute_reason': None,
            'dispute_amount': None,
        }


class PaymentTransactionAudit(models.Model):
    """
    Model to track payment transaction audits and monitoring
    """
    transaction_id = models.CharField(max_length=255, db_index=True)
    payment_method = models.CharField(max_length=50)
    order_id = models.IntegerField()
    customer_id = models.IntegerField(null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=50)
    gateway_response = models.JSONField(default=dict)
    staff_user = models.ForeignKey(
        'core.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='payment_audits'
    )
    action = models.CharField(max_length=100)  # 'created', 'updated', 'refunded', etc.
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Payment Transaction Audit"
        verbose_name_plural = "Payment Transaction Audits"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['transaction_id']),
            models.Index(fields=['payment_method']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Audit: {self.transaction_id} - {self.action}"


class PaymentDispute(models.Model):
    """
    Model to track payment disputes and chargebacks
    """
    DISPUTE_TYPES = [
        ('CHARGEBACK', 'Chargeback'),
        ('INQUIRY', 'Inquiry'),
        ('CLAIM', 'Claim'),
        ('REFUND_REQUEST', 'Refund Request'),
    ]

    DISPUTE_STATUS = [
        ('OPEN', 'Open'),
        ('UNDER_REVIEW', 'Under Review'),
        ('RESOLVED', 'Resolved'),
        ('CLOSED', 'Closed'),
    ]

    dispute_id = models.CharField(max_length=255, unique=True)
    order = models.ForeignKey('order.Order', on_delete=models.CASCADE, related_name='payment_disputes')
    dispute_type = models.CharField(max_length=20, choices=DISPUTE_TYPES)
    status = models.CharField(max_length=20, choices=DISPUTE_STATUS, default='OPEN')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField()
    gateway_data = models.JSONField(default=dict)
    assigned_to = models.ForeignKey(
        'core.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_disputes'
    )
    resolution_notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Payment Dispute"
        verbose_name_plural = "Payment Disputes"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dispute_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Dispute {self.dispute_id} - {self.get_dispute_type_display()}"

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'dispute_id': self.dispute_id,
            'order_id': self.order.id,
            'customer_email': self.order.customer.user.email if self.order.customer else None,
            'dispute_type': self.dispute_type,
            'status': self.status,
            'amount': float(self.amount),
            'reason': self.reason,
            'assigned_to': self.assigned_to.email if self.assigned_to else None,
            'created_at': self.created_at,
            'days_open': (timezone.now() - self.created_at).days,
        }
