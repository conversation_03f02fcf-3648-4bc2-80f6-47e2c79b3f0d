@use '../../../scss/variables' as *;

.alert {
  width: fit-content;
  min-width: 200px;
  margin: 1rem auto 1rem auto;
  padding: 10px 15px;
  border-radius: 3px;

  p::first-letter {
    text-transform: uppercase;
  }
}

.info {
  background-color: $info-bg;
  color: $info-text;
  border: 1px solid #9cceff;
}

.warning {
  background-color: $warning-bg;
  color: $warning-text;
  border: 1px solid #ffd344;
}

.error {
  background-color: $error-bg;
  color: $error-text;
  border: 1px solid #ff9fa7;
}

.success {
  background-color: $success-bg;
  color: $success-text;
  border: 1px solid #79fc7d;
}

.highlight {
  font-weight: bold;
}