// Error boundary component for handling React errors
// Provides fallback UI and error reporting

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { FiAlertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi'
import { Button } from '../../ui/Button'
import { Card } from '../../ui/Card'
import styles from './ErrorBoundary.module.scss'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Log error to console in development
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }

    // In production, you might want to send this to an error reporting service
    // Example: Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className={styles.container}>
          <Card className={styles.card} padding="lg">
            <div className={styles.content}>
              <div className={styles.icon}>
                <FiAlertTriangle />
              </div>
              
              <h1 className={styles.title}>Something went wrong</h1>
              
              <p className={styles.message}>
                An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
              </p>
              
              {import.meta.env.DEV && this.state.error && (
                <details className={styles.errorDetails}>
                  <summary className={styles.errorSummary}>
                    Error Details (Development Only)
                  </summary>
                  <div className={styles.errorContent}>
                    <h3>Error:</h3>
                    <pre className={styles.errorText}>
                      {this.state.error.toString()}
                    </pre>
                    
                    {this.state.errorInfo && (
                      <>
                        <h3>Component Stack:</h3>
                        <pre className={styles.errorText}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </>
                    )}
                  </div>
                </details>
              )}
              
              <div className={styles.actions}>
                <Button
                  variant="primary"
                  onClick={this.handleRetry}
                  className={styles.retryButton}
                >
                  <FiRefreshCw />
                  Try Again
                </Button>
                
                <Button
                  variant="outline"
                  onClick={this.handleGoHome}
                  className={styles.homeButton}
                >
                  <FiHome />
                  Go Home
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Functional component wrapper for easier usage
interface ErrorFallbackProps {
  error: Error
  resetError: () => void
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
}) => {
  return (
    <div className={styles.container}>
      <Card className={styles.card} padding="lg">
        <div className={styles.content}>
          <div className={styles.icon}>
            <FiAlertTriangle />
          </div>
          
          <h1 className={styles.title}>Something went wrong</h1>
          
          <p className={styles.message}>
            {error.message || 'An unexpected error occurred.'}
          </p>
          
          <div className={styles.actions}>
            <Button
              variant="primary"
              onClick={resetError}
              className={styles.retryButton}
            >
              <FiRefreshCw />
              Try Again
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
