// Authentication guard component for protecting routes
// Handles authentication and authorization checks

import React from 'react'
import { Navigate, useLocation } from '@tanstack/react-router'
import { useAuth } from '../../../hooks/use-auth'
import { PageLoading } from '../../ui/LoadingSpinner'
import { UnauthorizedPage } from '../../../pages/auth/UnauthorizedPage'

interface AuthGuardProps {
  children: React.ReactNode
  permission?: string
  group?: string
  requireAuth?: boolean
  fallback?: React.ReactNode
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  permission,
  group,
  requireAuth = true,
  fallback,
}) => {
  const location = useLocation()
  const {
    isAuthenticated,
    isLoading,
    user,
    checkPermission,
    hasGroup,
  } = useAuth()

  // Show loading while checking authentication (prevents premature redirects)
  if (isLoading) {
    return <PageLoading message="Checking authentication..." />
  }

  // Only redirect to login if authentication is required, user is not authenticated, and we're not loading
  if (requireAuth && !isAuthenticated && !isLoading) {
    return (
      <Navigate
        to="/login"
        search={{ redirect: location.pathname }}
        replace
      />
    )
  }

  // If authenticated, check permissions
  if (isAuthenticated && user) {
    // Superuser bypasses all permission checks
    if (user.is_superuser) {
      return <>{children}</>
    }

    // Check specific permission
    if (permission && !checkPermission(permission)) {
      return fallback || <UnauthorizedPage />
    }

    // Check group membership
    if (group && !hasGroup(group)) {
      return fallback || <UnauthorizedPage />
    }
  }

  return <>{children}</>
}

interface PermissionGuardProps {
  children: React.ReactNode
  permission: string
  fallback?: React.ReactNode
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
}) => {
  const { user, checkPermission } = useAuth()

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>
  }

  // Check permission
  if (!checkPermission(permission)) {
    return fallback || null
  }

  return <>{children}</>
}

interface GroupGuardProps {
  children: React.ReactNode
  group: string
  fallback?: React.ReactNode
}

export const GroupGuard: React.FC<GroupGuardProps> = ({
  children,
  group,
  fallback,
}) => {
  const { user, hasGroup } = useAuth()

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>
  }

  // Check group membership
  if (!hasGroup(group)) {
    return fallback || null
  }

  return <>{children}</>
}

interface RoleGuardProps {
  children: React.ReactNode
  roles: string[]
  requireAll?: boolean
  fallback?: React.ReactNode
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  roles,
  requireAll = false,
  fallback,
}) => {
  const { user, hasGroup } = useAuth()

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>
  }

  // Check role requirements
  const hasRequiredRoles = requireAll
    ? roles.every(role => hasGroup(role))
    : roles.some(role => hasGroup(role))

  if (!hasRequiredRoles) {
    return fallback || null
  }

  return <>{children}</>
}
