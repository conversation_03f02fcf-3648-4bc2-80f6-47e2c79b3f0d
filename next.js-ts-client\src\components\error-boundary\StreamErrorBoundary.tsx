'use client'

import React from 'react'

interface StreamErrorBoundaryProps {
  children: React.ReactNode
}

interface StreamErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class StreamErrorBoundary extends React.Component<StreamErrorBoundaryProps, StreamErrorBoundaryState> {
  constructor(props: StreamErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): StreamErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log stream controller errors specifically
    if (error.message.includes('transformAlgorithm') || error.message.includes('controller')) {
      console.group('🔴 Stream Controller Error Debug')
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
      console.error('Error name:', error.name)
      console.error('Component stack:', errorInfo.componentStack)
      console.error('Timestamp:', new Date().toISOString())
      console.error('User Agent:', navigator.userAgent)
      console.error('Next.js Version: 15.5.0')
      console.error('React Version: 19.1.0')
      console.groupEnd()

      // Report this error for tracking
      if (typeof window !== 'undefined') {
        console.warn('🚨 This appears to be a Next.js 15 streaming compatibility issue. Consider reporting to Next.js team.')
      }
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          border: '1px solid #ff6b6b',
          borderRadius: '4px',
          backgroundColor: '#ffe0e0',
          margin: '10px 0'
        }}>
          <h3>Something went wrong</h3>
          <p>A stream controller error occurred. Please check the console for details.</p>
          <button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            style={{
              padding: '8px 16px',
              backgroundColor: '#ff6b6b',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}

export default StreamErrorBoundary