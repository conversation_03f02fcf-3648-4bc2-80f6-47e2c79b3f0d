@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.cart {
  // background-color: red;
  h2 {
    margin: 1rem;
    font-weight: bold;
    font-size: 25px;
  }

  &__cart_items {
    margin: 1rem 0 0 0;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;

    @media (max-width: $tablet) {
      grid-template-columns: 1fr;
    }
  }

  &__summaries {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 300px;

    @media (max-width: $tablet) {
      min-width: 100%;
    }
  }

  // .cart__cart_items {
  //   display: grid;
  //   grid-template-columns: 1fr;
  // }
}

@media (min-width: $tablet) {
  .cart__cart_items {
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
  }
}
