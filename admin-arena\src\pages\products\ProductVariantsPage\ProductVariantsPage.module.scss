@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.backButton {
  @include flex-center;
  gap: $spacing-2;
  color: $gray-600;
  text-decoration: none;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: $primary-600;
  }
}

.titleSection {
  flex: 1;

  .title {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
  }

  .subtitle {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-sm;
  }

  .productName {
    color: $primary-600;
    font-weight: $font-weight-medium;
  }
}

.actions {
  @include flex-start;
  gap: $spacing-3;
}

.createButton {
  @include flex-center;
  gap: $spacing-2;
}

.variantsCard {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

.cardHeader {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;
  margin-bottom: $spacing-4;

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.variantsSummary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-6;
}

.summaryCard {
  @include card;
  padding: $spacing-4;
  text-align: center;

  .summaryValue {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $primary-600;
    margin: 0 0 $spacing-1 0;
  }

  .summaryLabel {
    font-size: $font-size-sm;
    color: $gray-600;
    margin: 0;
  }
}

// Table cell styles
.skuCell {
  @include flex-start;
  gap: $spacing-3;
  align-items: center;

  .skuIcon {
    @include flex-center;
    width: $spacing-8;
    height: $spacing-8;
    background: $primary-50;
    color: $primary-600;
    border-radius: $border-radius;
    font-size: $font-size-sm;
  }

  .skuInfo {
    .skuCode {
      font-weight: $font-weight-medium;
      color: $gray-900;
      font-family: $font-family-mono;
      font-size: $font-size-sm;
    }

    .skuLabel {
      font-size: $font-size-xs;
      color: $gray-500;
      margin-top: $spacing-0-5;
    }
  }
}

.priceCell {
  @include flex-column;
  gap: $spacing-1;

  .currentPrice {
    font-weight: $font-weight-semibold;
    color: $gray-900;
    font-size: $font-size-base;
  }

  .comparePrice {
    font-size: $font-size-sm;
    color: $gray-500;
    text-decoration: line-through;
  }

  .discount {
    font-size: $font-size-xs;
    color: $success-600;
    font-weight: $font-weight-medium;
  }
}

.stockCell {
  @include flex-start;
  gap: $spacing-2;
  align-items: center;

  .stockNumber {
    font-weight: $font-weight-medium;
    color: $gray-900;
  }
}

.attributesCell {
  @include flex-start;
  gap: $spacing-2;
  flex-wrap: wrap;
}

.attributeTag {
  padding: $spacing-1 $spacing-2;
  background: $gray-100;
  color: $gray-700;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;

  .attributeName {
    color: $gray-500;
  }

  .attributeValue {
    color: $gray-900;
  }
}

.actionsCell {
  @include flex-start;
  gap: $spacing-1;
}

.actionButton {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  border: none;
  background: none;
  color: $gray-500;
  cursor: pointer;
  border-radius: $border-radius;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: $gray-100;
    color: $gray-700;
  }

  &.editButton {
    &:hover {
      background-color: $primary-50;
      color: $primary-600;
    }
  }

  &.deleteButton {
    &:hover {
      background-color: $error-50;
      color: $error-600;
    }
  }
}

// Empty state
.emptyState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-500;
  text-align: center;

  svg {
    font-size: $font-size-4xl;
    color: $gray-300;
  }

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    margin: 0;
    line-height: $line-height-relaxed;
  }
}

// Loading state
.loading {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-600;

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

// Delete confirmation modal
.deleteModal {
  .deleteContent {
    @include flex-column;
    gap: $spacing-4;
    text-align: center;

    .deleteIcon {
      @include flex-center;
      width: $spacing-16;
      height: $spacing-16;
      background: $error-100;
      color: $error-600;
      border-radius: $border-radius-full;
      font-size: $font-size-2xl;
      margin: 0 auto;
    }

    .deleteTitle {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $gray-900;
      margin: 0;
    }

    .deleteMessage {
      color: $gray-600;
      font-size: $font-size-sm;
      margin: 0;
      line-height: $line-height-relaxed;

      .variantSku {
        font-weight: $font-weight-medium;
        color: $gray-900;
        font-family: $font-family-mono;
      }
    }

    .deleteActions {
      @include flex-center;
      gap: $spacing-3;
      margin-top: $spacing-2;
    }
  }
}

// Bulk actions
.bulkActions {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;
  padding: $spacing-3 $spacing-4;
  background: $primary-25;
  border: 1px solid $primary-200;
  border-radius: $border-radius;
  margin-bottom: $spacing-4;

  .bulkInfo {
    @include flex-start;
    gap: $spacing-2;
    align-items: center;
    color: $primary-700;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  .bulkButtons {
    @include flex-start;
    gap: $spacing-2;
  }
}
