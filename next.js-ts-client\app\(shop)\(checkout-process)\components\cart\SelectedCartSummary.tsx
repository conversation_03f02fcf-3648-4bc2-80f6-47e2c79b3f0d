'use client'

import React from 'react'
import cartStore from '@/src/stores/cart-store'
import { CartItemShape } from '@/src/types/store-types'
import { RiQuestionFill } from 'react-icons/ri'
import Tooltip from '@/src/components/utils/tooltip/Tooltip'
import styles from './SelectedCartSummary.module.scss'

interface SelectedCartSummaryProps {
  cartItems: CartItemShape[]
  showComparison?: boolean
  selectedIds?: Set<number>
  // New props from PriceSummary
  totalPrice?: number
  shippingCost?: number
  packingCost?: number
  grandTotal?: number
  item_count?: number
  cart_weight?: number
  onCheckout?: () => void
  isShippingCalculated?: boolean
}

const SelectedCartSummary = ({
  cartItems,
  showComparison = false,
  selectedIds,
  // New props from PriceSummary
  totalPrice,
  shippingCost,
  packingCost,
  grandTotal,
  item_count,
  cart_weight,
  onCheckout,
  isShippingCalculated = false,
}: SelectedCartSummaryProps) => {
  const selectedItemIds = cartStore((s) => s.selectedItemIds)

  // Prefer server-provided selection when available, otherwise fall back to persisted array
  const safeSelectedCartItems = React.useMemo(() => {
    if (selectedIds && selectedIds instanceof Set)
      return new Set<number>(selectedIds)
    return new Set<number>(
      Array.isArray(selectedItemIds) ? selectedItemIds : []
    )
  }, [selectedIds, selectedItemIds])

  // Calculate frontend totals for selected items
  const selectedItems = cartItems.filter((item) =>
    safeSelectedCartItems.has(item.id)
  )
  const selectedCount = selectedItems.length
  const totalCount = cartItems.length
  const selectedSubtotal = selectedItems.reduce(
    (sum, item) => sum + item.qty_price,
    0
  )

  if (selectedCount === 0) {
    return (
      <div className={styles.summary_container}>
        <div className={styles.no_selection}>
          <p>No items selected</p>
          <span>Select items to see checkout summary</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.summary_container}>
      <div className={styles.summary_header}>
        <h3>Selected Items Summary</h3>
        <span className={styles.item_count}>
          {selectedCount} of {totalCount} items selected
        </span>
      </div>

      <div className={styles.summary_details}>
        {/* PriceSummary functionality - show when props are provided */}
        {(item_count !== undefined ||
          cart_weight !== undefined ||
          totalPrice !== undefined) && (
          <>
            <div className={styles.price_summary_section}>
              {cart_weight !== undefined && (
                <div className={styles.summary_row}>
                  <span>
                    Cart weight:
                    <Tooltip
                      content={`Shipping cost will be calculated after finalizing adding items to the cart.`}
                      position='top'
                    >
                      <i>
                        <RiQuestionFill />
                      </i>
                    </Tooltip>
                  </span>
                  <span className={styles.price}>{cart_weight}g</span>
                </div>
              )}
              <div className={styles.summary_row}>
                <span>Selected Subtotal:</span>
                <span className={styles.price}>
                  ${selectedSubtotal.toFixed(2)}
                </span>
              </div>

              {isShippingCalculated && shippingCost !== undefined && (
                <div className={styles.summary_row}>
                  <span>Shipping cost:</span>
                  <span className={styles.price}>
                    ${shippingCost.toFixed(2)}
                  </span>
                </div>
              )}

              {isShippingCalculated &&
                packingCost !== undefined &&
                packingCost > 0 && (
                  <div className={styles.summary_row}>
                    <span>Packing cost:</span>
                    <span className={styles.price}>
                      ${packingCost.toFixed(2)}
                    </span>
                  </div>
                )}

              {isShippingCalculated && grandTotal !== undefined && (
                <div className={`${styles.summary_row} ${styles.total_row}`}>
                  <span>
                    <strong>Total:</strong>
                  </span>
                  <span className={styles.price}>
                    <strong>${grandTotal.toFixed(2)}</strong>
                  </span>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Checkout button */}
      {onCheckout && (
        <div className={styles.checkout_section}>
          <button className={styles.checkout_button} onClick={onCheckout}>
            Proceed to checkout
          </button>
        </div>
      )}
    </div>
  )
}

export default SelectedCartSummary
