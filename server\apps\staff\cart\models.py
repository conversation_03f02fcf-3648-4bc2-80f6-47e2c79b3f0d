from django.db import models
from apps.cart.models import Cart, CartItem


class CartProxy(Cart):
    """
    Proxy model for staff-specific cart operations
    Provides additional methods and behavior for staff cart management
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Cart"
        verbose_name_plural = "Staff Carts"
        permissions = [
            ("view_all_carts", "Can view all customer carts"),
            ("manage_abandoned_carts", "Can manage abandoned carts"),
            ("bulk_cart_operations", "Can perform bulk cart operations"),
            ("cart_analytics", "Can view cart analytics"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': str(self.id),
            'customer': {
                'id': self.customer.id if self.customer else None,
                'name': f"{self.customer.first_name} {self.customer.last_name}" if self.customer else "Anonymous",
                'email': self.customer.user.email if self.customer else None,
            },
            'items_count': self.cart_items.count(),
            'total_value': sum(item.get_total_item_price() for item in self.cart_items.all()),
            'total_weight': self.get_cart_weight(),
            'created_at': self.created_at,
            'is_abandoned': self.is_abandoned(),
            'last_activity': self.get_last_activity(),
        }

    def is_abandoned(self):
        """Check if cart is abandoned (no activity for 24+ hours)"""
        from django.utils import timezone
        from datetime import timedelta
        
        if not self.cart_items.exists():
            return False
            
        last_activity = self.get_last_activity()
        if not last_activity:
            return False
            
        return timezone.now() - last_activity > timedelta(hours=24)

    def get_last_activity(self):
        """Get the last activity timestamp for this cart"""
        latest_item = self.cart_items.order_by('-updated_at').first()
        return latest_item.updated_at if latest_item else self.created_at


class CartItemProxy(CartItem):
    """
    Proxy model for staff-specific cart item operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Cart Item"
        verbose_name_plural = "Staff Cart Items"
        permissions = [
            ("view_all_cart_items", "Can view all cart items"),
            ("modify_cart_items", "Can modify customer cart items"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'product': {
                'id': self.product.id,
                'title': self.product.title,
                'sku': self.product_variant.sku,
            },
            'variant': {
                'id': self.product_variant.id,
                'price': self.product_variant.price,
                'stock': self.product_variant.stock_qty,
            },
            'quantity': self.quantity,
            'total_price': self.get_total_item_price(),
            'created_at': self.created_at,
            'updated_at': self.updated_at,
        }
