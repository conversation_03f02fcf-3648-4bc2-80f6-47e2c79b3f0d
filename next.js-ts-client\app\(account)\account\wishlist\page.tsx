'use client'

import { FiTrash2 } from 'react-icons/fi'
import Link from 'next/link'
import Image from 'next/image'
import { useSearchParams } from 'next/navigation'
import authStore from '@/src/stores/auth-store'
import { useDeleteWishlistItem, useWishlist } from '@/src/hooks/wishlist-hooks'
import { ITEMS_PER_PAGE } from '@/src/constants/constants'

import Alert from '@/src/components/utils/alert/Alert'
import Pagination from '@/src/components/utils/pagination/Pagination'
import EmptyWishlist from '@/src/components/utils/empty-wishlist/EmptyWishlist'
import styles from './Wishlist.module.scss'
import WishlistLoading from './loading'

const Wishlist = () => {
  const searchParams = useSearchParams()
  const page = parseInt(searchParams.get('page') || '1', 10)
  const { isLoggedIn } = authStore()
  const { isLoading, error, data } = useWishlist(page, isLoggedIn)
  const mutation = useDeleteWishlistItem()

  const handleDelete = (itemId: number) => {
    mutation.mutate(itemId)
  }

  const handlePageChange = (newPage: number) => {
    const newSearchParams = new URLSearchParams(searchParams.toString())
    newSearchParams.set('page', newPage.toString())
    window.history.pushState(null, '', `?${newSearchParams.toString()}`)
  }

  if (isLoading) {
    return <WishlistLoading />
  }

  console.log(data)

  return (
    <>
      {error ? (
        <Alert variant='error' message={error.message} />
      ) : data?.results.length === 0 ? (
        <EmptyWishlist
          message='Your wishlist is currently empty'
          actionText='Continue Shopping'
        />
      ) : (
        <div className={styles.wishlist}>
          <h2 className='title'>My Wishlist</h2>
          <div className={styles.wishlist__items}>
            {data?.results.map((item) => (
              <div
                key={item.id}
                className={`${styles.wishlist__item} ${
                  !item.product.is_active ? styles.inactive : ''
                }`}
              >
                <div className={styles.product__image__container}>
                  <Image
                    src={`https://res.cloudinary.com/dev-kani/${item.product.product_variant[0].product_image[0].image}`}
                    alt={
                      item.product.product_variant[0].product_image[0]
                        .alternative_text
                    }
                    width={200}
                    height={200}
                    className={styles.product__image}
                    // style={{ objectFit: 'contain' }}
                    // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 200px"
                  />
                  {!item.product.is_active && (
                    <div className={styles.inactive__overlay}>
                      <span className={styles.inactive__text}>
                        This item is not available
                      </span>
                    </div>
                  )}
                </div>
                <div className={styles.item__details}>
                  <h3 className={styles.product__title}>
                    {item.product.title}
                  </h3>
                  <p className={styles.product__price}>
                    ${item.product.product_variant[0].price.toFixed(2)}
                  </p>
                  <p className={styles.added__date}>
                    Added on: {new Date(item.added_at).toLocaleDateString()}
                  </p>
                  <div className={styles.item__actions}>
                    <Link
                      href={`/product/${item.product.slug}`}
                      className={`${styles.product__link} ${
                        !item.product.is_active ? styles.disabled : ''
                      }`}
                    >
                      View Product
                    </Link>
                    <button
                      className='delete_btn'
                      onClick={() => handleDelete(item.id)}
                      aria-label='Remove from wishlist'
                    >
                      <i>
                        <FiTrash2 />
                      </i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {data && data.count > 0 && (
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(data.count / ITEMS_PER_PAGE)}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      )}
    </>
  )
}

export default Wishlist
