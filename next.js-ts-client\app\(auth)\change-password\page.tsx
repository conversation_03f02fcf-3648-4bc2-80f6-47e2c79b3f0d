'use client'

import Al<PERSON> from '@/src/components/utils/alert/Alert'
import { useChangePassword, useLogout } from '@/src/hooks/auth-hooks'
import { useTogglePasswordVisibility } from '@/src/hooks/other-hooks'
import { changeExistingPasswordSchema } from '@/src/schemas/schemas'
import authStore from '@/src/stores/auth-store'
import { ErrorResponse } from '@/src/types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import { z } from 'zod'
import AuthLayout from '../AuthLayout'
import styles from './ChangePassword.module.scss'

export type ChangePasswordShape = z.infer<typeof changeExistingPasswordSchema>

const ChangeExistingPassword = () => {
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { mutation } = useChangePassword()
  const { mutation: userLogout } = useLogout()

  const { isVisible: isCurrentPasswordVisible, toggleVisibility: toggleCurrentPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isNewPasswordVisible, toggleVisibility: toggleNewPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isReNewPasswordVisible, toggleVisibility: toggleReNewPasswordVisibility } = useTogglePasswordVisibility()

  const { register, handleSubmit, formState: { errors } } = useForm<ChangePasswordShape>({
    resolver: zodResolver(changeExistingPasswordSchema)
  })

  const onSubmit: SubmitHandler<ChangePasswordShape> = async (data) => {
    const { old_password, new_password, confirm_password } = data
    mutation.mutate({
      old_password,
      new_password,
      confirm_password,
    }, {
      onSuccess: () => {
        userLogout.mutate()
      },
    })
  }

  // Redirect to login if user is not logged in
  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login/')
    }
  }, [isLoggedIn, router])

  // Return null to prevent rendering anything if user is not logged in
  if (!isLoggedIn) {
    return null
  }

  return (
    <AuthLayout
      title='Change Password'
      error={mutation.error as AxiosError<ErrorResponse> | null}
    >
      {mutation.isSuccess ? (
        <>
          <Alert variant='success' message='Your password resetting is complete. You can now Login with your new password.' />
          <section className='btn_container'>
            <button className='empty_btn' onClick={() => router.push('/login/')}>Login</button>
          </section>
        </>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <div className='form_group'>
            <label className='form_label' htmlFor="old_password">Current Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                type={isCurrentPasswordVisible ? "text" : "password"}
                id="old_password"
                {...register('old_password')} />
              <span onClick={toggleCurrentPasswordVisibility}>
                <i>{isCurrentPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.old_password && <span>{errors.old_password.message}</span>}
          </div>
          <div className='form_group'>
            <label className='form_label' htmlFor="new_password">New Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                disabled={mutation.isPending}
                type={isNewPasswordVisible ? "text" : "password"}
                id="new_password"
                {...register('new_password')}
              />
              <span onClick={toggleNewPasswordVisibility}>
                <i>{isNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.new_password && <p>{errors.new_password.message} &#128543;</p>}
          </div>
          <div className='form_group'>
            <label className='form_label' htmlFor="confirm_password">Confirm New Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                disabled={mutation.isPending}
                type={isReNewPasswordVisible ? "text" : "password"}
                id="confirm_password"
                {...register('confirm_password')}
              />
              <span onClick={toggleReNewPasswordVisibility}>
                <i>{isReNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.confirm_password && <p>{errors.confirm_password.message}</p>}
          </div>
          <div className='btn_container'>
            <button
              type="button"
              className='empty_btn'
              disabled={mutation.isPending}
              onClick={() => router.push('/account/profile')}>Cancel
            </button>
            <button type="submit" className='empty_btn' disabled={mutation.isPending}>
              {mutation.isPending ? (
                // <img src={loading_blue} alt="Loading..." className='loading_svg' />
                'Changing...'
              ) : (
                'Change Password'
              )}
            </button>
          </div>
        </form>
      )}
    </AuthLayout>
  )
}

export default ChangeExistingPassword
