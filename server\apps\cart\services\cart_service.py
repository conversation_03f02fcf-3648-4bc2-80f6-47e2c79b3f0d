"""
Cart Calculation Service

Handles all cart-related calculations including totals, weights, volumes,
and pricing calculations. Focuses on selected cart items only.
"""

import logging
from decimal import Decimal
from typing import Dict, Any, List, Optional
from django.db.models import Sum, QuerySet

from ..models import Cart, CartItem
from .base import BaseCartService


class CartCalculationService(BaseCartService):
    """Service for handling cart calculations focused on selected items"""
    
    def calculate_selected_totals(self, cart: Cart) -> Dict[str, Any]:
        """
        Calculate comprehensive totals for selected cart items

        Args:
            cart: Cart instance

        Returns:
            Dictionary containing all calculated totals for selected items
        """
        return self.execute_with_error_handling(
            f"calculate selected totals for cart {cart.id}",
            self._calculate_selected_totals_impl,
            cart
        )

    def _calculate_selected_totals_impl(self, cart: Cart) -> Dict[str, Any]:
        """Implementation of selected totals calculation"""
        selected_items = cart.cart_items.filter(is_selected=True)

        if not selected_items.exists():
            return self._get_empty_totals()

        # Calculate basic totals
        total_price = self._calculate_selected_total_price(selected_items)
        total_weight = self._calculate_selected_weight(selected_items)
        total_volume = self._calculate_selected_volume(selected_items)
        item_count = self._calculate_selected_item_count(selected_items)

        # Get shipping and packing costs (stored in cart)
        shipping_cost = getattr(cart, 'shipping_cost', Decimal('0.00'))
        packing_cost = getattr(cart, 'packing_cost', Decimal('0.00'))

        # Calculate grand total
        grand_total = total_price + shipping_cost + packing_cost

        return {
            'total_price': total_price,
            'total_weight': total_weight,
            'total_volume': total_volume,
            'item_count': item_count,
            'shipping_cost': shipping_cost,
            'packing_cost': packing_cost,
            'grand_total': grand_total,
            'has_selected_items': True
        }
    
    def calculate_item_total_price(self, cart_item: CartItem) -> Decimal:
        """
        Calculate total price for a single cart item including discounts
        
        Args:
            cart_item: CartItem instance
            
        Returns:
            Total price for the item
        """
        try:
            # Use prefetched active_discounts if available
            active_discounts = getattr(cart_item.product_variant, 'active_discounts', [])
            
            if active_discounts:
                price = active_discounts[0].apply_discount(cart_item.product_variant.price)
            else:
                price = cart_item.product_variant.price
            
            return Decimal(str(price)) * cart_item.quantity
            
        except Exception as e:
            self.logger.error(f"Error calculating item price for cart item {cart_item.id}: {e}")
            return Decimal('0.00')
    
    def check_all_items_selected(self, cart: Cart) -> bool:
        """
        Check if all cart items are selected
        
        Args:
            cart: Cart instance
            
        Returns:
            True if all items are selected, False otherwise
        """
        try:
            total_items = cart.cart_items.count()
            if total_items == 0:
                return False
            
            selected_items = cart.cart_items.filter(is_selected=True).count()
            return total_items == selected_items
            
        except Exception as e:
            self.logger.error(f"Error checking selection status for cart {cart.id}: {e}")
            return False
    
    def _calculate_selected_total_price(self, selected_items: QuerySet) -> Decimal:
        """Calculate total price of selected items"""
        total = Decimal('0.00')
        for item in selected_items:
            total += self.calculate_item_total_price(item)
        return total
    
    def _calculate_selected_weight(self, selected_items: QuerySet) -> Decimal:
        """Calculate total weight of selected items"""
        total_weight = selected_items.aggregate(
            total=Sum('quantity') * Sum('product_variant__weight')
        )['total']
        return Decimal(str(total_weight or 0))
    
    def _calculate_selected_volume(self, selected_items: QuerySet) -> Decimal:
        """Calculate total volume of selected items"""
        total_volume = Decimal('0.0000')
        for item in selected_items:
            item_volume = (
                item.product_variant.length * 
                item.product_variant.width * 
                item.product_variant.height * 
                item.quantity
            )
            total_volume += item_volume
        return total_volume
    
    def _calculate_selected_item_count(self, selected_items: QuerySet) -> int:
        """Calculate total count of selected items"""
        return selected_items.aggregate(total=Sum('quantity'))['total'] or 0
    
    def _get_empty_totals(self) -> Dict[str, Any]:
        """Return empty totals structure"""
        return {
            'total_price': Decimal('0.00'),
            'total_weight': Decimal('0.00'),
            'total_volume': Decimal('0.0000'),
            'item_count': 0,
            'shipping_cost': Decimal('0.00'),
            'packing_cost': Decimal('0.00'),
            'grand_total': Decimal('0.00'),
            'has_selected_items': False
        }
