'use client'

import React from 'react'

import { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from 'react-icons/fi'
import Image from 'next/image'
import Link from 'next/link'
import LimitTitleLength from '@/src/components/utils/TextLimit'
import { CartItemShape } from '@/src/types/store-types'
import {
  useCartItemSelection,
  useBulkCartSelection,
} from '@/src/hooks/cart-selection-hooks'
import cartStore from '@/src/stores/cart-store'
import styles from './CartItemsList.module.scss'

interface CartItemsListProps {
  cartItems: CartItemShape[]
  handleIncrement?: (item: CartItemShape) => void
  handleDecrement?: (item: CartItemShape) => void
  deleteCartItem?: (itemId: number) => void
  showSelection?: boolean
  // optional server-provided selection set (server as source-of-truth)
  selectedIds?: Set<number>
}

const CartItemsList = ({
  cartItems,
  handleIncrement,
  handleDecrement,
  deleteCartItem,
  showSelection = true,
  selectedIds,
}: CartItemsListProps) => {
  // local persisted array (fallback when server doesn't provide selection)
  const storeSelectedItemIds = cartStore((s) => s.selectedItemIds)
  const toggleSelectAll = cartStore((s) => s.toggleSelectAll)
  const { mutate: updateItemSelection } = useCartItemSelection()
  const { bulkSelect, bulkDeselect } = useBulkCartSelection()

  // Build a local Set for O(1) lookups from persisted array
  // Prefer server-provided selection when available, otherwise use persisted array
  const safeSelectedCartItems = React.useMemo(() => {
    if (selectedIds && selectedIds instanceof Set)
      return new Set<number>(selectedIds)
    return new Set<number>(
      Array.isArray(storeSelectedItemIds) ? storeSelectedItemIds : []
    )
  }, [selectedIds, storeSelectedItemIds])

  const handleItemSelectionChange = (
    itemId: number,
    currentlySelected: boolean
  ) => {
    // Update backend
    updateItemSelection({
      itemId,
      isSelected: !currentlySelected,
    })
  }

  const handleSelectAllChange = () => {
    const allItemIds = cartItems.map((item) => item.id)
    const allSelected = allItemIds.every((id) => safeSelectedCartItems.has(id))

    if (allSelected) {
      // Deselect all
      bulkDeselect({ deselect_all: true })
      // when server is source-of-truth we avoid mutating local store
      if (!selectedIds) toggleSelectAll(allItemIds)
    } else {
      // Select all
      bulkSelect({ select_all: true, item_ids: allItemIds })
      if (!selectedIds) toggleSelectAll(allItemIds)
    }
  }

  const allItemsSelected =
    cartItems.length > 0 &&
    cartItems.every((item) => safeSelectedCartItems.has(item.id))
  const someItemsSelected = cartItems.some((item) =>
    safeSelectedCartItems.has(item.id)
  )

  return (
    <div>
      {showSelection && cartItems.length > 0 && (
        <div className={styles.selection_header}>
          <label className={styles.select_all_container}>
            <input
              type='checkbox'
              checked={allItemsSelected}
              ref={(input) => {
                if (input)
                  input.indeterminate = someItemsSelected && !allItemsSelected
              }}
              onChange={handleSelectAllChange}
              className={styles.select_all_checkbox}
            />
            <span>Select All ({cartItems.length} items)</span>
          </label>
        </div>
      )}

      <ul>
        {cartItems?.map((item: CartItemShape) => {
          const isSelected = safeSelectedCartItems.has(item.id)

          return (
            <li
              key={item.id}
              className={`${styles.cart_item} ${
                isSelected ? styles.selected : ''
              }`}
            >
              {showSelection && (
                <div className={styles.cart_item__selection}>
                  <input
                    type='checkbox'
                    checked={isSelected}
                    onChange={() =>
                      handleItemSelectionChange(item.id, isSelected)
                    }
                    className={styles.item_checkbox}
                  />
                </div>
              )}

              <div className={styles.cart_item__img}>
                <Image
                  src={
                    item.product_variant?.product_image?.[0]?.image
                      ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                      : ''
                  }
                  alt={
                    item.product_variant?.product_image?.[0]
                      ?.alternative_text || item.product.title
                  }
                  width={200}
                  height={200}
                />
              </div>

              <div className={styles.cart_item__info}>
                <span className={styles.cart_item__title}>
                  <Link href={`/product/${item.product.slug}`}>
                    <LimitTitleLength
                      title={item.product.title}
                      maxLength={60}
                    />
                  </Link>
                </span>
                {` `}
                <span>
                  (${item.product_variant.price} x {item.quantity})
                </span>
                <span>
                  Variant: {item.product_variant?.price_label?.attribute_value}
                </span>
                {Object.entries(item.extra_data).map(([key, value], index) => (
                  <div key={index} className={styles.cart_item__extra_data}>
                    <p>{key} :</p>
                    <p>{value}</p>
                  </div>
                ))}
              </div>

              <div className={styles.cart_item__quantity}>
                <div>
                  <p>Qty:</p>
                  {handleDecrement && (
                    <button
                      onClick={() => handleDecrement(item)}
                      disabled={item.product_variant.stock_qty === 0}
                    >
                      <i>
                        <FiMinus />
                      </i>
                    </button>
                  )}
                  <p>{item.quantity}</p>
                  {handleIncrement && (
                    <button
                      onClick={() => handleIncrement(item)}
                      disabled={item.product_variant.stock_qty === 0}
                    >
                      <i>
                        <FiPlus />
                      </i>
                    </button>
                  )}
                  {deleteCartItem && (
                    <button onClick={() => deleteCartItem(item.id)}>
                      <i>
                        <FiTrash2 />
                      </i>
                    </button>
                  )}
                </div>
                {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}
              </div>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default CartItemsList
