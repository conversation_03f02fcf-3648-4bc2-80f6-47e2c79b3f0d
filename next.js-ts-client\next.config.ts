import type { NextConfig } from "next"

const nextConfig: NextConfig = {
  typedRoutes: true,

  // Move this outside of experimental and rename it
  serverExternalPackages: [],

  // Remove the old experimental setting
  // experimental: {
  //   serverComponentsExternalPackages: [], // Remove this
  // },

  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  images: {
    remotePatterns: [new URL('https://res.cloudinary.com/dev-kani/**')],
  },
}

export default nextConfig