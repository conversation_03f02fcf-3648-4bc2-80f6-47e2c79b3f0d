'use client'

import Modal from '@/src/components/utils/modal/Modal'
import PhoneNumberInput from '@/src/components/utils/phone-number-input/PhoneNumberInput'
import { useChangeAuthInfo } from '@/src/hooks/auth-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { changeAuthInfoSchema } from '@/src/schemas/schemas'
import authStore from '@/src/stores/auth-store'
import { ErrorResponse } from '@/src/types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import AuthLayout from '../../AuthLayout'

export type NewAuthInfoShape = z.infer<typeof changeAuthInfoSchema>

const ChangePhoneNumber = () => {
  const { isLoggedIn } = authStore()
  const { data: customerData } = useCustomerDetails(isLoggedIn)
  const [phoneValue, setPhoneValue] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState<NewAuthInfoShape | null>(null)
  const router = useRouter()
  const { mutation } = useChangeAuthInfo()

  const { register, handleSubmit, formState: { errors }, setValue } = useForm<NewAuthInfoShape>({
    resolver: zodResolver(changeAuthInfoSchema),
    defaultValues: {
      phone_number: customerData?.phone_number || ''
    }
  })

  useEffect(() => {
    if (customerData?.phone_number) {
      const initialPhoneNumber = customerData.phone_number.replace('+', '')
      setPhoneValue(initialPhoneNumber)
      setValue('phone_number', customerData.phone_number)
    }
  }, [customerData, setValue])

  const submitForm = (data: NewAuthInfoShape) => {
    mutation.mutate(data, {
      onSuccess: () => {
        router.push('/change-auth-info/verify')
      }
    })
  }

  const onSubmit: SubmitHandler<NewAuthInfoShape> = (data) => {
    // Check if there's an existing phone number and if it's different from the new one
    if (customerData?.phone_number && customerData.phone_number !== data.phone_number) {
      setFormData(data) // Store form data temporarily
      setShowModal(true) // Show modal only when changing existing number
    } else {
      // If no existing phone number or no change, submit directly
      submitForm(data)
    }
  }

  const handleConfirm = () => {
    if (formData) {
      submitForm(formData)
    }
    setShowModal(false)
  }

  const handleCancel = () => {
    setShowModal(false)
  }

  const handleInputChange = (value: string) => {
    setPhoneValue(value)
    setValue("phone_number", `+${value}`)
  }

  return (
    <AuthLayout title="Change phone number" error={mutation.error as AxiosError<ErrorResponse> | null}>
      <form onSubmit={handleSubmit(onSubmit)} className='form'>
        <div className='form_group'>
          <PhoneNumberInput
            {...register("phone_number")}
            value={phoneValue}
            defaultCountry="lk"
            onlyCountries={['lk', 'us']}
            onChange={handleInputChange}
            placeholder='Enter phone number'
            pending={mutation.isPending}
          />
          {errors.phone_number && <p>{errors.phone_number.message} 😟</p>}
        </div>
        <section className='btn_container'>
          <button
            type="button"
            className='empty_btn'
            disabled={mutation.isPending}
            onClick={() => router.push('/account/profile')}>Cancel
          </button>
          <button type="submit" className='empty_btn' disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_blue} alt="Loading..." className='loading_svg' />
              'Updating...'
            ) : (
              'Update'
            )}
          </button>
        </section>
      </form>

      <Modal
        title="Confirm Phone Number Change"
        message="Are you sure you want to change your phone number?"
        show={showModal}
        onClose={handleCancel}
        onConfirm={handleConfirm}
        btn1="Yes"
        btn2="No"
      />
    </AuthLayout>
  )
}

export default ChangePhoneNumber