import { CartItemShape, CustomerShape } from './store-types'

export interface FetchPaginatedResponse<T> {
  count: number
  next: number | null
  previous: number | null
  results: T[]
}

// export interface FetchResponse<T> {
//   data?: T[]
// }

// Registration related types
export interface InitRegUserShape {
  email?: string
  phone_number?: string
}

export interface VerificationCredentials {
  code: number
  email?: string
  phone_number?: string
}

export interface PaymentOptionsShape {
  id: number
  name: string
  is_active: boolean
}

export interface SimpleCartShape {
  id: string
  customer: CustomerShape | null
  cart_items: CartItemShape[]
  selected_total_price: 160.97
  selected_cart_weight: 4800.0
  selected_item_count: 3
  all_items_selected: true
}

export interface CartShape {
  id: string
  customer: CustomerShape | null
  cart_items: CartItemShape[]
  total_price: number
  shipping_cost?: number
  packing_cost?: number
  total_volume?: number
  packing_details?: PackingDetails
  grand_total?: number
  item_count: number
  cart_weight: number
  last_shipping_calculation?: string
}

export interface PackingDetails {
  boxes: PackingBox[]
  method_used: string
  calculation_time: number
  calculated_at: string
  shipping_options_count: number
}

export interface PackingBox {
  box_id: number
  box_title: string
  box_cost: number
  utilization: number
  total_weight: number
  items_count: number
}

export interface ShippingOption {
  carrier_name: string
  service_name: string
  cost: number
  estimated_days: number
  max_days: number
  tracking_available: boolean
  insurance_available: boolean
  signature_available: boolean
}

export interface ShippingCalculationResponse {
  cart: CartShape
  shipping_calculation: {
    success: boolean
    message: string
    calculation_time: number
    packing_cost: number
    total_weight: number
    total_volume: number
    shipping_options: ShippingOption[]
    packing_details: {
      boxes_count: number
      method_used: string
      calculation_time: number
      warnings: string[]
    }
    shipping_cost: number
    estimated_delivery_days: number
    carrier_name: string
  }
}

export interface ErrorResponse {
  [key: string]: string[]
}

export interface AttributeValue {
  id: number
  attribute: {
    id: number
    title: string
  }
  attribute_value: string
  for_filtering: boolean
  is_active: boolean
  selectable: boolean
}

export interface FilterOptionsShape {
  price_range: {
    min: number
    max: number
  }
  condition: [string, string][]
  brands: {
    id: number
    slug: string
  }[]
  attribute_filters: {
    [key: string]: string[]
  }
}
