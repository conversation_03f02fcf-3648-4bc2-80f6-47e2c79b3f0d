'use client'

import { useState, useMemo, useEffect } from 'react'
import Image from 'next/image'
import { FiMinus, FiPlus } from 'react-icons/fi'
import { useRouter } from 'next/navigation'
import {
  ProductShape,
  ProductVariant,
} from '../../../../../src/types/product-types'
import {
  calculateDiscount,
  priceWithTrickyDiscount,
  getStockMessage,
  getStockStatusClass,
  getVariantColorValue,
  getColorSelectionImage,
} from '../utils/product-utils'
import { Rating } from '../../../../../src/components/rating/Rating'
import AddToCartButton from '../add-to-cart-button/AddToCartButton'
import WishlistButton from '../wishlist-button/WishlistButton'
import SelectableAttributeValues from '../selectable-attr-values/SelectableAttributeValues'
import cartStore from '../../../../../src/stores/cart-store'
import styles from './ProductInfo.module.scss'

interface ProductInfoProps {
  product: ProductShape
  selectedVariant: ProductVariant | null
  qty: number
  onVariantClick: (variant: ProductVariant) => void
  onQtyChange: (qty: number) => void
}

export default function ProductInfo({
  product,
  selectedVariant,
  qty,
  onVariantClick,
  onQtyChange,
}: ProductInfoProps) {
  const router = useRouter()
  const { setExtraData, resetExtraData } = cartStore()
  const [selectedAttributes, setSelectedAttributes] = useState<
    Record<string, string>
  >({})
  const [hasUserInteracted, setHasUserInteracted] = useState(false)

  // Sync selectedAttributes with cart store extra_data
  // Only populate extra_data if there are selectable attributes (option_selectors or selectable_attribute_values)
  // and the user has made selections from them
  useEffect(() => {
    const hasSelectableAttributes =
      (product?.option_selectors && product.option_selectors.length > 0) ||
      (product?.selectable_attribute_values &&
        product.selectable_attribute_values.length > 0)

    if (
      hasSelectableAttributes &&
      hasUserInteracted &&
      Object.keys(selectedAttributes).length > 0
    ) {
      // Only include attributes that are actually selectable by the user
      const selectableExtraData: Record<string, string> = {}

      // Check if each selected attribute is actually selectable
      Object.entries(selectedAttributes).forEach(([attrTitle, attrValue]) => {
        const isSelectableAttribute =
          product?.option_selectors?.some(
            (selector) =>
              selector.attribute_title.toLowerCase() === attrTitle.toLowerCase()
          ) ||
          product?.selectable_attribute_values?.some(
            (selectableAttr) =>
              selectableAttr.attribute_value.attribute.title.toLowerCase() ===
              attrTitle.toLowerCase()
          )

        if (isSelectableAttribute) {
          selectableExtraData[attrTitle] = attrValue
        }
      })

      if (Object.keys(selectableExtraData).length > 0) {
        setExtraData(selectableExtraData)
      } else {
        resetExtraData()
      }
    } else {
      resetExtraData()
    }
  }, [
    selectedAttributes,
    setExtraData,
    resetExtraData,
    product,
    hasUserInteracted,
  ])

  // Get the primary attribute title (the one used for price_label)
  const primaryAttributeTitle =
    product?.product_variant?.[0]?.price_label_attr_title || 'Options'

  // Group variants by primary attribute (e.g., Size) and sort by order
  const groupedVariants = useMemo(() => {
    if (!product?.product_variant || product.product_variant.length === 0) {
      return []
    }

    const sortedVariants = [...product.product_variant].sort(
      (a, b) => a.order - b.order
    )
    const groups: Record<
      string,
      {
        primaryValue: string
        price: number
        order: number
        variants: ProductVariant[]
      }
    > = {}

    sortedVariants.forEach((variant) => {
      const primaryValue = variant.price_label

      if (!groups[primaryValue]) {
        groups[primaryValue] = {
          primaryValue,
          price: variant.price,
          order: variant.order,
          variants: [],
        }
      }

      groups[primaryValue].variants.push(variant)
    })

    return Object.values(groups).sort((a, b) => a.order - b.order)
  }, [product])

  // Get secondary attributes (attributes other than the primary one)
  const secondaryAttributes = useMemo(() => {
    if (!product?.option_selectors) {
      return []
    }

    return product.option_selectors.filter(
      (selector) =>
        selector.attribute_title.toLowerCase() !==
        primaryAttributeTitle.toLowerCase()
    )
  }, [product, primaryAttributeTitle])

  // Helper function to get all available attribute values for a given attribute
  // based on the current selections, ordered by variant order
  const getAvailableAttributeValues = (attributeTitle: string) => {
    if (!product?.product_variant) return []

    // Create a temporary set of attributes that includes all current selections
    // but excludes the attribute we're checking
    const currentSelections = { ...selectedAttributes }
    delete currentSelections[attributeTitle]

    // Sort variants by order first
    const sortedVariants = [...product.product_variant].sort(
      (a, b) => a.order - b.order
    )

    // Find variants that match all currently selected attributes
    const matchingVariants = sortedVariants.filter((variant) => {
      // If no attributes are selected, all variants match
      if (Object.keys(currentSelections).length === 0) return true

      // Check if this variant matches all currently selected attributes
      return Object.entries(currentSelections).every(
        ([attrTitle, attrValue]) => {
          return variant.attribute_value.some(
            (attr) =>
              attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
              attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        }
      )
    })

    // Extract all unique values for the given attribute from matching variants
    // while preserving the order based on variant order
    const availableValues: string[] = []
    const seenValues = new Set<string>()

    matchingVariants.forEach((variant) => {
      variant.attribute_value.forEach((attr) => {
        if (
          attr.attribute.title.toLowerCase() === attributeTitle.toLowerCase()
        ) {
          const lowerValue = attr.attribute_value.toLowerCase()
          if (!seenValues.has(lowerValue)) {
            seenValues.add(lowerValue)
            availableValues.push(lowerValue)
          }
        }
      })
    })

    return availableValues
  }

  // Initialize selected attributes based on the selected variant
  useEffect(() => {
    if (selectedVariant && Object.keys(selectedAttributes).length === 0) {
      // Initialize selected attributes based on the selected variant
      const initialAttributes: Record<string, string> = {}
      selectedVariant.attribute_value.forEach((attr) => {
        initialAttributes[attr.attribute.title] = attr.attribute_value
      })
      setSelectedAttributes(initialAttributes)
    }
  }, [selectedVariant, selectedAttributes])

  // Handle primary attribute selection (e.g., Size) with toggle functionality
  const handlePrimarySelection = (value: string) => {
    setHasUserInteracted(true)
    const isCurrentlySelected =
      selectedAttributes[primaryAttributeTitle] === value

    if (isCurrentlySelected) {
      // If already selected, deselect it
      const newAttributes = { ...selectedAttributes }
      delete newAttributes[primaryAttributeTitle]

      // Update the selected attributes
      setSelectedAttributes(newAttributes)

      // Note: extra_data will be updated by the useEffect hook based on selectable attributes

      // Find the best variant to select after deselection
      const sortedVariants = [...(product?.product_variant || [])].sort(
        (a, b) => a.order - b.order
      )

      // If there are other selected attributes, find a variant that matches them
      if (Object.keys(newAttributes).length > 0) {
        const matchingVariant = sortedVariants.find((variant) => {
          return Object.entries(newAttributes).every(
            ([attrTitle, attrValue]) => {
              return variant.attribute_value.some(
                (attr) =>
                  attr.attribute.title.toLowerCase() ===
                    attrTitle.toLowerCase() &&
                  attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
              )
            }
          )
        })
        if (matchingVariant) {
          onVariantClick(matchingVariant)
        }
      } else {
        // No other attributes selected, go to first variant
        if (sortedVariants.length > 0) {
          onVariantClick(sortedVariants[0])
        }
      }
    } else {
      // Select this primary value
      const newAttributes = {
        ...selectedAttributes,
        [primaryAttributeTitle]: value,
      }
      setSelectedAttributes(newAttributes)

      // Note: extra_data will be updated by the useEffect hook based on selectable attributes

      // Find the best variant that matches all selected attributes
      const sortedVariants = [...(product?.product_variant || [])].sort(
        (a, b) => a.order - b.order
      )
      const matchingVariant = sortedVariants.find((variant) => {
        return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
          return variant.attribute_value.some(
            (attr) =>
              attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
              attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        })
      })

      if (matchingVariant) {
        onVariantClick(matchingVariant)
      }
    }
  }

  // Helper function to check if an attribute value is available given current selections
  const isAttributeValueAvailable = (
    attributeTitle: string,
    attributeValue: string
  ) => {
    if (!product?.product_variant) return false

    // Create a temporary set of attributes that includes the value we're checking
    const testSelections = {
      ...selectedAttributes,
      [attributeTitle]: attributeValue,
    }

    // Check if there's any variant that matches all these selections
    return product.product_variant.some((variant) => {
      return Object.entries(testSelections).every(([attrTitle, attrValue]) => {
        return variant.attribute_value.some(
          (attr) =>
            attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
            attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
        )
      })
    })
  }

  // Handle attribute selection with cascading filtering and toggle functionality
  const handleAttributeSelection = (
    attributeTitle: string,
    _valueId: number,
    valueText: string
  ) => {
    setHasUserInteracted(true)
    // Check if this attribute value is already selected (toggle functionality)
    const isCurrentlySelected = selectedAttributes[attributeTitle] === valueText

    if (isCurrentlySelected) {
      // If already selected, deselect it
      const newAttributes = { ...selectedAttributes }
      delete newAttributes[attributeTitle]

      // Update the selected attributes
      setSelectedAttributes(newAttributes)

      // Note: extra_data will be updated by the useEffect hook based on selectable attributes

      // Find the best variant to select after deselection
      const sortedVariants = [...(product?.product_variant || [])].sort(
        (a, b) => a.order - b.order
      )

      if (Object.keys(newAttributes).length > 0) {
        // Find a variant that matches the remaining selected attributes
        const matchingVariant = sortedVariants.find((variant) => {
          return Object.entries(newAttributes).every(
            ([attrTitle, attrValue]) => {
              return variant.attribute_value.some(
                (attr) =>
                  attr.attribute.title.toLowerCase() ===
                    attrTitle.toLowerCase() &&
                  attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
              )
            }
          )
        })
        if (matchingVariant) {
          onVariantClick(matchingVariant)
        }
      } else {
        // No attributes selected, go to first variant
        if (sortedVariants.length > 0) {
          onVariantClick(sortedVariants[0])
        }
      }
    } else {
      // Select this attribute value
      const newAttributes = {
        ...selectedAttributes,
        [attributeTitle]: valueText,
      }

      // Check if this selection creates a valid variant combination
      const hasValidVariant = product?.product_variant?.some((variant) => {
        return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
          return variant.attribute_value.some(
            (attr) =>
              attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
              attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        })
      })

      // If this selection doesn't create a valid variant combination, don't update
      if (!hasValidVariant) {
        console.log(
          `No valid variant found for selection: ${attributeTitle}=${valueText}`
        )
        return
      }

      // Update the selected attributes
      setSelectedAttributes(newAttributes)

      // Note: extra_data will be updated by the useEffect hook based on selectable attributes

      // Find the variant that matches all selected attributes
      const sortedVariants = [...(product?.product_variant || [])].sort(
        (a, b) => a.order - b.order
      )
      const matchingVariant = sortedVariants.find((variant) => {
        return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
          return variant.attribute_value.some(
            (attr) =>
              attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
              attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        })
      })

      if (matchingVariant) {
        onVariantClick(matchingVariant)
      }
    }
  }

  // Get missing attributes that need to be selected
  const missingAttributes = useMemo(() => {
    if (!product?.option_selectors) return []

    return product.option_selectors
      .filter((selector) => !selectedAttributes[selector.attribute_title])
      .map((selector) => selector.attribute_title)
  }, [product, selectedAttributes])

  const handleBuyNow = () => {
    if (!selectedVariant || selectedVariant.stock_qty <= 0) {
      alert('This item is temporarily out of stock.')
      return
    }
    // Add to cart logic would go here
    router.push('/checkout/cart')
  }

  return (
    <section className={styles.product_details}>
      <section className={styles.title_and_rating}>
        <h1 className={styles.product_title}>{product?.title}</h1>
        <div className={styles.rating_container}>
          <Rating product={product} color='#FF9900' />
        </div>
      </section>

      <hr className={styles.divider} />

      <section className={styles.price_container}>
        <div className={styles.discount_badge}>
          {calculateDiscount(selectedVariant?.price)}% off
        </div>
        <div className={styles.price}>
          <span className={styles.current_price}>
            ${selectedVariant?.price?.toFixed(2)}
          </span>
          <span className={styles.original_price}>
            ${priceWithTrickyDiscount(selectedVariant?.price)}
          </span>
        </div>
        <div className={styles.savings}>
          You save: $
          {(
            parseFloat(priceWithTrickyDiscount(selectedVariant?.price) || '0') -
            (selectedVariant?.price || 0)
          ).toFixed(2)}
        </div>
      </section>

      <hr className={styles.divider} />

      {/* Primary Attribute Selector (e.g., Size) */}
      {groupedVariants.length > 1 && (
        <section className={styles.product_variants}>
          <h3>
            {primaryAttributeTitle}:
            {selectedAttributes[primaryAttributeTitle] && (
              <span className={styles.selected_value_display}>
                {selectedAttributes[primaryAttributeTitle]}
              </span>
            )}
          </h3>
          <div className={styles.variants}>
            {groupedVariants.map((group) => {
              const isSelected =
                selectedAttributes[primaryAttributeTitle] === group.primaryValue
              return (
                <div
                  key={group.primaryValue}
                  className={`${
                    isSelected
                      ? `${styles.variant__highlight} ${styles.variant}`
                      : `${styles.variant}`
                  }`}
                  onClick={() => handlePrimarySelection(group.primaryValue)}
                  title={
                    isSelected
                      ? `Click to deselect ${primaryAttributeTitle} ${group.primaryValue}`
                      : `Click to select ${primaryAttributeTitle} ${group.primaryValue}`
                  }
                  style={{ cursor: 'pointer' }}
                >
                  <p>{group.primaryValue}</p>
                  <p>${group.price.toFixed(2)}</p>
                </div>
              )
            })}
          </div>
        </section>
      )}

      {/* Secondary Attribute Selectors (e.g., Color) */}
      {secondaryAttributes.map((selector) => (
        <section
          key={selector.attribute_id}
          className={
            `${styles.option_selector} ` +
            (missingAttributes.length > 0 &&
            missingAttributes[0] === selector.attribute_title
              ? styles.next_to_select
              : '')
          }
        >
          <h3 className={styles.selector_title}>
            {selector.attribute_title}:
            {selectedAttributes[selector.attribute_title] && (
              <span className={styles.selected_value_display}>
                {selectedAttributes[selector.attribute_title]}
              </span>
            )}
            {/* Show message if this is the next attribute to select and not selected yet */}
            {missingAttributes.length > 0 &&
              missingAttributes[0] === selector.attribute_title &&
              !selectedAttributes[selector.attribute_title] && (
                <span className={styles.missing_attr_msg}>
                  Select a {selector.attribute_title}
                </span>
              )}
          </h3>
          <div className={styles.selector_values}>
            {selector.values.map((value) => {
              const isAvailable = isAttributeValueAvailable(
                selector.attribute_title,
                value.value_text
              )
              const isSelected =
                selectedAttributes[selector.attribute_title] ===
                value.value_text

              // Determine the appropriate title based on state
              let buttonTitle = value.value_text
              if (!isAvailable) {
                buttonTitle = `Not available with current selections`
              } else if (isSelected) {
                buttonTitle = `Click to deselect ${selector.attribute_title} ${value.value_text}`
              } else {
                buttonTitle = `Click to select ${selector.attribute_title} ${value.value_text}`
              }

              return (
                <button
                  key={value.value_id}
                  className={`
                    ${styles.selector_value}
                    ${isSelected ? styles.selected_value : ''}
                    ${!isAvailable ? styles.unavailable_value : ''}
                  `}
                  onClick={() =>
                    handleAttributeSelection(
                      selector.attribute_title,
                      value.value_id,
                      value.value_text
                    )
                  }
                  disabled={!isAvailable}
                  title={buttonTitle}
                >
                  {selector.attribute_title.toLowerCase() === 'color' ? (
                    (() => {
                      // Find a variant with this color to get its image
                      const colorVariant = product?.product_variant?.find(
                        (variant) => {
                          const variantColor = getVariantColorValue(variant)
                          return (
                            variantColor?.toLowerCase() ===
                            value.value_text.toLowerCase()
                          )
                        }
                      )

                      const colorImage = colorVariant
                        ? getColorSelectionImage(
                            colorVariant,
                            process.env.NEXT_PUBLIC_CLOUDINARY_URL || ''
                          )
                        : null

                      return colorImage ? (
                        <div
                          className={`${styles.color_image_button} ${
                            isSelected ? styles.selected_color_image : ''
                          } ${
                            !isAvailable ? styles.unavailable_color_image : ''
                          }`}
                          title={buttonTitle}
                        >
                          <Image
                            src={colorImage}
                            alt={`${value.value_text} color option`}
                            width={35}
                            height={35}
                            sizes='35px'
                            style={{
                              objectFit: 'cover',
                              width: '100%',
                              height: '100%',
                            }}
                          />
                        </div>
                      ) : (
                        // Fallback to color swatch if no image available
                        <div
                          className={styles.color_swatch}
                          style={{
                            backgroundColor: value.value_text.toLowerCase(),
                          }}
                          title={buttonTitle}
                        />
                      )
                    })()
                  ) : (
                    // For non-color attributes, show text
                    <span className={styles.attribute_text}>
                      {value.value_text}
                    </span>
                  )}
                </button>
              )
            })}
          </div>
        </section>
      ))}

      {/* If there are no option selectors, show the selectable attribute values */}
      {(!product?.option_selectors || product.option_selectors.length === 0) &&
        product?.selectable_attribute_values &&
        product.selectable_attribute_values.length > 0 && (
          <section className={styles.selectable_attribute_values}>
            <SelectableAttributeValues
              selectableAttValues={product.selectable_attribute_values}
            />
          </section>
        )}

      <section className={styles.stock_status}>
        <p
          className={`${styles.stock_indicator} ${
            styles[getStockStatusClass(selectedVariant?.stock_qty || 0)]
          }`}
        >
          {getStockMessage(selectedVariant?.stock_qty || 0)}
        </p>
      </section>

      <hr className={styles.divider} />

      <section className={styles.product_quantity}>
        <p>Quantity:</p>
        <div className={styles.quantity__controls}>
          <button onClick={() => onQtyChange(qty - 1)} disabled={qty <= 1}>
            <i>
              <FiMinus />
            </i>
          </button>
          <input
            type='text'
            value={qty}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              if (!isNaN(value) && value > 0) {
                onQtyChange(value)
              }
            }}
            min={1}
            max={selectedVariant?.stock_qty || 10}
          />
          <button
            onClick={() => onQtyChange(qty + 1)}
            disabled={qty >= (selectedVariant?.stock_qty || 10)}
          >
            <i>
              <FiPlus />
            </i>
          </button>
        </div>
      </section>

      <section className={styles.checkout}>
        <AddToCartButton
          product={product}
          selectedVariant={selectedVariant}
          qty={qty}
        />

        <button
          className={styles.buy_now_btn}
          onClick={handleBuyNow}
          disabled={!selectedVariant || selectedVariant.stock_qty === 0}
        >
          Buy Now
        </button>

        <div className={styles.wishlist_wrapper}>
          <WishlistButton productId={product.id} />
        </div>
      </section>
    </section>
  )
}
