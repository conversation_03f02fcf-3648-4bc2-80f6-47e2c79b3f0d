@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: $spacing-6;
}

.title {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.subtitle {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-base;
}

.content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: $spacing-6;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.sidebar {
  @include mobile-only {
    order: 2;
  }
}

.tabNav {
  @include flex-column;
  gap: $spacing-1;
  
  @include mobile-only {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: $spacing-2;
  }
}

.tabButton {
  @include flex-start;
  gap: $spacing-3;
  padding: $spacing-3 $spacing-4;
  border: none;
  background: transparent;
  border-radius: $border-radius;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-align: left;
  width: 100%;
  
  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }
  
  &.active {
    background-color: $primary-100;
    color: $primary-700;
  }
  
  @include mobile-only {
    flex-shrink: 0;
    width: auto;
    min-width: 120px;
    justify-content: center;
  }
}

.tabIcon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.main {
  @include mobile-only {
    order: 1;
  }
}

.settingsCard {
  h2 {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-sm;
  }
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
  
  label {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.formActions {
  @include flex-start;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

// Preferences specific styles
.preferenceSection {
  margin-bottom: $spacing-6;
  
  h3 {
    margin: 0 0 $spacing-4 0;
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
}

.preferenceGrid {
  @include flex-column;
  gap: $spacing-4;
}

.preferenceItem {
  @include flex-between;
  gap: $spacing-4;
  padding: $spacing-4;
  background-color: $gray-50;
  border-radius: $border-radius;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-3;
  }
}

.preferenceInfo {
  flex: 1;
  
  h4 {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-900;
  }
  
  p {
    margin: 0;
    font-size: $font-size-xs;
    color: $gray-600;
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }
  
  .title {
    font-size: $font-size-xl;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
  }
  
  .formActions {
    flex-direction: column;
    align-items: stretch;
  }
}
