@use './variables' as *;
@use './mixins' as *;
@use './animations' as *;
@use 'sass:color';

a {
  text-decoration: none;
}

.title {
  font-size: $font-size-3;
  font-weight: 600;
  // margin-bottom: 1rem;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  // background-color: aqua;
}

html {
  font-family: $primary-font-family;
}

button {
  cursor: pointer;
  border: none;
  letter-spacing: 0.4px;
  border-radius: 2px;
  font-size: 16px;
}

.loading_svg {
  margin: 2px 10px;
  width: 20px;
  height: 20px;
}

.logo_header {
  display: flex;
  justify-content: center;
  background-color: $primary-dark;
  padding: 10px 0;
}

.title {
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  margin: 1rem 0;
  color: $primary-dark-blue;
}

.form {
  .form_group {
    display: flex;
    flex-direction: column;
    margin: 15px 0;
    row-gap: 4px;

    .form_label {
      font-weight: bold;
      color: $primary-dark-blue;
    }

    .form_input {
      width: 100%;
      border: 0.1px solid $primary-dark-text-color;
      border-radius: 3px;
      padding: 5px 5px;
      font-size: 16.5px;

      &:focus {
        outline: 2px solid $lighten-blue;
        border: none;
      }
    }

    .form_error {
      color: $error-red;
      text-align: center;
    }
  }
}

.empty_btn {
  @include btn($lighten-blue, #fff);
  // margin: 0 auto 0 auto;
  padding: 0.36rem 1.2rem;
  border: 1px solid $lighten-blue;
  letter-spacing: 0.7px;
  transition: all 0.2s ease;

  &:hover {
    border: 1px solid $primary-blue;
    // color: $primary-blue;
  }
}

.delete_btn {
  @include btn($error-red, #fff);
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:hover {
    background-color: color.adjust($error-red, $lightness: 40%, $space: hsl);
  }

  i {
    @include flexbox(center, center);
  }
}

.success_message {
  margin: 1rem 0;
  // padding: 0 0 1rem 0;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.btn_container {
  // background-color: rgb(132, 189, 189);
  display: flex;
  flex-direction: row;
  justify-content: center;
  // align-items: center;
  column-gap: 1rem;
}

.password__container {
  position: relative;
  display: flex;
  align-items: center;

  // input {
  //   width: 100%;
  // }

  span {
    position: absolute;
    right: 7px;
    cursor: pointer;

    i {
      @include flexbox(center, center);
      font-size: 18px;
      color: $primary-dark-text-color;
    }
  }
}

// .loading_span {
//   @include flexbox(center, center);
//   gap: 0.5rem;
// }
