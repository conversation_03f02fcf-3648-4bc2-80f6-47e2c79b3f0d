'use client'

import { useEffect } from 'react'
import { ProductShape, ProductVariant } from '../../../../../src/types/product-types'
import { useAddToCart } from '../../../../../src/hooks/cart-hooks'
import cartStore from '../../../../../src/stores/cart-store'
import styles from './AddToCartButton.module.scss'

interface AddToCartButtonProps {
  product: ProductShape
  selectedVariant: ProductVariant | null
  qty: number
}

export default function AddToCartButton({ product, selectedVariant, qty }: AddToCartButtonProps) {
  const { setProductVariant } = cartStore()
  const { handleAddToCart, isPending, error } = useAddToCart(product, qty)

  // Update the cart store when selectedVariant changes
  useEffect(() => {
    if (selectedVariant) {
      setProductVariant(selectedVariant)
    }
  }, [selectedVariant, setProductVariant])

  const isDisabled = isPending || !selectedVariant || selectedVariant.stock_qty === 0

  const onAddToCart = () => {
    if (!selectedVariant) return
    handleAddToCart()
  }

  return (
    <div className={styles.add_to_cart_container}>
      <button
        className={styles.add_to_cart_btn}
        type="button"
        disabled={isDisabled}
        onClick={onAddToCart}
      >
        {isPending ? (
          <div className={styles.loading_spinner}>
            <div className={styles.spinner}></div>
            Adding...
          </div>
        ) : (
          'Add to Cart'
        )}
      </button>

      {error && (
        <div className={styles.error_message}>
          {error.message || 'Failed to add item to cart. Please try again.'}
        </div>
      )}
    </div>
  )
}