import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import styles from './PhoneNumberInput.module.scss' // Optional, for additional custom styling

interface Props {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  defaultCountry?: string
  pending?: boolean
  onlyCountries?: string[]
}

const PhoneNumberInput = ({ value,
  onChange,
  placeholder = "Enter phone number",
  defaultCountry = "us",
  pending,
  onlyCountries
}: Props) => {
  return (
    <PhoneInput
      country={defaultCountry}
      onlyCountries={onlyCountries}
      value={value}
      onChange={onChange}
      disabled={pending}
      placeholder={placeholder}
      // prefix={'+'}
      inputProps={{
        name: 'phone',
        autoFocus: true,
        placeholder
      }}
      // You can set default styles here or use an external SCSS file for more control
      containerClass={styles.phone_input_container}
      inputClass={styles.phone_input}
      buttonClass={styles.phone_input_button}
      dropdownClass={styles.phone_input_dropdown}
      searchClass={styles.phone_input_search}
      containerStyle={{
        // width: '100%',
        // border: '1px solid #ccc',
        // borderRadius: '4px',
        // padding: '8px',
        // fontSize: '16px',
      }}
      inputStyle={{
        // border: 'none',
        // outline: 'none',
        // fontSize: '16px',
      }}
      buttonStyle={{
        // border: 'none',
        // background: 'none',
        // cursor: 'pointer',
      }}
      dropdownStyle={{
        // border: '1px solid #ccc',
        // borderRadius: '4px',
        // padding: '8px',
        // fontSize: '16px',
      }}
      searchStyle={{
        // border: 'none',
        // outline: 'none',
        // fontSize: '16px',
      }}
      enableSearch={true}
      disableSearchIcon={true}
    />
  )
}

export default PhoneNumberInput
