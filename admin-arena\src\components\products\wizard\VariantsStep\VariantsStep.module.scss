@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
  }
}

.headerContent {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0 0 $spacing-2 0;
  }

  p {
    color: $gray-600;
    font-size: $font-size-sm;
    margin: 0;
    line-height: $line-height-relaxed;
  }
}

.variantsContainer {
  @include flex-column;
  gap: $spacing-4;
}

.variantCard {
  background: white;
  border: 1px solid $gray-200;
}

.variantHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-200;
}

.variantTitle {
  @include flex-start;
  align-items: center;
  gap: $spacing-2;
  color: $gray-700;

  svg {
    color: $primary-600;
  }

  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    margin: 0;
  }
}

.removeButton {
  color: $error-600;

  &:hover {
    background: $error-50;
    color: $error-700;
  }
}

.variantForm {
  @include flex-column;
  gap: $spacing-4;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.priceInput {
  position: relative;
}

.priceIcon {
  position: absolute;
  left: $spacing-3;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-400;
  font-size: $font-size-sm;
}

.priceInput input {
  padding-left: $spacing-8;
}

.switchGroup {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius;
}

.switchInfo {
  @include flex-column;
  gap: $spacing-1;
  flex: 1;
}

.helpText {
  font-size: $font-size-xs;
  color: $gray-500;
  line-height: $line-height-normal;
}

.attributesSection {
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius;

  h5 {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0 0 $spacing-1 0;
  }
}

.attributesList {
  @include flex-column;
  gap: $spacing-2;
  margin-top: $spacing-3;
}

.attributeItem {
  @include flex-between;
  align-items: center;
  padding: $spacing-2;
  background: white;
  border-radius: $border-radius-sm;
}

.attributeName {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

.summary {
  margin-top: $spacing-4;
}

.summaryCard {
  background: $primary-25;
  border: 1px solid $primary-200;

  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $primary-900;
    margin: 0 0 $spacing-3 0;
  }
}

.summaryStats {
  @include flex-column;
  gap: $spacing-2;
}

.stat {
  @include flex-between;
  align-items: center;
  padding: $spacing-2;
  background: white;
  border-radius: $border-radius-sm;
}

.statLabel {
  font-size: $font-size-sm;
  color: $gray-700;
}

.statValue {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $primary-700;
}
