from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from phonenumber_field.modelfields import PhoneNumberField


class Customer(models.Model):
    first_name = models.CharField(max_length=50, blank=True, null=True)
    last_name = models.CharField(max_length=50, blank=True, null=True)
    birth_date = models.DateField(null=True, blank=True)
    birth_date_change_count = models.PositiveIntegerField(default=0)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True)

    def __str__(self):
        return f'{self.first_name} {self.last_name}'

    def save(self, *args, **kwargs):
        if self.pk:
            old_instance = Customer.objects.get(pk=self.pk)
            if old_instance.birth_date != self.birth_date:
                if self.birth_date_change_count >= 2:
                    raise ValidationError("Birth date can only be changed twice.")
                self.birth_date_change_count += 1
        super().save(*args, **kwargs)


class Address(models.Model):
    full_name = models.CharField(max_length=100)
    street_name = models.CharField(max_length=255)
    address_line_1 = models.CharField(max_length=50, blank=True)
    address_line_2 = models.CharField(max_length=50, blank=True)
    postal_code = models.CharField(max_length=20)
    city_or_village = models.CharField(max_length=100)
    state_or_region = models.CharField(max_length=100)
    country = models.CharField(max_length=30)
    country_code = models.CharField(max_length=2, default='NO')
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='address', db_index=True)

    def __str__(self):
        return f'of {self.full_name}'

    class Meta:
        verbose_name_plural = "Addresses"
